-- DeepLedger Database Schema
-- Generated from Supabase project: iajycvybkkrwmhkompec

-- ============================================================================
-- EXTENSIONS
-- ============================================================================

-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pgvector for vector operations (used by Mastra memory system)
CREATE EXTENSION IF NOT EXISTS vector;

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Function to round monetary values to 2 decimal places
CREATE OR REPLACE FUNCTION round_money(amount NUMERIC)
RETURNS NUMERIC(15,2)
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
    -- Round to 2 decimal places using banker's rounding
    RETURN ROUND(amount, 2);
END;
$$;

-- Function to check if amounts are within tolerance
CREATE OR REPLACE FUNCTION amounts_within_tolerance(amount1 NUMERIC, amount2 NUMERIC, tolerance NUMERIC)
RETURNS BOOLEAN
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
    RETURN ABS(amount1 - amount2) <= tolerance;
END;
$$;

-- Function to validate decimal places
CREATE OR REPLACE FUNCTION has_valid_decimal_places(amount NUMERIC, max_places INTEGER)
RETURNS BOOLEAN
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
    -- Check if the number has at most the specified decimal places
    RETURN amount = ROUND(amount, max_places);
END;
$$;

-- Function to generate unique slugs
CREATE OR REPLACE FUNCTION generate_slug(input_text TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
  -- Convert to lowercase, replace spaces and special chars with hyphens
  -- Then add a random suffix for uniqueness
  RETURN LOWER(REGEXP_REPLACE(input_text, '[^a-zA-Z0-9]', '-', 'g')) || '-' || SUBSTR(MD5(random()::text), 1, 6);
END;
$$;

-- ============================================================================
-- MASTRA SCHEMA - AI FRAMEWORK TABLES
-- ============================================================================

-- Create mastra schema for AI framework isolation
CREATE SCHEMA IF NOT EXISTS mastra;

-- Mastra evaluation results
CREATE TABLE mastra.mastra_evals (
    input text NOT NULL,
    output text NOT NULL,
    result jsonb NOT NULL,
    agent_name text NOT NULL,
    metric_name text NOT NULL,
    instructions text NOT NULL,
    test_info jsonb,
    global_run_id text NOT NULL,
    run_id text NOT NULL,
    created_at timestamp without time zone NOT NULL,
    "createdAt" timestamp without time zone
);

-- Mastra conversation messages
CREATE TABLE mastra.mastra_messages (
    id text NOT NULL PRIMARY KEY,
    thread_id text NOT NULL,
    content text NOT NULL,
    role text NOT NULL,
    type text NOT NULL,
    "createdAt" timestamp without time zone NOT NULL
);

-- Mastra conversation threads
CREATE TABLE mastra.mastra_threads (
    id text NOT NULL PRIMARY KEY,
    "resourceId" text NOT NULL,
    title text NOT NULL,
    metadata text,
    "createdAt" timestamp without time zone NOT NULL,
    "updatedAt" timestamp without time zone NOT NULL
);

-- Mastra telemetry traces
CREATE TABLE mastra.mastra_traces (
    id text NOT NULL PRIMARY KEY,
    "parentSpanId" text,
    name text NOT NULL,
    "traceId" text NOT NULL,
    scope text NOT NULL,
    kind integer NOT NULL,
    attributes jsonb,
    status jsonb,
    events jsonb,
    links jsonb,
    other text,
    "startTime" bigint NOT NULL,
    "endTime" bigint NOT NULL,
    "createdAt" timestamp without time zone NOT NULL
);

-- Mastra workflow snapshots
CREATE TABLE mastra.mastra_workflow_snapshot (
    workflow_name text NOT NULL,
    run_id text NOT NULL,
    "resourceId" text,
    snapshot text NOT NULL,
    "createdAt" timestamp without time zone NOT NULL,
    "updatedAt" timestamp without time zone NOT NULL
);

-- Mastra memory messages with vector embeddings
CREATE SEQUENCE mastra.memory_messages_id_seq;
CREATE TABLE mastra.memory_messages (
    id integer NOT NULL DEFAULT nextval('mastra.memory_messages_id_seq'::regclass) PRIMARY KEY,
    vector_id text NOT NULL,
    embedding vector,
    metadata jsonb DEFAULT '{}'::jsonb
);

-- Mastra memory messages with 384-dimensional embeddings
CREATE SEQUENCE mastra.memory_messages_384_id_seq;
CREATE TABLE mastra.memory_messages_384 (
    id integer NOT NULL DEFAULT nextval('mastra.memory_messages_384_id_seq'::regclass) PRIMARY KEY,
    vector_id text NOT NULL,
    embedding vector(384),
    metadata jsonb DEFAULT '{}'::jsonb
);

-- ============================================================================
-- CORE TABLES - PUBLIC SCHEMA
-- ============================================================================

-- Organizations table
CREATE TABLE public.organizations (
    organization_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name character varying NOT NULL,
    slug character varying NOT NULL UNIQUE,
    logo text,
    settings jsonb DEFAULT '{}'::jsonb,
    fiscal_year_start date,
    fiscal_year_end date,
    accounting_method character varying(10) CHECK (accounting_method IN ('cash', 'accrual')),
    address text,
    tax_id character varying(50),
    industry character varying(100),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    created_by uuid,
    updated_by uuid
);

-- Users table
CREATE TABLE public.users (
    user_id uuid PRIMARY KEY NOT NULL,
    first_name text,
    last_name text,
    avatar_url text,
    phone text,
    timezone text DEFAULT 'UTC'::text,
    preferences jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Organization users junction table
CREATE TABLE public.organization_users (
    organization_user_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    user_id uuid NOT NULL,
    role character varying NOT NULL DEFAULT 'member'::character varying,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    UNIQUE(organization_id, user_id)
);

-- Organization invitations table
CREATE TABLE public.organization_invitations (
    invitation_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    email character varying(255) NOT NULL,
    role character varying(20) NOT NULL,
    status character varying(20) NOT NULL DEFAULT 'pending'::character varying,
    message text,
    invited_by uuid NOT NULL,
    invited_at timestamp with time zone DEFAULT now(),
    expires_at timestamp with time zone DEFAULT (now() + '7 days'::interval),
    responded_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    verification_token character varying(255)
);

-- Chart of Accounts
CREATE TABLE public.accounts (
    account_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    account_code integer NOT NULL,
    account_name character varying(100) NOT NULL,
    account_type character varying(20) NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    parent_id uuid REFERENCES public.accounts(account_id),
    account_level integer NOT NULL DEFAULT 1,
    is_summary boolean NOT NULL DEFAULT false,
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id),
    account_type_detail character varying(50) NOT NULL,
    UNIQUE(organization_id, account_code)
);

-- Classes (for tracking/categorization)
CREATE TABLE public.classes (
    class_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    class_name character varying NOT NULL,
    description text,
    parent_id uuid REFERENCES public.classes(class_id),
    is_active boolean NOT NULL DEFAULT true,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Projects (for project tracking/categorization)
CREATE TABLE public.projects (
    project_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    project_name character varying NOT NULL,
    description text,
    parent_id uuid REFERENCES public.projects(project_id),
    is_active boolean NOT NULL DEFAULT true,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Customers
CREATE TABLE public.customers (
    customer_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_name character varying(100) NOT NULL,
    customer_email character varying(100),
    customer_phone character varying(20),
    customer_address text,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Vendors
CREATE TABLE public.vendors (
    vendor_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_name character varying(100) NOT NULL,
    vendor_email character varying(100),
    vendor_phone character varying(20),
    vendor_address text,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Email Templates
CREATE TABLE public.email_templates (
    template_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name character varying(100) NOT NULL,
    subject text NOT NULL,
    html_content text NOT NULL,
    text_content text,
    variables jsonb DEFAULT '{}'::jsonb,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Products and Services (Replacement for Items)
-- Designed for solo and small businesses with simplified inventory tracking
CREATE TABLE public.products_and_services (
    product_service_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    product_service_name character varying NOT NULL,
    description text,
    product_service_type character varying NOT NULL CHECK (product_service_type IN ('product', 'service')),
    sku character varying,
    barcode character varying,

    -- Pricing
    sales_price numeric(15,2),
    purchase_price numeric(15,2),

    -- Account Mappings
    revenue_account_id uuid NOT NULL REFERENCES public.accounts(account_id),
    expense_account_id uuid REFERENCES public.accounts(account_id), -- For products (COGS), optional for services

    -- Simple Quantity Tracking (no complex inventory management)
    purchase_qty numeric(15,3) DEFAULT 0, -- Total purchased
    sale_qty numeric(15,3) DEFAULT 0,     -- Total sold
    current_qty numeric(15,3) DEFAULT 0,  -- Current on hand (purchase_qty - sale_qty)

    -- Business Intelligence Fields
    total_revenue numeric(15,2) DEFAULT 0,     -- Total revenue generated
    total_cost numeric(15,2) DEFAULT 0,        -- Total cost of goods sold
    total_profit numeric(15,2) DEFAULT 0,      -- Total profit (revenue - cost)
    last_sale_date date,                       -- Last time this was sold
    last_purchase_date date,                   -- Last time this was purchased
    last_sale_price numeric(15,2),            -- Last sale price
    last_purchase_price numeric(15,2),        -- Last purchase price

    -- Status
    is_active boolean DEFAULT true,

    -- Standard fields
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Tax Rates
CREATE TABLE public.tax_rates (
    tax_rate_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    tax_rate_name character varying NOT NULL,
    tax_rate_percentage numeric(15,4) NOT NULL,
    tax_rate_type character varying NOT NULL,
    is_compound boolean NOT NULL DEFAULT false,
    is_recoverable boolean NOT NULL DEFAULT true,
    tax_account_id uuid REFERENCES public.accounts(account_id),
    is_active boolean NOT NULL DEFAULT true,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Tax Groups
CREATE TABLE public.tax_groups (
    tax_group_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    tax_group_name character varying NOT NULL,
    description text,
    is_active boolean NOT NULL DEFAULT true,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Tax Group Items (junction table)
CREATE TABLE public.tax_group_items (
    tax_group_item_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    tax_group_id uuid NOT NULL REFERENCES public.tax_groups(tax_group_id),
    tax_rate_id uuid NOT NULL REFERENCES public.tax_rates(tax_rate_id),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id),
    UNIQUE(tax_group_id, tax_rate_id, organization_id)
);

-- Transactions (main transaction table)
CREATE TABLE public.transactions (
    transaction_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_type character varying(50) NOT NULL,
    transaction_date date NOT NULL,
    reference_number character varying(50),
    description text,
    customer_id uuid REFERENCES public.customers(customer_id),
    vendor_id uuid REFERENCES public.vendors(vendor_id),
    total_amount numeric(15,2) NOT NULL,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    due_date date,
    status character varying,
    payment_terms character varying,
    tax_amount numeric(15,2) NOT NULL DEFAULT 0,
    subtotal_amount numeric(15,2) NOT NULL DEFAULT 0,
    payment_status character varying(20) DEFAULT 'unpaid'::character varying,
    paid_amount numeric(15,2) NOT NULL DEFAULT 0,
    outstanding_amount numeric(15,2),
    check_number character varying(50),
    document_number character varying(50),
    payment_method character varying(50),
    original_transaction_id uuid REFERENCES public.transactions(transaction_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Add constraints for transactions table
ALTER TABLE public.transactions ADD CONSTRAINT transactions_transaction_type_check
CHECK (transaction_type IN (
    'sales_invoice',
    'sales_receipt',
    'customer_payment',
    'sales_return',
    'customer_cash_refund',
    'bill',
    'expense',
    'vendor_payment',
    'purchase_return',
    'vendor_cash_refund',
    'journal_entry'
));

ALTER TABLE public.transactions ADD CONSTRAINT chk_payment_status
CHECK (payment_status IN ('unpaid', 'partial', 'paid', 'overpaid', 'n/a'));

-- Add precision constraints for monetary fields
ALTER TABLE public.transactions ADD CONSTRAINT chk_total_amount_precision
CHECK (has_valid_decimal_places(total_amount, 2));

ALTER TABLE public.transactions ADD CONSTRAINT chk_tax_amount_precision
CHECK (tax_amount IS NULL OR has_valid_decimal_places(tax_amount, 2));

ALTER TABLE public.transactions ADD CONSTRAINT chk_subtotal_amount_precision
CHECK (subtotal_amount IS NULL OR has_valid_decimal_places(subtotal_amount, 2));

ALTER TABLE public.transactions ADD CONSTRAINT chk_paid_amount_precision
CHECK (paid_amount IS NULL OR has_valid_decimal_places(paid_amount, 2));

ALTER TABLE public.transactions ADD CONSTRAINT chk_outstanding_amount_precision
CHECK (outstanding_amount IS NULL OR has_valid_decimal_places(outstanding_amount, 2));

-- Transaction Lines (double-entry bookkeeping with product/service support)
CREATE TABLE public.transaction_lines (
    transaction_line_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id uuid NOT NULL REFERENCES public.transactions(transaction_id),
    account_id uuid NOT NULL REFERENCES public.accounts(account_id),
    description text,
    debit_amount numeric(15,2) NOT NULL DEFAULT 0,
    credit_amount numeric(15,2) NOT NULL DEFAULT 0,

    -- Product/Service Line Item Fields (eliminates need for invoice_items and bill_items)
    product_service_id uuid REFERENCES public.products_and_services(product_service_id),
    quantity numeric(15,3),
    unit_price numeric(15,2),
    discount_percentage numeric(5,2) DEFAULT 0,
    discount_amount numeric(15,2) DEFAULT 0,
    tax_rate_id uuid REFERENCES public.tax_rates(tax_rate_id),
    tax_rate numeric(15,4) DEFAULT 0,

    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    class_id uuid REFERENCES public.classes(class_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id),
    project_id uuid REFERENCES public.projects(project_id)
);



-- Invoice Payments
CREATE TABLE public.invoice_payments (
    invoice_payment_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id uuid NOT NULL REFERENCES public.transactions(transaction_id),
    payment_transaction_id uuid NOT NULL REFERENCES public.transactions(transaction_id),
    amount numeric(15,2) NOT NULL,
    payment_date date NOT NULL,
    payment_method character varying,
    reference character varying,
    check_number character varying(50),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid,
    updated_by uuid
);

-- Bill Payments
CREATE TABLE public.bill_payments (
    bill_payment_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id uuid NOT NULL REFERENCES public.transactions(transaction_id),
    payment_transaction_id uuid NOT NULL REFERENCES public.transactions(transaction_id),
    amount numeric(15,2) NOT NULL,
    payment_date date NOT NULL,
    payment_method character varying,
    reference character varying,
    check_number character varying(50),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);



-- Bank Accounts
CREATE TABLE public.bank_accounts (
    bank_account_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    bank_account_name character varying NOT NULL,
    account_number character varying,
    institution character varying,
    account_id uuid NOT NULL REFERENCES public.accounts(account_id),
    is_active boolean DEFAULT true,
    last_reconciled_date date,
    last_reconciled_balance numeric(15,2),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Bank Transactions
CREATE TABLE public.bank_transactions (
    bank_transaction_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    bank_account_id uuid NOT NULL REFERENCES public.bank_accounts(bank_account_id),
    transaction_date date NOT NULL,
    description text,
    amount numeric(15,2) NOT NULL,
    reference character varying,
    is_reconciled boolean NOT NULL DEFAULT false,
    matched_transaction_id uuid REFERENCES public.transactions(transaction_id),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Reconciliations
CREATE TABLE public.reconciliations (
    reconciliation_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    bank_account_id uuid NOT NULL REFERENCES public.bank_accounts(bank_account_id),
    start_date date NOT NULL,
    end_date date NOT NULL,
    starting_balance numeric(15,2) NOT NULL,
    ending_balance numeric(15,2) NOT NULL,
    is_completed boolean NOT NULL DEFAULT false,
    completed_date timestamp with time zone,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id),
    created_by uuid REFERENCES public.users(user_id),
    updated_by uuid REFERENCES public.users(user_id)
);

-- Reconciliation Items
CREATE TABLE public.reconciliation_items (
    reconciliation_item_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    reconciliation_id uuid NOT NULL REFERENCES public.reconciliations(reconciliation_id),
    transaction_id uuid REFERENCES public.transactions(transaction_id),
    bank_transaction_id uuid REFERENCES public.bank_transactions(bank_transaction_id),
    is_matched boolean NOT NULL DEFAULT false,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL REFERENCES public.organizations(organization_id)
);

-- ============================================================================
-- VIEWS
-- ============================================================================

-- Account Balances View
CREATE VIEW public.account_balances AS
SELECT
    tl.account_id,
    tl.organization_id,
    a.account_name,
    a.account_code,
    a.account_type,
    round_money(COALESCE(SUM(tl.debit_amount), 0)) AS total_debits,
    round_money(COALESCE(SUM(tl.credit_amount), 0)) AS total_credits,
    round_money(COALESCE(SUM(tl.debit_amount), 0) - COALESCE(SUM(tl.credit_amount), 0)) AS balance,
    COUNT(*) AS transaction_count,
    MAX(tl.created_at) AS last_transaction_date,
    CURRENT_TIMESTAMP AS last_updated
FROM transaction_lines tl
JOIN accounts a ON tl.account_id = a.account_id
WHERE a.is_active = true
GROUP BY tl.account_id, tl.organization_id, a.account_name, a.account_code, a.account_type;

-- Transaction Payment Summary View
CREATE VIEW public.transaction_payment_summary AS
SELECT
    t.transaction_id,
    t.transaction_type,
    t.transaction_date,
    t.reference_number,
    t.document_number,
    t.check_number,
    t.description,
    CASE
        WHEN t.customer_id IS NOT NULL THEN c.customer_name
        WHEN t.vendor_id IS NOT NULL THEN v.vendor_name
        ELSE NULL
    END AS party_name,
    t.total_amount,
    t.paid_amount,
    t.outstanding_amount,
    t.payment_status,
    t.status AS approval_status,
    t.due_date,
    CASE
        WHEN t.due_date IS NOT NULL AND t.due_date < CURRENT_DATE AND t.outstanding_amount > 0 THEN true
        ELSE false
    END AS is_overdue,
    CASE
        WHEN t.due_date IS NOT NULL AND t.outstanding_amount > 0 THEN (CURRENT_DATE - t.due_date)
        ELSE NULL
    END AS days_overdue,
    t.created_at,
    t.updated_at,
    t.organization_id
FROM transactions t
LEFT JOIN customers c ON t.customer_id = c.customer_id
LEFT JOIN vendors v ON t.vendor_id = v.vendor_id
WHERE t.transaction_type IN ('sales_invoice', 'bill', 'sales_receipt', 'customer_payment', 'vendor_payment', 'expense', 'journal_entry', 'sales_return', 'customer_cash_refund', 'purchase_return', 'vendor_cash_refund');

-- ============================================================================
-- BUSINESS FUNCTIONS
-- ============================================================================

-- Function to generate document numbers
CREATE OR REPLACE FUNCTION generate_document_number(p_transaction_type VARCHAR, p_organization_id UUID)
RETURNS VARCHAR(50)
LANGUAGE plpgsql
AS $$
DECLARE
    prefix VARCHAR(10);
    next_number INTEGER;
    result_document_number VARCHAR(50);
BEGIN
    -- Determine prefix based on transaction type
    CASE p_transaction_type
        WHEN 'sales_invoice' THEN prefix := 'INV';
        WHEN 'sales_receipt' THEN prefix := 'REC';
        WHEN 'bill' THEN prefix := 'BILL';
        WHEN 'expense' THEN prefix := 'EXP';
        WHEN 'customer_payment' THEN prefix := 'PMT';
        WHEN 'vendor_payment' THEN prefix := 'VPMT';
        WHEN 'journal_entry' THEN prefix := 'JE';
        WHEN 'sales_return' THEN prefix := 'CR';
        WHEN 'customer_cash_refund' THEN prefix := 'CRF';
        WHEN 'purchase_return' THEN prefix := 'DR';
        WHEN 'vendor_cash_refund' THEN prefix := 'VRF';
        ELSE prefix := 'DOC';
    END CASE;

    -- Get the next number for this prefix and organization
    SELECT COALESCE(MAX(
        CAST(
            SUBSTRING(
                t.document_number FROM LENGTH(prefix) + 2
            ) AS INTEGER
        )
    ), 0) + 1
    INTO next_number
    FROM transactions t
    WHERE t.organization_id = p_organization_id
    AND t.document_number LIKE prefix || '-%';

    -- Format the document number
    result_document_number := prefix || '-' || LPAD(next_number::TEXT, 3, '0');

    RETURN result_document_number;
END;
$$;

-- Function to get trial balance
CREATE OR REPLACE FUNCTION get_trial_balance(p_organization_id UUID, p_as_of_date DATE, p_include_zero_balances BOOLEAN DEFAULT false)
RETURNS TABLE(
    account_id UUID,
    account_code INTEGER,
    account_name VARCHAR,
    account_type VARCHAR,
    balance NUMERIC(15,2),
    debit_balance NUMERIC(15,2),
    credit_balance NUMERIC(15,2),
    transaction_count BIGINT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.account_id,
        a.account_code,
        a.account_name,
        a.account_type,
        round_money(COALESCE(SUM(tl.debit_amount), 0) - COALESCE(SUM(tl.credit_amount), 0)) as balance,
        CASE
            WHEN round_money(COALESCE(SUM(tl.debit_amount), 0) - COALESCE(SUM(tl.credit_amount), 0)) > 0
            THEN round_money(COALESCE(SUM(tl.debit_amount), 0) - COALESCE(SUM(tl.credit_amount), 0))
            ELSE 0.00
        END as debit_balance,
        CASE
            WHEN round_money(COALESCE(SUM(tl.debit_amount), 0) - COALESCE(SUM(tl.credit_amount), 0)) < 0
            THEN round_money(ABS(COALESCE(SUM(tl.debit_amount), 0) - COALESCE(SUM(tl.credit_amount), 0)))
            ELSE 0.00
        END as credit_balance,
        COUNT(tl.transaction_line_id) as transaction_count
    FROM accounts a
    LEFT JOIN transaction_lines tl ON a.account_id = tl.account_id
        AND tl.organization_id = p_organization_id
    LEFT JOIN transactions t ON tl.transaction_id = t.transaction_id
        AND t.transaction_date <= p_as_of_date
    WHERE a.organization_id = p_organization_id
        AND a.is_active = true
    GROUP BY a.account_id, a.account_code, a.account_name, a.account_type
    HAVING (p_include_zero_balances = true OR
            ABS(round_money(COALESCE(SUM(tl.debit_amount), 0) - COALESCE(SUM(tl.credit_amount), 0))) > 0.00)
    ORDER BY a.account_code;
END;
$$;

-- Function to update payment status
CREATE OR REPLACE FUNCTION update_payment_status()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    target_transaction_id UUID;
    total_paid NUMERIC(15,2);
    transaction_total NUMERIC(15,2);
    transaction_type_val VARCHAR;
BEGIN
    -- Get the transaction ID being affected
    IF TG_OP = 'DELETE' THEN
        target_transaction_id := OLD.transaction_id;
    ELSE
        target_transaction_id := NEW.transaction_id;
    END IF;

    -- Get transaction details
    SELECT total_amount, transaction_type
    INTO transaction_total, transaction_type_val
    FROM transactions
    WHERE transaction_id = target_transaction_id;

    -- Only update payment status for transactions that can have payments
    IF transaction_type_val NOT IN ('sales_invoice', 'bill', 'sales_return', 'purchase_return') THEN
        RETURN COALESCE(NEW, OLD);
    END IF;

    -- Calculate total paid amount from both payment tables
    SELECT COALESCE(SUM(amount), 0)
    INTO total_paid
    FROM (
        SELECT amount FROM invoice_payments WHERE transaction_id = target_transaction_id
        UNION ALL
        SELECT amount FROM bill_payments WHERE transaction_id = target_transaction_id
    ) all_payments;

    -- Update the transaction with new payment information
    UPDATE transactions
    SET
        paid_amount = total_paid,
        outstanding_amount = transaction_total - total_paid,
        payment_status = CASE
            WHEN total_paid = 0 THEN 'unpaid'
            WHEN total_paid >= transaction_total THEN 'paid'
            WHEN total_paid > transaction_total THEN 'overpaid'
            ELSE 'partial'
        END,
        updated_at = now()
    WHERE transaction_id = target_transaction_id;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- Function to validate transaction balance
CREATE OR REPLACE FUNCTION validate_transaction_balance()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    total_debits NUMERIC(15,2);
    total_credits NUMERIC(15,2);
    balance_difference NUMERIC(15,2);
BEGIN
    -- Calculate totals for the transaction with proper rounding
    SELECT
        round_money(COALESCE(SUM(debit_amount), 0)),
        round_money(COALESCE(SUM(credit_amount), 0))
    INTO total_debits, total_credits
    FROM transaction_lines
    WHERE transaction_id = COALESCE(NEW.transaction_id, OLD.transaction_id);

    -- Calculate the difference
    balance_difference := ABS(total_debits - total_credits);

    -- CRITICAL FIX: Enforce exact balance (0.00 tolerance)
    IF balance_difference > 0.00 THEN
        RAISE EXCEPTION 'Transaction must balance exactly: Debits (%) != Credits (%). Difference: %',
                       total_debits, total_credits, balance_difference;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- Function to round monetary values before storing
CREATE OR REPLACE FUNCTION round_monetary_values()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Round all monetary values before storing
    IF TG_TABLE_NAME = 'transaction_lines' THEN
        NEW.debit_amount := round_money(COALESCE(NEW.debit_amount, 0));
        NEW.credit_amount := round_money(COALESCE(NEW.credit_amount, 0));
        NEW.unit_price := round_money(COALESCE(NEW.unit_price, 0));
        NEW.discount_amount := round_money(COALESCE(NEW.discount_amount, 0));


    ELSIF TG_TABLE_NAME = 'transactions' THEN
        NEW.total_amount := round_money(COALESCE(NEW.total_amount, 0));
        NEW.tax_amount := round_money(COALESCE(NEW.tax_amount, 0));
        NEW.subtotal_amount := round_money(COALESCE(NEW.subtotal_amount, 0));
    ELSIF TG_TABLE_NAME = 'products_and_services' THEN
        NEW.sales_price := round_money(COALESCE(NEW.sales_price, 0));
        NEW.purchase_price := round_money(COALESCE(NEW.purchase_price, 0));
        NEW.total_revenue := round_money(COALESCE(NEW.total_revenue, 0));
        NEW.total_cost := round_money(COALESCE(NEW.total_cost, 0));
        NEW.total_profit := round_money(COALESCE(NEW.total_profit, 0));
        NEW.last_sale_price := round_money(COALESCE(NEW.last_sale_price, 0));
        NEW.last_purchase_price := round_money(COALESCE(NEW.last_purchase_price, 0));
    END IF;

    RETURN NEW;
END;
$$;



-- Function to update product/service statistics
CREATE OR REPLACE FUNCTION update_product_service_stats()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    ps_record RECORD;
    line_record RECORD;
BEGIN
    -- Get the product/service record from transaction lines
    FOR line_record IN
        SELECT DISTINCT tl.product_service_id
        FROM transaction_lines tl
        WHERE tl.transaction_id = COALESCE(NEW.transaction_id, OLD.transaction_id)
        AND tl.product_service_id IS NOT NULL
    LOOP
        -- Get current product/service record
        SELECT * INTO ps_record
        FROM products_and_services
        WHERE product_service_id = line_record.product_service_id;

        IF ps_record.product_service_id IS NOT NULL THEN
            -- Recalculate statistics from transaction_lines
            UPDATE products_and_services SET
                -- Calculate total quantities
                sale_qty = COALESCE((
                    SELECT SUM(
                        CASE
                            WHEN t.transaction_type IN ('sales_invoice', 'sales_receipt') THEN tl.quantity
                            WHEN t.transaction_type = 'sales_return' THEN -tl.quantity
                            ELSE 0
                        END
                    )
                    FROM transaction_lines tl
                    JOIN transactions t ON tl.transaction_id = t.transaction_id
                    WHERE tl.product_service_id = ps_record.product_service_id
                    AND t.transaction_type IN ('sales_invoice', 'sales_receipt', 'sales_return')
                    AND tl.quantity > 0
                ), 0),

                purchase_qty = COALESCE((
                    SELECT SUM(
                        CASE
                            WHEN t.transaction_type IN ('bill', 'expense') THEN tl.quantity
                            WHEN t.transaction_type = 'purchase_return' THEN -tl.quantity
                            ELSE 0
                        END
                    )
                    FROM transaction_lines tl
                    JOIN transactions t ON tl.transaction_id = t.transaction_id
                    WHERE tl.product_service_id = ps_record.product_service_id
                    AND t.transaction_type IN ('bill', 'expense', 'purchase_return')
                    AND tl.quantity > 0
                ), 0),

                -- Calculate financial totals
                total_revenue = COALESCE((
                    SELECT SUM(tl.credit_amount)
                    FROM transaction_lines tl
                    JOIN transactions t ON tl.transaction_id = t.transaction_id
                    WHERE tl.product_service_id = ps_record.product_service_id
                    AND tl.account_id = ps_record.revenue_account_id
                ), 0),

                total_cost = COALESCE((
                    SELECT SUM(tl.debit_amount)
                    FROM transaction_lines tl
                    JOIN transactions t ON tl.transaction_id = t.transaction_id
                    WHERE tl.product_service_id = ps_record.product_service_id
                    AND tl.account_id = ps_record.expense_account_id
                ), 0),

                -- Calculate last sale info
                last_sale_date = (
                    SELECT MAX(t.transaction_date)
                    FROM transaction_lines tl
                    JOIN transactions t ON tl.transaction_id = t.transaction_id
                    WHERE tl.product_service_id = ps_record.product_service_id
                    AND t.transaction_type IN ('sales_invoice', 'sales_receipt', 'sales_return')
                ),

                last_sale_price = (
                    SELECT tl.unit_price
                    FROM transaction_lines tl
                    JOIN transactions t ON tl.transaction_id = t.transaction_id
                    WHERE tl.product_service_id = ps_record.product_service_id
                    AND t.transaction_type IN ('sales_invoice', 'sales_receipt', 'sales_return')
                    ORDER BY t.transaction_date DESC, t.created_at DESC
                    LIMIT 1
                ),

                -- Calculate last purchase info
                last_purchase_date = (
                    SELECT MAX(t.transaction_date)
                    FROM transaction_lines tl
                    JOIN transactions t ON tl.transaction_id = t.transaction_id
                    WHERE tl.product_service_id = ps_record.product_service_id
                    AND t.transaction_type IN ('bill', 'expense', 'purchase_return')
                ),

                last_purchase_price = (
                    SELECT tl.unit_price
                    FROM transaction_lines tl
                    JOIN transactions t ON tl.transaction_id = t.transaction_id
                    WHERE tl.product_service_id = ps_record.product_service_id
                    AND t.transaction_type IN ('bill', 'expense', 'purchase_return')
                    ORDER BY t.transaction_date DESC, t.created_at DESC
                    LIMIT 1
                ),

                updated_at = now()
            WHERE product_service_id = ps_record.product_service_id;

            -- Update current quantity and profit
            UPDATE products_and_services SET
                current_qty = purchase_qty - sale_qty,
                total_profit = total_revenue - total_cost
            WHERE product_service_id = ps_record.product_service_id;
        END IF;
    END LOOP;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;

-- ============================================================================
-- TRIGGERS
-- ============================================================================

-- Triggers for updating updated_at timestamps
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_organization_users_updated_at BEFORE UPDATE ON organization_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_organization_invitations_updated_at BEFORE UPDATE ON organization_invitations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_classes_updated_at BEFORE UPDATE ON classes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vendors_updated_at BEFORE UPDATE ON vendors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_templates_updated_at BEFORE UPDATE ON email_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_and_services_updated_at BEFORE UPDATE ON products_and_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tax_rates_updated_at BEFORE UPDATE ON tax_rates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tax_groups_updated_at BEFORE UPDATE ON tax_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tax_group_items_updated_at BEFORE UPDATE ON tax_group_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transaction_lines_updated_at BEFORE UPDATE ON transaction_lines FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_invoice_payments_updated_at BEFORE UPDATE ON invoice_payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bill_payments_updated_at BEFORE UPDATE ON bill_payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bank_accounts_updated_at BEFORE UPDATE ON bank_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bank_transactions_updated_at BEFORE UPDATE ON bank_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reconciliations_updated_at BEFORE UPDATE ON reconciliations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reconciliation_items_updated_at BEFORE UPDATE ON reconciliation_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Triggers for monetary value rounding
CREATE TRIGGER round_transaction_lines_amounts BEFORE INSERT OR UPDATE ON transaction_lines FOR EACH ROW EXECUTE FUNCTION round_monetary_values();
CREATE TRIGGER round_transactions_amounts BEFORE INSERT OR UPDATE ON transactions FOR EACH ROW EXECUTE FUNCTION round_monetary_values();
CREATE TRIGGER round_products_services_amounts BEFORE INSERT OR UPDATE ON products_and_services FOR EACH ROW EXECUTE FUNCTION round_monetary_values();

-- Triggers for product/service tracking
CREATE TRIGGER update_product_service_stats_on_line_change AFTER INSERT OR UPDATE OR DELETE ON transaction_lines FOR EACH ROW EXECUTE FUNCTION update_product_service_stats();

-- Triggers for transaction balance validation
CREATE TRIGGER validate_transaction_lines_balance AFTER INSERT OR UPDATE OR DELETE ON transaction_lines FOR EACH ROW EXECUTE FUNCTION validate_transaction_balance();

-- Triggers for payment status updates
CREATE TRIGGER update_payment_status_on_invoice_payment AFTER INSERT OR UPDATE OR DELETE ON invoice_payments FOR EACH ROW EXECUTE FUNCTION update_payment_status();
CREATE TRIGGER update_payment_status_on_bill_payment AFTER INSERT OR UPDATE OR DELETE ON bill_payments FOR EACH ROW EXECUTE FUNCTION update_payment_status();

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Organization-based indexes for multi-tenancy
CREATE INDEX idx_accounts_organization_id ON accounts(organization_id);
CREATE INDEX idx_transactions_organization_id ON transactions(organization_id);
CREATE INDEX idx_transaction_lines_organization_id ON transaction_lines(organization_id);
CREATE INDEX idx_customers_organization_id ON customers(organization_id);
CREATE INDEX idx_vendors_organization_id ON vendors(organization_id);
CREATE INDEX idx_products_services_organization_id ON products_and_services(organization_id);

-- Performance indexes for common queries
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_payment_status ON transactions(payment_status);
CREATE INDEX idx_transaction_lines_account_id ON transaction_lines(account_id);
CREATE INDEX idx_transaction_lines_product_service ON transaction_lines(product_service_id);
CREATE INDEX idx_transaction_lines_tax_rate ON transaction_lines(tax_rate_id);
CREATE INDEX idx_accounts_code ON accounts(account_code);
CREATE INDEX idx_accounts_type ON accounts(account_type);

-- Indexes for products_and_services performance
CREATE INDEX idx_products_services_type ON products_and_services(product_service_type);
CREATE INDEX idx_products_services_active ON products_and_services(is_active);
CREATE INDEX idx_products_services_revenue_account ON products_and_services(revenue_account_id);
CREATE INDEX idx_products_services_expense_account ON products_and_services(expense_account_id);

-- Index for original transaction relationships (returns, refunds, etc.)
CREATE INDEX idx_transactions_original_transaction_id ON transactions(original_transaction_id) WHERE original_transaction_id IS NOT NULL;

-- ============================================================================
-- BUSINESS INTELLIGENCE VIEWS
-- ============================================================================

-- View for product/service performance analysis
CREATE VIEW public.product_service_performance AS
SELECT
    ps.*,
    CASE
        WHEN ps.sale_qty > 0 THEN ps.total_revenue / ps.sale_qty
        ELSE 0
    END AS avg_sale_price,
    CASE
        WHEN ps.purchase_qty > 0 THEN ps.total_cost / ps.purchase_qty
        ELSE 0
    END AS avg_purchase_price,
    CASE
        WHEN ps.total_revenue > 0 THEN (ps.total_profit / ps.total_revenue) * 100
        ELSE 0
    END AS profit_margin_percentage,
    ra.account_name AS revenue_account_name,
    ea.account_name AS expense_account_name
FROM products_and_services ps
LEFT JOIN accounts ra ON ps.revenue_account_id = ra.account_id
LEFT JOIN accounts ea ON ps.expense_account_id = ea.account_id
WHERE ps.is_active = true;

-- View for detailed transaction analysis with product/service information
CREATE VIEW public.transaction_line_details AS
SELECT
    tl.*,
    t.transaction_type,
    t.transaction_date,
    t.reference_number,
    t.document_number,
    ps.product_service_name,
    ps.product_service_type,
    ps.sku,
    a.account_name,
    a.account_code,
    a.account_type,
    c.customer_name,
    v.vendor_name,
    tr.tax_rate_name,
    tr.tax_rate_percentage,
    cl.class_name,
    pr.project_name
FROM transaction_lines tl
JOIN transactions t ON tl.transaction_id = t.transaction_id
JOIN accounts a ON tl.account_id = a.account_id
LEFT JOIN products_and_services ps ON tl.product_service_id = ps.product_service_id
LEFT JOIN customers c ON t.customer_id = c.customer_id
LEFT JOIN vendors v ON t.vendor_id = v.vendor_id
LEFT JOIN tax_rates tr ON tl.tax_rate_id = tr.tax_rate_id
LEFT JOIN classes cl ON tl.class_id = cl.class_id
LEFT JOIN projects pr ON tl.project_id = pr.project_id;

-- View for sales analysis by product/service
CREATE VIEW public.sales_by_product_service AS
SELECT
    ps.product_service_id,
    ps.product_service_name,
    ps.product_service_type,
    ps.sku,
    ps.sales_price,
    ps.purchase_price,
    COUNT(tl.transaction_line_id) AS total_sales_transactions,
    SUM(tl.quantity) AS total_quantity_sold,
    SUM(tl.credit_amount) AS total_revenue,
    AVG(tl.unit_price) AS average_sale_price,
    MIN(tl.unit_price) AS min_sale_price,
    MAX(tl.unit_price) AS max_sale_price,
    MAX(t.transaction_date) AS last_sale_date
FROM products_and_services ps
LEFT JOIN transaction_lines tl ON ps.product_service_id = tl.product_service_id
LEFT JOIN transactions t ON tl.transaction_id = t.transaction_id
WHERE t.transaction_type IN ('sales_invoice', 'sales_receipt') OR t.transaction_type IS NULL
GROUP BY ps.product_service_id, ps.product_service_name, ps.product_service_type, ps.sku, ps.sales_price, ps.purchase_price
ORDER BY total_revenue DESC NULLS LAST;

-- View for purchase analysis by product/service
CREATE VIEW public.purchases_by_product_service AS
SELECT
    ps.product_service_id,
    ps.product_service_name,
    ps.product_service_type,
    ps.sku,
    ps.sales_price,
    ps.purchase_price,
    COUNT(tl.transaction_line_id) AS total_purchase_transactions,
    SUM(tl.quantity) AS total_quantity_purchased,
    SUM(tl.debit_amount) AS total_cost,
    AVG(tl.unit_price) AS average_purchase_price,
    MIN(tl.unit_price) AS min_purchase_price,
    MAX(tl.unit_price) AS max_purchase_price,
    MAX(t.transaction_date) AS last_purchase_date
FROM products_and_services ps
LEFT JOIN transaction_lines tl ON ps.product_service_id = tl.product_service_id
LEFT JOIN transactions t ON tl.transaction_id = t.transaction_id
WHERE t.transaction_type IN ('bill', 'expense') OR t.transaction_type IS NULL
GROUP BY ps.product_service_id, ps.product_service_name, ps.product_service_type, ps.sku, ps.sales_price, ps.purchase_price
ORDER BY total_cost DESC NULLS LAST;

-- View for inventory status (simple quantity tracking)
CREATE VIEW public.inventory_status AS
SELECT
    ps.product_service_id,
    ps.product_service_name,
    ps.product_service_type,
    ps.sku,
    ps.current_qty,
    ps.sales_price,
    ps.purchase_price,
    ps.current_qty * ps.purchase_price AS inventory_value,
    CASE
        WHEN ps.product_service_type = 'product' AND ps.current_qty <= 0 THEN 'Out of Stock'
        WHEN ps.product_service_type = 'product' AND ps.current_qty <= 5 THEN 'Low Stock'
        WHEN ps.product_service_type = 'product' THEN 'In Stock'
        ELSE 'Service'
    END AS stock_status
FROM products_and_services ps
WHERE ps.is_active = true
ORDER BY ps.product_service_type, stock_status, ps.product_service_name;

-- ============================================================================
-- COMMENTS
-- ============================================================================

COMMENT ON SCHEMA public IS 'DeepLedger accounting system schema with complete double-entry bookkeeping support';
COMMENT ON TABLE organizations IS 'Multi-tenant organizations for data isolation';
COMMENT ON TABLE users IS 'User profiles linked to auth.users';
COMMENT ON TABLE accounts IS 'Chart of accounts for double-entry bookkeeping';
COMMENT ON TABLE transactions IS 'Main transaction table supporting all transaction types';
COMMENT ON TABLE transaction_lines IS 'Enhanced double-entry transaction lines with product/service support, eliminating need for separate invoice_items and bill_items tables';
COMMENT ON TABLE products_and_services IS 'Simplified products and services tracking for solo and small businesses with automatic quantity and profit tracking';
COMMENT ON VIEW account_balances IS 'Real-time account balance calculations';
COMMENT ON VIEW transaction_payment_summary IS 'Payment status summary for invoices and bills';
COMMENT ON VIEW product_service_performance IS 'Complete performance analysis for products and services including profit margins';
COMMENT ON VIEW transaction_line_details IS 'Detailed transaction analysis with product/service information';
COMMENT ON VIEW sales_by_product_service IS 'Sales analytics by product/service';
COMMENT ON VIEW purchases_by_product_service IS 'Purchase analytics by product/service';
COMMENT ON VIEW inventory_status IS 'Simple inventory tracking with stock status';
COMMENT ON FUNCTION round_money(NUMERIC) IS 'Ensures consistent 2-decimal place rounding for all monetary values';
COMMENT ON FUNCTION generate_document_number(VARCHAR, UUID) IS 'Generates sequential document numbers by transaction type';
COMMENT ON FUNCTION get_trial_balance(UUID, DATE, BOOLEAN) IS 'Calculates trial balance for a specific organization and date';
COMMENT ON FUNCTION update_product_service_stats() IS 'Automatically updates product/service statistics when transaction lines change';
COMMENT ON COLUMN transactions.original_transaction_id IS 'References the original transaction for returns, refunds, and related transactions';


-- ============================================================================
-- SCHEMA SYNCHRONIZATION NOTES
-- ============================================================================
-- This schema has been synchronized with the live Supabase database (project: iajycvybkkrwmhkompec)
-- Date: 2025-01-27
--
-- Changes made during synchronization:
-- 1. Added Mastra schema with all AI framework tables (mastra_evals, mastra_messages, etc.)
-- 2. Removed item_categories table (not present in Supabase)
-- 3. Updated organization_invitations table with proper column constraints
-- 4. Added missing created_by/updated_by columns to payment tables
-- 5. Updated default values to match Supabase exactly
-- 6. Removed references to item_categories in triggers
--
-- All public schema tables, views, functions, and triggers are now consistent with Supabase.
-- The Mastra schema contains AI framework tables used by the Mastra.ai agent system.
