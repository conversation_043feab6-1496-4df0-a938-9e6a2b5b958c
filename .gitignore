# Dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
yarn.lock
package-lock.json
.pnpm-debug.log

# Build outputs
dist/
build/
out/
.next/
*.tsbuildinfo
.turbo
tsconfig.tsbuildinfo

# Environment variables


# Mastra specific
.mastra/
.mastra/.build/
.mastra/output/
.mastra/output.txt
output.txt

# Database files
*.db
*.db-shm
*.db-wal
memory.db*
sqlite.db
sqlite.db-*

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store
.history

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
