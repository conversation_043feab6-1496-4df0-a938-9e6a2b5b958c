-- ============================================================================
-- NORMALIZED COLUMNS MIGRATION FOR DEEPLEDGER AI ACCOUNTING SYSTEM
-- ============================================================================
-- This script adds normalized columns to prevent duplicates and improve search
-- accuracy for AI agents processing plain English input.
--
-- Execute this script in phases to minimize downtime and validate each step.
-- ============================================================================

-- ============================================================================
-- PHASE 1: CORE NORMALIZATION FUNCTIONS
-- ============================================================================

-- Enable pg_trgm extension for fuzzy matching (if not already enabled)
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Core normalization function
CREATE OR REPLACE FUNCTION normalize_name(input_name TEXT)
RETURNS TEXT
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
  IF input_name IS NULL OR TRIM(input_name) = '' THEN
    RETURN NULL;
  END IF;
  
  -- Comprehensive normalization:
  -- 1. Convert to lowercase
  -- 2. Trim leading/trailing whitespace
  -- 3. Replace multiple spaces with single space
  -- 4. Remove common punctuation
  -- 5. Remove common business suffixes for better matching
  -- 6. Remove extra whitespace again
  RETURN TRIM(
    REGEXP_REPLACE(
      REGEXP_REPLACE(
        REGEXP_REPLACE(
          REGEXP_REPLACE(
            LOWER(TRIM(input_name)),
            '[.,;:!?()"\-&]', '', 'g'  -- Remove punctuation
          ),
          '\s+', ' ', 'g'  -- Multiple spaces to single space
        ),
        '\s+(inc|corp|corporation|ltd|limited|llc|llp|co|company|llc|pllc)\s*$', '', 'g'  -- Business suffixes
      ),
      '\s+(the|and|&)\s+', ' ', 'g'  -- Common words
    )
  );
END;
$$;

-- Function to calculate similarity score between two names
CREATE OR REPLACE FUNCTION name_similarity_score(name1 TEXT, name2 TEXT)
RETURNS NUMERIC
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
  norm1 TEXT;
  norm2 TEXT;
  similarity_score NUMERIC;
BEGIN
  IF name1 IS NULL OR name2 IS NULL THEN
    RETURN 0;
  END IF;
  
  norm1 := normalize_name(name1);
  norm2 := normalize_name(name2);
  
  -- Exact match after normalization
  IF norm1 = norm2 THEN
    RETURN 1.0;
  END IF;
  
  -- Use PostgreSQL's built-in similarity function
  BEGIN
    SELECT similarity(norm1, norm2) INTO similarity_score;
    RETURN similarity_score;
  EXCEPTION WHEN OTHERS THEN
    -- Fallback: check if one contains the other
    IF norm1 LIKE '%' || norm2 || '%' OR norm2 LIKE '%' || norm1 || '%' THEN
      RETURN 0.7;
    ELSE
      RETURN 0.0;
    END IF;
  END;
END;
$$;

-- ============================================================================
-- PHASE 2: ADD NORMALIZED COLUMNS TO CRITICAL TABLES
-- ============================================================================

-- 1. CUSTOMERS TABLE (Highest Priority)
-- Add normalized column
ALTER TABLE customers 
ADD COLUMN IF NOT EXISTS customer_name_normalized VARCHAR(100) 
GENERATED ALWAYS AS (normalize_name(customer_name)) STORED;

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_customers_name_normalized 
ON customers(organization_id, customer_name_normalized);

-- Create trigram index for fuzzy matching
CREATE INDEX IF NOT EXISTS idx_customers_name_trgm 
ON customers USING gin(customer_name_normalized gin_trgm_ops);

-- 2. VENDORS TABLE (Highest Priority)
-- Add normalized column
ALTER TABLE vendors 
ADD COLUMN IF NOT EXISTS vendor_name_normalized VARCHAR(100) 
GENERATED ALWAYS AS (normalize_name(vendor_name)) STORED;

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_vendors_name_normalized 
ON vendors(organization_id, vendor_name_normalized);

-- Create trigram index for fuzzy matching
CREATE INDEX IF NOT EXISTS idx_vendors_name_trgm 
ON vendors USING gin(vendor_name_normalized gin_trgm_ops);

-- 3. ACCOUNTS TABLE (Highest Priority)
-- Add normalized column
ALTER TABLE accounts 
ADD COLUMN IF NOT EXISTS account_name_normalized VARCHAR(100) 
GENERATED ALWAYS AS (normalize_name(account_name)) STORED;

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_accounts_name_normalized 
ON accounts(organization_id, account_name_normalized);

-- Create trigram index for fuzzy matching
CREATE INDEX IF NOT EXISTS idx_accounts_name_trgm 
ON accounts USING gin(account_name_normalized gin_trgm_ops);

-- 4. PRODUCTS_AND_SERVICES TABLE (Highest Priority)
-- Add normalized column
ALTER TABLE products_and_services 
ADD COLUMN IF NOT EXISTS product_service_name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(product_service_name)) STORED;

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_products_services_name_normalized 
ON products_and_services(organization_id, product_service_name_normalized);

-- Create trigram index for fuzzy matching
CREATE INDEX IF NOT EXISTS idx_products_services_name_trgm 
ON products_and_services USING gin(product_service_name_normalized gin_trgm_ops);

-- ============================================================================
-- PHASE 3: ADD NORMALIZED COLUMNS TO SUPPORTING TABLES
-- ============================================================================

-- 5. ORGANIZATIONS TABLE
-- Add normalized column
ALTER TABLE organizations 
ADD COLUMN IF NOT EXISTS name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(name)) STORED;

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_organizations_name_normalized 
ON organizations(name_normalized);

-- Create trigram index for fuzzy matching
CREATE INDEX IF NOT EXISTS idx_organizations_name_trgm 
ON organizations USING gin(name_normalized gin_trgm_ops);

-- 6. CLASSES TABLE
-- Add normalized column
ALTER TABLE classes 
ADD COLUMN IF NOT EXISTS class_name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(class_name)) STORED;

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_classes_name_normalized 
ON classes(organization_id, class_name_normalized);

-- Create trigram index for fuzzy matching
CREATE INDEX IF NOT EXISTS idx_classes_name_trgm 
ON classes USING gin(class_name_normalized gin_trgm_ops);

-- 7. PROJECTS TABLE
-- Add normalized column
ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS project_name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(project_name)) STORED;

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_projects_name_normalized 
ON projects(organization_id, project_name_normalized);

-- Create trigram index for fuzzy matching
CREATE INDEX IF NOT EXISTS idx_projects_name_trgm 
ON projects USING gin(project_name_normalized gin_trgm_ops);

-- ============================================================================
-- PHASE 4: ADD NORMALIZED COLUMNS TO ADMINISTRATIVE TABLES
-- ============================================================================

-- 8. TAX_GROUPS TABLE
-- Add normalized column
ALTER TABLE tax_groups 
ADD COLUMN IF NOT EXISTS tax_group_name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(tax_group_name)) STORED;

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_tax_groups_name_normalized 
ON tax_groups(organization_id, tax_group_name_normalized);

-- 9. EMAIL_TEMPLATES TABLE
-- Add normalized column
ALTER TABLE email_templates 
ADD COLUMN IF NOT EXISTS name_normalized VARCHAR(100) 
GENERATED ALWAYS AS (normalize_name(name)) STORED;

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_email_templates_name_normalized 
ON email_templates(name_normalized);

-- 10. USERS TABLE (Full name combination)
-- Add normalized column for full name
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS full_name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (
  normalize_name(
    TRIM(COALESCE(first_name, '') || ' ' || COALESCE(last_name, ''))
  )
) STORED;

-- Create index for fast searching
CREATE INDEX IF NOT EXISTS idx_users_full_name_normalized 
ON users(full_name_normalized);

-- ============================================================================
-- PHASE 5: FUZZY SEARCH FUNCTIONS FOR AI AGENTS
-- ============================================================================

-- Enhanced customer search function
CREATE OR REPLACE FUNCTION search_customers_fuzzy(
  p_search_term TEXT,
  p_organization_id UUID,
  p_min_similarity NUMERIC DEFAULT 0.3,
  p_limit INTEGER DEFAULT 10,
  p_is_active BOOLEAN DEFAULT TRUE
)
RETURNS TABLE(
  customer_id UUID,
  customer_name VARCHAR(100),
  customer_name_normalized VARCHAR(100),
  customer_email VARCHAR(100),
  customer_phone VARCHAR(20),
  customer_address TEXT,
  is_active BOOLEAN,
  organization_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  similarity_score NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.customer_id,
    c.customer_name,
    c.customer_name_normalized,
    c.customer_email,
    c.customer_phone,
    c.customer_address,
    c.is_active,
    c.organization_id,
    c.created_at,
    c.updated_at,
    similarity(c.customer_name_normalized, normalize_name(p_search_term)) as similarity_score
  FROM customers c
  WHERE c.organization_id = p_organization_id
    AND (p_is_active IS NULL OR c.is_active = p_is_active)
    AND similarity(c.customer_name_normalized, normalize_name(p_search_term)) >= p_min_similarity
  ORDER BY similarity_score DESC, c.customer_name
  LIMIT p_limit;
END;
$$;

-- Enhanced vendor search function
CREATE OR REPLACE FUNCTION search_vendors_fuzzy(
  p_search_term TEXT,
  p_organization_id UUID,
  p_min_similarity NUMERIC DEFAULT 0.3,
  p_limit INTEGER DEFAULT 10,
  p_is_active BOOLEAN DEFAULT TRUE
)
RETURNS TABLE(
  vendor_id UUID,
  vendor_name VARCHAR(100),
  vendor_name_normalized VARCHAR(100),
  vendor_email VARCHAR(100),
  vendor_phone VARCHAR(20),
  vendor_address TEXT,
  is_active BOOLEAN,
  organization_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  similarity_score NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    v.vendor_id,
    v.vendor_name,
    v.vendor_name_normalized,
    v.vendor_email,
    v.vendor_phone,
    v.vendor_address,
    v.is_active,
    v.organization_id,
    v.created_at,
    v.updated_at,
    similarity(v.vendor_name_normalized, normalize_name(p_search_term)) as similarity_score
  FROM vendors v
  WHERE v.organization_id = p_organization_id
    AND (p_is_active IS NULL OR v.is_active = p_is_active)
    AND similarity(v.vendor_name_normalized, normalize_name(p_search_term)) >= p_min_similarity
  ORDER BY similarity_score DESC, v.vendor_name
  LIMIT p_limit;
END;
$$;

-- Enhanced account search function
CREATE OR REPLACE FUNCTION search_accounts_fuzzy(
  p_search_term TEXT,
  p_organization_id UUID,
  p_min_similarity NUMERIC DEFAULT 0.3,
  p_limit INTEGER DEFAULT 10,
  p_is_active BOOLEAN DEFAULT TRUE
)
RETURNS TABLE(
  account_id UUID,
  account_code INTEGER,
  account_name VARCHAR(100),
  account_name_normalized VARCHAR(100),
  account_type VARCHAR(20),
  account_type_detail VARCHAR(50),
  description TEXT,
  is_active BOOLEAN,
  organization_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  similarity_score NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    a.account_id,
    a.account_code,
    a.account_name,
    a.account_name_normalized,
    a.account_type,
    a.account_type_detail,
    a.description,
    a.is_active,
    a.organization_id,
    a.created_at,
    a.updated_at,
    similarity(a.account_name_normalized, normalize_name(p_search_term)) as similarity_score
  FROM accounts a
  WHERE a.organization_id = p_organization_id
    AND (p_is_active IS NULL OR a.is_active = p_is_active)
    AND similarity(a.account_name_normalized, normalize_name(p_search_term)) >= p_min_similarity
  ORDER BY similarity_score DESC, a.account_name
  LIMIT p_limit;
END;
$$;

-- Enhanced products/services search function
CREATE OR REPLACE FUNCTION search_products_services_fuzzy(
  p_search_term TEXT,
  p_organization_id UUID,
  p_min_similarity NUMERIC DEFAULT 0.3,
  p_limit INTEGER DEFAULT 10,
  p_is_active BOOLEAN DEFAULT TRUE,
  p_type VARCHAR DEFAULT NULL
)
RETURNS TABLE(
  product_service_id UUID,
  product_service_name VARCHAR,
  product_service_name_normalized VARCHAR,
  product_service_type VARCHAR,
  description TEXT,
  sku VARCHAR,
  sales_price NUMERIC(15,2),
  purchase_price NUMERIC(15,2),
  is_active BOOLEAN,
  organization_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  similarity_score NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    ps.product_service_id,
    ps.product_service_name,
    ps.product_service_name_normalized,
    ps.product_service_type,
    ps.description,
    ps.sku,
    ps.sales_price,
    ps.purchase_price,
    ps.is_active,
    ps.organization_id,
    ps.created_at,
    ps.updated_at,
    similarity(ps.product_service_name_normalized, normalize_name(p_search_term)) as similarity_score
  FROM products_and_services ps
  WHERE ps.organization_id = p_organization_id
    AND (p_is_active IS NULL OR ps.is_active = p_is_active)
    AND (p_type IS NULL OR ps.product_service_type = p_type)
    AND similarity(ps.product_service_name_normalized, normalize_name(p_search_term)) >= p_min_similarity
  ORDER BY similarity_score DESC, ps.product_service_name
  LIMIT p_limit;
END;
$$;

-- ============================================================================
-- PHASE 6: DUPLICATE DETECTION FUNCTIONS
-- ============================================================================

-- Function to check for potential duplicates before creating entities
CREATE OR REPLACE FUNCTION check_for_duplicates(
  p_entity_type TEXT,
  p_name TEXT,
  p_organization_id UUID,
  p_exclude_id UUID DEFAULT NULL,
  p_min_similarity NUMERIC DEFAULT 0.7
)
RETURNS TABLE(
  entity_id UUID,
  entity_name TEXT,
  similarity_score NUMERIC,
  risk_level TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
  normalized_name TEXT;
BEGIN
  normalized_name := normalize_name(p_name);

  IF p_entity_type = 'customer' THEN
    RETURN QUERY
    SELECT
      c.customer_id::UUID as entity_id,
      c.customer_name::TEXT as entity_name,
      similarity(c.customer_name_normalized, normalized_name) as similarity_score,
      CASE
        WHEN similarity(c.customer_name_normalized, normalized_name) >= 0.9 THEN 'HIGH'
        WHEN similarity(c.customer_name_normalized, normalized_name) >= p_min_similarity THEN 'MEDIUM'
        ELSE 'LOW'
      END as risk_level
    FROM customers c
    WHERE c.organization_id = p_organization_id
      AND (p_exclude_id IS NULL OR c.customer_id != p_exclude_id)
      AND similarity(c.customer_name_normalized, normalized_name) >= p_min_similarity
    ORDER BY similarity_score DESC;

  ELSIF p_entity_type = 'vendor' THEN
    RETURN QUERY
    SELECT
      v.vendor_id::UUID as entity_id,
      v.vendor_name::TEXT as entity_name,
      similarity(v.vendor_name_normalized, normalized_name) as similarity_score,
      CASE
        WHEN similarity(v.vendor_name_normalized, normalized_name) >= 0.9 THEN 'HIGH'
        WHEN similarity(v.vendor_name_normalized, normalized_name) >= p_min_similarity THEN 'MEDIUM'
        ELSE 'LOW'
      END as risk_level
    FROM vendors v
    WHERE v.organization_id = p_organization_id
      AND (p_exclude_id IS NULL OR v.vendor_id != p_exclude_id)
      AND similarity(v.vendor_name_normalized, normalized_name) >= p_min_similarity
    ORDER BY similarity_score DESC;

  ELSIF p_entity_type = 'account' THEN
    RETURN QUERY
    SELECT
      a.account_id::UUID as entity_id,
      a.account_name::TEXT as entity_name,
      similarity(a.account_name_normalized, normalized_name) as similarity_score,
      CASE
        WHEN similarity(a.account_name_normalized, normalized_name) >= 0.9 THEN 'HIGH'
        WHEN similarity(a.account_name_normalized, normalized_name) >= p_min_similarity THEN 'MEDIUM'
        ELSE 'LOW'
      END as risk_level
    FROM accounts a
    WHERE a.organization_id = p_organization_id
      AND (p_exclude_id IS NULL OR a.account_id != p_exclude_id)
      AND similarity(a.account_name_normalized, normalized_name) >= p_min_similarity
    ORDER BY similarity_score DESC;

  ELSIF p_entity_type = 'product_service' THEN
    RETURN QUERY
    SELECT
      ps.product_service_id::UUID as entity_id,
      ps.product_service_name::TEXT as entity_name,
      similarity(ps.product_service_name_normalized, normalized_name) as similarity_score,
      CASE
        WHEN similarity(ps.product_service_name_normalized, normalized_name) >= 0.9 THEN 'HIGH'
        WHEN similarity(ps.product_service_name_normalized, normalized_name) >= p_min_similarity THEN 'MEDIUM'
        ELSE 'LOW'
      END as risk_level
    FROM products_and_services ps
    WHERE ps.organization_id = p_organization_id
      AND (p_exclude_id IS NULL OR ps.product_service_id != p_exclude_id)
      AND similarity(ps.product_service_name_normalized, normalized_name) >= p_min_similarity
    ORDER BY similarity_score DESC;
  END IF;
END;
$$;

-- ============================================================================
-- PHASE 7: VALIDATION AND TESTING
-- ============================================================================

-- Function to validate the migration was successful
CREATE OR REPLACE FUNCTION validate_normalized_columns()
RETURNS TABLE(
  table_name TEXT,
  total_records BIGINT,
  normalized_records BIGINT,
  null_normalized BIGINT,
  status TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    'customers'::TEXT,
    COUNT(*)::BIGINT,
    COUNT(customer_name_normalized)::BIGINT,
    COUNT(*) - COUNT(customer_name_normalized)::BIGINT,
    CASE WHEN COUNT(*) = COUNT(customer_name_normalized) THEN 'OK' ELSE 'ISSUES' END::TEXT
  FROM customers

  UNION ALL

  SELECT
    'vendors'::TEXT,
    COUNT(*)::BIGINT,
    COUNT(vendor_name_normalized)::BIGINT,
    COUNT(*) - COUNT(vendor_name_normalized)::BIGINT,
    CASE WHEN COUNT(*) = COUNT(vendor_name_normalized) THEN 'OK' ELSE 'ISSUES' END::TEXT
  FROM vendors

  UNION ALL

  SELECT
    'accounts'::TEXT,
    COUNT(*)::BIGINT,
    COUNT(account_name_normalized)::BIGINT,
    COUNT(*) - COUNT(account_name_normalized)::BIGINT,
    CASE WHEN COUNT(*) = COUNT(account_name_normalized) THEN 'OK' ELSE 'ISSUES' END::TEXT
  FROM accounts

  UNION ALL

  SELECT
    'products_and_services'::TEXT,
    COUNT(*)::BIGINT,
    COUNT(product_service_name_normalized)::BIGINT,
    COUNT(*) - COUNT(product_service_name_normalized)::BIGINT,
    CASE WHEN COUNT(*) = COUNT(product_service_name_normalized) THEN 'OK' ELSE 'ISSUES' END::TEXT
  FROM products_and_services;
END;
$$;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- Run validation
SELECT * FROM validate_normalized_columns();

-- Display summary
SELECT
  'NORMALIZED COLUMNS MIGRATION COMPLETED' as status,
  NOW() as completed_at,
  'Run validate_normalized_columns() to check status' as next_step;
