# Default Accounts Implementation Guide

## Overview
This guide provides step-by-step instructions for implementing automatic default account creation when new organizations are registered in the system.

## Implementation Options

### Option 1: Database Trigger (Recommended)
**Pros**: Guaranteed execution, database-level consistency
**Cons**: Logic in database, harder to modify

### Option 2: Application-Level Hook
**Pros**: Business logic in application, easier to test/modify
**Cons**: Could be bypassed if multiple creation paths exist

### Option 3: Background Job
**Pros**: Non-blocking, graceful error handling
**Cons**: Temporary inconsistent state

## Recommended Implementation: Application-Level Hook

### Step 1: Create Default Accounts Data Structure

```javascript
// default-accounts.js
export const DEFAULT_ACCOUNTS = [
  // Assets
  { code: '1000', name: 'Cash', type: 'ASSET', category: 'CURRENT_ASSET', balance: 'DEBIT' },
  { code: '1100', name: 'Bank Account', type: 'ASSET', category: 'CURRENT_ASSET', balance: 'DEBIT' },
  { code: '1200', name: 'Accounts Receivable', type: 'ASSET', category: 'CURRENT_ASSET', balance: 'DEBIT' },
  { code: '1300', name: 'Inventory', type: 'ASSET', category: 'CURRENT_ASSET', balance: 'DEBIT' },
  { code: '1400', name: 'Prepaid Expenses', type: 'ASSET', category: 'CURRENT_ASSET', balance: 'DEBIT' },
  { code: '1500', name: 'Equipment', type: 'ASSET', category: 'FIXED_ASSET', balance: 'DEBIT' },
  { code: '1600', name: 'Accumulated Depreciation', type: 'ASSET', category: 'FIXED_ASSET', balance: 'CREDIT' },
  
  // Liabilities
  { code: '2000', name: 'Accounts Payable', type: 'LIABILITY', category: 'CURRENT_LIABILITY', balance: 'CREDIT' },
  { code: '2100', name: 'Accrued Expenses', type: 'LIABILITY', category: 'CURRENT_LIABILITY', balance: 'CREDIT' },
  { code: '2200', name: 'Loans Payable', type: 'LIABILITY', category: 'LONG_TERM_LIABILITY', balance: 'CREDIT' },
  { code: '2300', name: 'Sales Tax Payable', type: 'LIABILITY', category: 'CURRENT_LIABILITY', balance: 'CREDIT' },
  { code: '2400', name: 'Payroll Liabilities', type: 'LIABILITY', category: 'CURRENT_LIABILITY', balance: 'CREDIT' },
  
  // Equity
  { code: '3000', name: 'Owner\'s Equity', type: 'EQUITY', category: 'EQUITY', balance: 'CREDIT' },
  { code: '3100', name: 'Retained Earnings', type: 'EQUITY', category: 'EQUITY', balance: 'CREDIT' },
  
  // Revenue
  { code: '4000', name: 'Sales Revenue', type: 'REVENUE', category: 'OPERATING_REVENUE', balance: 'CREDIT' },
  { code: '4100', name: 'Service Revenue', type: 'REVENUE', category: 'OPERATING_REVENUE', balance: 'CREDIT' },
  { code: '4200', name: 'Other Income', type: 'REVENUE', category: 'NON_OPERATING_REVENUE', balance: 'CREDIT' },
  
  // Expenses
  { code: '5000', name: 'Cost of Goods Sold', type: 'EXPENSE', category: 'COST_OF_SALES', balance: 'DEBIT' },
  { code: '5100', name: 'Office Supplies', type: 'EXPENSE', category: 'OPERATING_EXPENSE', balance: 'DEBIT' },
  { code: '5200', name: 'Rent', type: 'EXPENSE', category: 'OPERATING_EXPENSE', balance: 'DEBIT' },
  { code: '5300', name: 'Utilities', type: 'EXPENSE', category: 'OPERATING_EXPENSE', balance: 'DEBIT' },
  { code: '5400', name: 'Marketing', type: 'EXPENSE', category: 'OPERATING_EXPENSE', balance: 'DEBIT' },
  { code: '5500', name: 'Professional Services', type: 'EXPENSE', category: 'OPERATING_EXPENSE', balance: 'DEBIT' },
  { code: '5600', name: 'Insurance', type: 'EXPENSE', category: 'OPERATING_EXPENSE', balance: 'DEBIT' },
  { code: '5700', name: 'Other Expenses', type: 'EXPENSE', category: 'OPERATING_EXPENSE', balance: 'DEBIT' }
];
```

### Step 2: Create Account Service

```javascript
// services/accountService.js
import { supabase } from '../lib/supabase';
import { DEFAULT_ACCOUNTS } from '../data/default-accounts';

export class AccountService {
  static async createDefaultAccounts(organizationId) {
    try {
      const accountsToCreate = DEFAULT_ACCOUNTS.map(account => ({
        organization_id: organizationId,
        account_code: account.code,
        account_name: account.name,
        account_type: account.type,
        account_category: account.category,
        normal_balance: account.balance,
        is_default: true,
        is_system_account: true,
        is_active: true,
        description: `Default ${account.name} account`
      }));

      const { data, error } = await supabase
        .from('accounts')
        .insert(accountsToCreate)
        .select();

      if (error) {
        console.error('Error creating default accounts:', error);
        throw error;
      }

      console.log(`Created ${data.length} default accounts for organization ${organizationId}`);
      return data;
    } catch (error) {
      console.error('Failed to create default accounts:', error);
      throw error;
    }
  }

  static async getAccountByCode(organizationId, accountCode) {
    const { data, error } = await supabase
      .from('accounts')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('account_code', accountCode)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    return data;
  }

  static async getARAccount(organizationId) {
    return this.getAccountByCode(organizationId, '1200');
  }

  static async getAPAccount(organizationId) {
    return this.getAccountByCode(organizationId, '2000');
  }

  static async getCashAccount(organizationId) {
    return this.getAccountByCode(organizationId, '1000');
  }
}
```

### Step 3: Modify Organization Creation

```javascript
// services/organizationService.js
import { supabase } from '../lib/supabase';
import { AccountService } from './accountService';

export class OrganizationService {
  static async createOrganization(organizationData) {
    try {
      // Create organization
      const { data: organization, error: orgError } = await supabase
        .from('organizations')
        .insert(organizationData)
        .select()
        .single();

      if (orgError) {
        throw orgError;
      }

      // Create default accounts
      await AccountService.createDefaultAccounts(organization.id);

      console.log(`Organization ${organization.id} created with default accounts`);
      return organization;
    } catch (error) {
      console.error('Failed to create organization:', error);
      throw error;
    }
  }
}
```

### Step 4: Update Sales Invoice Workflow

```javascript
// workflows/salesInvoiceWorkflow.js
import { AccountService } from '../services/accountService';

export async function createSalesInvoice(invoiceData) {
  try {
    // Get AR account for the organization
    const arAccount = await AccountService.getARAccount(invoiceData.organization_id);
    
    if (!arAccount) {
      throw new Error('Accounts Receivable account not found for organization');
    }

    // Create invoice with AR account
    const invoice = {
      ...invoiceData,
      ar_account_id: arAccount.id,
      ar_account_code: arAccount.account_code
    };

    // Continue with invoice creation logic...
    return invoice;
  } catch (error) {
    console.error('Failed to create sales invoice:', error);
    throw error;
  }
}
```

## Database Migration

```sql
-- Create accounts table if it doesn't exist
CREATE TABLE IF NOT EXISTS accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    account_code VARCHAR(10) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_type VARCHAR(50) NOT NULL CHECK (account_type IN ('ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE')),
    account_category VARCHAR(100),
    normal_balance VARCHAR(10) NOT NULL CHECK (normal_balance IN ('DEBIT', 'CREDIT')),
    is_default BOOLEAN DEFAULT FALSE,
    is_system_account BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    parent_account_id UUID REFERENCES accounts(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT unique_org_account_code UNIQUE(organization_id, account_code),
    CONSTRAINT unique_org_account_name UNIQUE(organization_id, account_name)
);

-- Create indexes for performance
CREATE INDEX idx_accounts_organization_id ON accounts(organization_id);
CREATE INDEX idx_accounts_account_code ON accounts(account_code);
CREATE INDEX idx_accounts_account_type ON accounts(account_type);
CREATE INDEX idx_accounts_is_active ON accounts(is_active);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_accounts_updated_at 
    BEFORE UPDATE ON accounts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
```

## Testing Checklist

- [ ] New organization creation triggers default account creation
- [ ] All 22 default accounts are created correctly
- [ ] Account codes and names are unique within organization
- [ ] System accounts cannot be deleted
- [ ] Sales invoice workflow uses AR account (1200)
- [ ] Purchase workflow uses AP account (2000)
- [ ] Financial reports work with default accounts
- [ ] Error handling for duplicate account creation
- [ ] Performance testing with multiple organizations

## Rollback Plan

If issues arise, you can:
1. Disable automatic account creation in organization service
2. Manually clean up test accounts if needed
3. Revert database schema changes if necessary

## Next Steps

1. Implement the account service and organization modification
2. Test with a new organization creation
3. Verify sales invoice workflow integration
4. Add financial reporting capabilities
5. Consider industry-specific account templates
