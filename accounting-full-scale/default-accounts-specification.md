# Default Accounts Specification

## Overview
This document defines the default chart of accounts that will be automatically created when a new organization is registered in the system. These accounts provide a comprehensive foundation for accounting operations without overwhelming users with complexity.

## Design Principles
- **Simplicity First**: Use clear, intuitive account names instead of formal accounting jargon
- **Universal Applicability**: Accounts suitable for most business types and sizes
- **Immediate Usability**: Enable core accounting workflows (AR, AP, invoicing) from day one
- **User-Friendly**: Non-accountants should easily understand account purposes

## Default Chart of Accounts

### Asset Accounts (1000-1999)
| Code | Account Name | Type | Type Detail | Description |
|------|--------------|------|-------------|-------------|
| 1000 | Cash | asset | other_current_assets | Physical cash and petty cash |
| 1100 | Bank Account | asset | bank | Primary business checking account |
| 1200 | Accounts Receivable | asset | accounts_receivable | Money owed by customers (AR) |
| 1300 | Inventory | asset | other_current_assets | Products held for sale |
| 1400 | Prepaid Expenses | asset | other_current_assets | Expenses paid in advance |
| 1500 | Equipment | asset | fixed_assets | Business equipment and machinery |
| 1600 | Accumulated Depreciation | asset | fixed_assets | Depreciation of equipment over time |

### Liability Accounts (2000-2999)
| Code | Account Name | Type | Type Detail | Description |
|------|--------------|------|-------------|-------------|
| 2000 | Accounts Payable | liability | accounts_payable | Money owed to suppliers (AP) |
| 2100 | Accrued Expenses | liability | other_current_liabilities | Expenses incurred but not yet paid |
| 2200 | Loans Payable | liability | long_term_liabilities | Business loans and financing |
| 2300 | Sales Tax Payable | liability | other_current_liabilities | Sales tax collected from customers |
| 2400 | Payroll Liabilities | liability | other_current_liabilities | Payroll taxes and withholdings |

### Equity Accounts (3000-3999)
| Code | Account Name | Type | Type Detail | Description |
|------|--------------|------|-------------|-------------|
| 3000 | Owner's Equity | equity | equity | Owner's investment in the business |
| 3100 | Retained Earnings | equity | equity | Accumulated profits/losses |

### Revenue Accounts (4000-4999)
| Code | Account Name | Type | Type Detail | Description |
|------|--------------|------|-------------|-------------|
| 4000 | Sales Revenue | revenue | revenue | Revenue from product sales |
| 4100 | Service Revenue | revenue | revenue | Revenue from services provided |
| 4200 | Other Income | revenue | other_revenue | Miscellaneous income |

### Expense Accounts (5000-5999)
| Code | Account Name | Type | Type Detail | Description |
|------|--------------|------|-------------|-------------|
| 5000 | Cost of Goods Sold | expense | cost_of_goods_sold | Direct costs of products sold |
| 5100 | Office Supplies | expense | expenditures | Office materials and supplies |
| 5200 | Rent | expense | expenditures | Office or facility rent |
| 5300 | Utilities | expense | expenditures | Electricity, water, gas |
| 5400 | Marketing | expense | expenditures | Advertising and marketing costs |
| 5500 | Professional Services | expense | expenditures | Legal, accounting, consulting |
| 5600 | Insurance | expense | expenditures | Business insurance premiums |
| 5700 | Travel & Transportation | expense | expenditures | Business travel, mileage, parking |
| 5710 | Meals & Entertainment | expense | expenditures | Business meals, client entertainment |
| 5720 | Phone & Internet | expense | expenditures | Business phone, internet, mobile plans |
| 5730 | Software & Subscriptions | expense | expenditures | Business software, SaaS tools, online services |
| 5740 | Bank Fees | expense | expenditures | Transaction fees, account fees, payment processing |
| 5750 | Training & Education | expense | expenditures | Courses, certifications, books, conferences |
| 5760 | Equipment Maintenance | expense | expenditures | Repairs, maintenance contracts |
| 5770 | Depreciation | expense | expenditures | Equipment depreciation expense |
| 5780 | Interest Expense | expense | other_expenditure | Loan interest, credit card interest |
| 5790 | Taxes & Licenses | expense | expenditures | Business licenses, permits, property taxes |
| 5800 | Other Expenses | expense | expenditures | Miscellaneous business expenses |

## Account Properties

### Required Fields
- `account_id`: UUID primary key (auto-generated)
- `account_code`: Integer identifier (unique within organization)
- `account_name`: VARCHAR(100) - Human-readable account name
- `account_type`: VARCHAR(20) - Must be: asset, liability, equity, revenue, expense
- `account_type_detail`: VARCHAR(50) - Subcategory based on account_type
- `organization_id`: UUID foreign key to organization
- `account_level`: Integer (default: 1) - For hierarchical structures
- `is_summary`: Boolean (default: false) - Whether account is a summary/parent account

### Optional Fields
- `description`: TEXT - Detailed account description
- `parent_id`: UUID - For hierarchical account structures
- `is_active`: Boolean (default: true) - Account status
- `created_by`: UUID - User who created the account
- `updated_by`: UUID - User who last updated the account
- `created_at`: Timestamp (auto-generated)
- `updated_at`: Timestamp (auto-updated)

### Account Type Detail Constraints
- **asset**: accounts_receivable, other_current_assets, bank, fixed_assets, other_assets
- **liability**: accounts_payable, credit_card, other_current_liabilities, long_term_liabilities
- **equity**: equity
- **revenue**: revenue, other_revenue
- **expense**: cost_of_goods_sold, expenditures, other_expenditure

## Implementation Strategy

### 1. Database Schema
```sql
CREATE TABLE accounts (
    account_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_code INTEGER NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    account_type VARCHAR(20) NOT NULL CHECK (account_type IN ('asset', 'liability', 'equity', 'revenue', 'expense')),
    account_type_detail VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    organization_id UUID NOT NULL REFERENCES organizations(organization_id),
    parent_id UUID REFERENCES accounts(account_id),
    account_level INTEGER NOT NULL DEFAULT 1,
    is_summary BOOLEAN NOT NULL DEFAULT FALSE,
    created_by UUID,
    updated_by UUID,
    UNIQUE(organization_id, account_code),
    -- Check constraint for account_type_detail based on account_type
    CONSTRAINT check_account_type_detail CHECK (
        (account_type = 'asset' AND account_type_detail IN ('accounts_receivable', 'other_current_assets', 'bank', 'fixed_assets', 'other_assets')) OR
        (account_type = 'liability' AND account_type_detail IN ('accounts_payable', 'credit_card', 'other_current_liabilities', 'long_term_liabilities')) OR
        (account_type = 'equity' AND account_type_detail = 'equity') OR
        (account_type = 'revenue' AND account_type_detail IN ('revenue', 'other_revenue')) OR
        (account_type = 'expense' AND account_type_detail IN ('cost_of_goods_sold', 'expenditures', 'other_expenditure'))
    )
);
```

### 2. Account Creation Trigger
The system automatically creates all default accounts when a new organization is added using a database trigger.

#### Trigger Function
```sql
CREATE OR REPLACE FUNCTION create_default_accounts_for_organization()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert all 35 default accounts for the new organization
    INSERT INTO accounts (account_code, account_name, account_type, account_type_detail, description, organization_id, is_active, account_level, is_summary) VALUES
    -- Asset Accounts (7 accounts: 1000-1600)
    (1000, 'Cash', 'asset', 'other_current_assets', 'Physical cash and petty cash', NEW.organization_id, true, 1, false),
    (1100, 'Bank Account', 'asset', 'bank', 'Primary business checking account', NEW.organization_id, true, 1, false),
    (1200, 'Accounts Receivable', 'asset', 'accounts_receivable', 'Money owed by customers (AR)', NEW.organization_id, true, 1, false),
    -- ... (additional accounts)
    -- Expense Accounts (18 accounts: 5000-5800)
    (5800, 'Other Expenses', 'expense', 'expenditures', 'Miscellaneous business expenses', NEW.organization_id, true, 1, false);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

#### Trigger Definition
```sql
CREATE TRIGGER create_default_accounts_trigger
    AFTER INSERT ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION create_default_accounts_for_organization();
```

#### Features
- **Automatic Execution**: Triggers immediately after organization creation
- **Complete Chart**: Creates all 35 default accounts (7 assets, 5 liabilities, 2 equity, 3 revenue, 18 expenses)
- **Proper Categorization**: Uses correct account_type and account_type_detail values
- **Consistency**: Ensures every organization starts with the same foundational accounts
- **No Manual Intervention**: Fully automated process

### 3. Integration Points
- **Sales Invoices**: Default to account_code 1200 (Accounts Receivable)
- **Purchase Orders**: Default to account_code 2000 (Accounts Payable)
- **Expense Tracking**: Pre-populated expense categories
- **Financial Reports**: Immediate P&L and Balance Sheet capability

## Business Rules

### Account Management
1. Account codes must be unique within an organization
2. Account names should be unique within an organization
3. Inactive accounts (is_active = false) are hidden from UI but preserved for historical data
4. Summary accounts (is_summary = true) cannot have transactions posted directly
5. Account type detail must match the allowed values for the account type

### Workflow Integration
1. Sales invoices automatically use AR account (account_code 1200)
2. Vendor bills automatically use AP account (account_code 2000)
3. Payment processing updates appropriate cash/bank accounts
4. Expense categorization uses predefined expense accounts

## Future Enhancements

### Industry-Specific Templates
- Retail: Additional inventory accounts
- Service: Expanded service revenue categories
- Manufacturing: Work-in-progress accounts
- SaaS: Subscription and recurring revenue accounts

### Advanced Features
- Account hierarchies (parent/child relationships)
- Custom account numbering schemes
- Multi-currency support
- Department/location segmentation

## Testing Requirements
1. Verify all default accounts are created for new organizations
2. Test account uniqueness constraints
3. Validate workflow integrations (invoicing, payments)
4. Ensure system accounts cannot be deleted
5. Test financial report generation with default accounts
