-- Default Accounts Database Setup
-- This script creates the accounts table and sets up default account creation

-- Create accounts table
CREATE TABLE IF NOT EXISTS accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    account_code VARCHAR(10) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_type VARCHAR(50) NOT NULL CHECK (account_type IN ('ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE')),
    account_category VARCHAR(100),
    normal_balance VARCHAR(10) NOT NULL CHECK (normal_balance IN ('DEBIT', 'CREDIT')),
    is_default BOOLEAN DEFAULT FALSE,
    is_system_account BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    parent_account_id UUID REFERENCES accounts(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_org_account_code UNIQUE(organization_id, account_code),
    CONSTRAINT unique_org_account_name UNIQUE(organization_id, account_name)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_accounts_organization_id ON accounts(organization_id);
CREATE INDEX IF NOT EXISTS idx_accounts_account_code ON accounts(account_code);
CREATE INDEX IF NOT EXISTS idx_accounts_account_type ON accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_accounts_is_active ON accounts(is_active);
CREATE INDEX IF NOT EXISTS idx_accounts_is_default ON accounts(is_default);
CREATE INDEX IF NOT EXISTS idx_accounts_is_system ON accounts(is_system_account);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for accounts table
DROP TRIGGER IF EXISTS update_accounts_updated_at ON accounts;
CREATE TRIGGER update_accounts_updated_at 
    BEFORE UPDATE ON accounts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to create default accounts for an organization
CREATE OR REPLACE FUNCTION create_default_accounts(org_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Insert default accounts
    INSERT INTO accounts (
        organization_id, 
        account_code, 
        account_name, 
        account_type, 
        account_category, 
        normal_balance, 
        is_default, 
        is_system_account, 
        description
    ) VALUES
    -- Assets
    (org_id, '1000', 'Cash', 'ASSET', 'CURRENT_ASSET', 'DEBIT', true, true, 'Physical cash and petty cash'),
    (org_id, '1100', 'Bank Account', 'ASSET', 'CURRENT_ASSET', 'DEBIT', true, true, 'Primary business checking account'),
    (org_id, '1200', 'Accounts Receivable', 'ASSET', 'CURRENT_ASSET', 'DEBIT', true, true, 'Money owed by customers (AR)'),
    (org_id, '1300', 'Inventory', 'ASSET', 'CURRENT_ASSET', 'DEBIT', true, true, 'Products held for sale'),
    (org_id, '1400', 'Prepaid Expenses', 'ASSET', 'CURRENT_ASSET', 'DEBIT', true, true, 'Expenses paid in advance'),
    (org_id, '1500', 'Equipment', 'ASSET', 'FIXED_ASSET', 'DEBIT', true, true, 'Business equipment and machinery'),
    (org_id, '1600', 'Accumulated Depreciation', 'ASSET', 'FIXED_ASSET', 'CREDIT', true, true, 'Depreciation of equipment over time'),
    
    -- Liabilities
    (org_id, '2000', 'Accounts Payable', 'LIABILITY', 'CURRENT_LIABILITY', 'CREDIT', true, true, 'Money owed to suppliers (AP)'),
    (org_id, '2100', 'Accrued Expenses', 'LIABILITY', 'CURRENT_LIABILITY', 'CREDIT', true, true, 'Expenses incurred but not yet paid'),
    (org_id, '2200', 'Loans Payable', 'LIABILITY', 'LONG_TERM_LIABILITY', 'CREDIT', true, true, 'Business loans and financing'),
    (org_id, '2300', 'Sales Tax Payable', 'LIABILITY', 'CURRENT_LIABILITY', 'CREDIT', true, true, 'Sales tax collected from customers'),
    (org_id, '2400', 'Payroll Liabilities', 'LIABILITY', 'CURRENT_LIABILITY', 'CREDIT', true, true, 'Payroll taxes and withholdings'),
    
    -- Equity
    (org_id, '3000', 'Owner''s Equity', 'EQUITY', 'EQUITY', 'CREDIT', true, true, 'Owner''s investment in the business'),
    (org_id, '3100', 'Retained Earnings', 'EQUITY', 'EQUITY', 'CREDIT', true, true, 'Accumulated profits/losses'),
    
    -- Revenue
    (org_id, '4000', 'Sales Revenue', 'REVENUE', 'OPERATING_REVENUE', 'CREDIT', true, true, 'Revenue from product sales'),
    (org_id, '4100', 'Service Revenue', 'REVENUE', 'OPERATING_REVENUE', 'CREDIT', true, true, 'Revenue from services provided'),
    (org_id, '4200', 'Other Income', 'REVENUE', 'NON_OPERATING_REVENUE', 'CREDIT', true, true, 'Miscellaneous income'),
    
    -- Expenses
    (org_id, '5000', 'Cost of Goods Sold', 'EXPENSE', 'COST_OF_SALES', 'DEBIT', true, true, 'Direct costs of products sold'),
    (org_id, '5100', 'Office Supplies', 'EXPENSE', 'OPERATING_EXPENSE', 'DEBIT', true, true, 'Office materials and supplies'),
    (org_id, '5200', 'Rent', 'EXPENSE', 'OPERATING_EXPENSE', 'DEBIT', true, true, 'Office or facility rent'),
    (org_id, '5300', 'Utilities', 'EXPENSE', 'OPERATING_EXPENSE', 'DEBIT', true, true, 'Electricity, water, internet'),
    (org_id, '5400', 'Marketing', 'EXPENSE', 'OPERATING_EXPENSE', 'DEBIT', true, true, 'Advertising and marketing costs'),
    (org_id, '5500', 'Professional Services', 'EXPENSE', 'OPERATING_EXPENSE', 'DEBIT', true, true, 'Legal, accounting, consulting'),
    (org_id, '5600', 'Insurance', 'EXPENSE', 'OPERATING_EXPENSE', 'DEBIT', true, true, 'Business insurance premiums'),
    (org_id, '5700', 'Other Expenses', 'EXPENSE', 'OPERATING_EXPENSE', 'DEBIT', true, true, 'Miscellaneous business expenses');
    
    RAISE NOTICE 'Created default accounts for organization %', org_id;
END;
$$ LANGUAGE plpgsql;

-- Optional: Trigger to automatically create default accounts when organization is created
-- Uncomment if you want database-level automation
/*
CREATE OR REPLACE FUNCTION trigger_create_default_accounts()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM create_default_accounts(NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER auto_create_default_accounts
    AFTER INSERT ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION trigger_create_default_accounts();
*/

-- Helper functions for common account lookups
CREATE OR REPLACE FUNCTION get_ar_account(org_id UUID)
RETURNS UUID AS $$
DECLARE
    account_id UUID;
BEGIN
    SELECT id INTO account_id 
    FROM accounts 
    WHERE organization_id = org_id 
    AND account_code = '1200' 
    AND is_active = true;
    
    RETURN account_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_ap_account(org_id UUID)
RETURNS UUID AS $$
DECLARE
    account_id UUID;
BEGIN
    SELECT id INTO account_id 
    FROM accounts 
    WHERE organization_id = org_id 
    AND account_code = '2000' 
    AND is_active = true;
    
    RETURN account_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_cash_account(org_id UUID)
RETURNS UUID AS $$
DECLARE
    account_id UUID;
BEGIN
    SELECT id INTO account_id 
    FROM accounts 
    WHERE organization_id = org_id 
    AND account_code = '1000' 
    AND is_active = true;
    
    RETURN account_id;
END;
$$ LANGUAGE plpgsql;

-- View for easy account lookup
CREATE OR REPLACE VIEW account_lookup AS
SELECT 
    a.id,
    a.organization_id,
    a.account_code,
    a.account_name,
    a.account_type,
    a.account_category,
    a.normal_balance,
    a.is_default,
    a.is_system_account,
    a.is_active,
    o.name as organization_name
FROM accounts a
JOIN organizations o ON a.organization_id = o.id
WHERE a.is_active = true
ORDER BY a.account_code;

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE ON accounts TO your_app_user;
-- GRANT USAGE ON SEQUENCE accounts_id_seq TO your_app_user;
-- GRANT EXECUTE ON FUNCTION create_default_accounts(UUID) TO your_app_user;
-- GRANT EXECUTE ON FUNCTION get_ar_account(UUID) TO your_app_user;
-- GRANT EXECUTE ON FUNCTION get_ap_account(UUID) TO your_app_user;
-- GRANT EXECUTE ON FUNCTION get_cash_account(UUID) TO your_app_user;

-- Example usage:
-- To create default accounts for an existing organization:
-- SELECT create_default_accounts('your-org-uuid-here');

-- To get AR account for an organization:
-- SELECT get_ar_account('your-org-uuid-here');

COMMENT ON TABLE accounts IS 'Chart of accounts for organizations';
COMMENT ON COLUMN accounts.account_code IS 'Unique account code within organization (e.g., 1200)';
COMMENT ON COLUMN accounts.account_name IS 'Human-readable account name (e.g., Accounts Receivable)';
COMMENT ON COLUMN accounts.account_type IS 'Account type: ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE';
COMMENT ON COLUMN accounts.account_category IS 'Subcategory for reporting (e.g., CURRENT_ASSET)';
COMMENT ON COLUMN accounts.normal_balance IS 'Normal balance: DEBIT or CREDIT';
COMMENT ON COLUMN accounts.is_default IS 'True if this is a default account created automatically';
COMMENT ON COLUMN accounts.is_system_account IS 'True if this account cannot be deleted by users';
COMMENT ON FUNCTION create_default_accounts(UUID) IS 'Creates all default accounts for an organization';
COMMENT ON FUNCTION get_ar_account(UUID) IS 'Returns the Accounts Receivable account ID for an organization';
COMMENT ON FUNCTION get_ap_account(UUID) IS 'Returns the Accounts Payable account ID for an organization';
COMMENT ON FUNCTION get_cash_account(UUID) IS 'Returns the Cash account ID for an organization';
