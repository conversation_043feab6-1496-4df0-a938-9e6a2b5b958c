# Normalized Columns Implementation for DeepLedger AI Accounting System

## Overview
This document outlines all database tables that should implement normalized columns to prevent duplicates and improve search accuracy in our AI-driven accounting system.

## Problem Statement
- AI agents create duplicates like "Raju" vs "raju" vs "RAJU Corp."
- Case sensitivity, spacing, and punctuation variations cause search failures
- Manual duplicate cleanup is time-consuming and error-prone

## Solution: Database-Level Normalized Columns
Add auto-generated normalized columns that store cleaned versions of user input for consistent searching and duplicate detection.

---

## Tables Requiring Normalized Columns

### 1. **CUSTOMERS** (High Priority)
**Current Issues:** "John Smith Corp" vs "john smith corp." vs "JOHN SMITH CORP"

```sql
ALTER TABLE customers 
ADD COLUMN customer_name_normalized VARCHAR(100) 
GENERATED ALWAYS AS (normalize_name(customer_name)) STORED;

CREATE INDEX idx_customers_name_normalized ON customers(organization_id, customer_name_normalized);
```

**Benefits:**
- Prevents duplicate customer creation
- Improves customer search accuracy
- Essential for invoice/payment processing

---

### 2. **VENDORS** (High Priority)
**Current Issues:** "Microsoft Corp" vs "microsoft corporation" vs "Microsoft Corporation"

```sql
ALTER TABLE vendors 
ADD COLUMN vendor_name_normalized VARCHAR(100) 
GENERATED ALWAYS AS (normalize_name(vendor_name)) STORED;

CREATE INDEX idx_vendors_name_normalized ON vendors(organization_id, vendor_name_normalized);
```

**Benefits:**
- Prevents duplicate vendor creation
- Improves expense categorization
- Critical for bill processing

---

### 3. **ACCOUNTS** (High Priority)
**Current Issues:** "Office Supplies" vs "office supplies" vs "Office Supplies Expense"

```sql
ALTER TABLE accounts 
ADD COLUMN account_name_normalized VARCHAR(100) 
GENERATED ALWAYS AS (normalize_name(account_name)) STORED;

CREATE INDEX idx_accounts_name_normalized ON accounts(organization_id, account_name_normalized);
```

**Benefits:**
- Prevents duplicate chart of accounts entries
- Improves transaction categorization
- Essential for financial reporting accuracy

---

### 4. **PRODUCTS_AND_SERVICES** (High Priority)
**Current Issues:** "Web Design Service" vs "web design service" vs "Web Design Services"

```sql
ALTER TABLE products_and_services 
ADD COLUMN product_service_name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(product_service_name)) STORED;

CREATE INDEX idx_products_services_name_normalized ON products_and_services(organization_id, product_service_name_normalized);
```

**Benefits:**
- Prevents duplicate product/service entries
- Improves inventory management
- Critical for sales/purchase processing

---

### 5. **ORGANIZATIONS** (Medium Priority)
**Current Issues:** "ABC Corp" vs "abc corp" vs "A.B.C. Corporation"

```sql
ALTER TABLE organizations 
ADD COLUMN name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(name)) STORED;

CREATE INDEX idx_organizations_name_normalized ON organizations(name_normalized);
```

**Benefits:**
- Prevents duplicate organization creation
- Improves multi-tenant data integrity

---

### 6. **CLASSES** (Medium Priority)
**Current Issues:** "Marketing Expenses" vs "marketing expenses" vs "Marketing Expense"

```sql
ALTER TABLE classes 
ADD COLUMN class_name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(class_name)) STORED;

CREATE INDEX idx_classes_name_normalized ON classes(organization_id, class_name_normalized);
```

**Benefits:**
- Prevents duplicate class/category creation
- Improves transaction categorization
- Better reporting accuracy

---

### 7. **PROJECTS** (Medium Priority)
**Current Issues:** "Website Redesign" vs "website redesign" vs "Website Re-design"

```sql
ALTER TABLE projects 
ADD COLUMN project_name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(project_name)) STORED;

CREATE INDEX idx_projects_name_normalized ON projects(organization_id, project_name_normalized);
```

**Benefits:**
- Prevents duplicate project creation
- Improves project-based accounting
- Better cost tracking

---

### 8. **TAX_GROUPS** (Medium Priority)
**Current Issues:** "Sales Tax" vs "sales tax" vs "Sales Tax Group"

```sql
ALTER TABLE tax_groups 
ADD COLUMN tax_group_name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(tax_group_name)) STORED;

CREATE INDEX idx_tax_groups_name_normalized ON tax_groups(organization_id, tax_group_name_normalized);
```

**Benefits:**
- Prevents duplicate tax group creation
- Improves tax calculation accuracy

---

### 9. **EMAIL_TEMPLATES** (Low Priority)
**Current Issues:** "Invoice Template" vs "invoice template" vs "Invoice Template - Default"

```sql
ALTER TABLE email_templates 
ADD COLUMN name_normalized VARCHAR(100) 
GENERATED ALWAYS AS (normalize_name(name)) STORED;

CREATE INDEX idx_email_templates_name_normalized ON email_templates(name_normalized);
```

**Benefits:**
- Prevents duplicate template creation
- Improves template management

---

### 10. **USERS** (Low Priority)
**Current Issues:** "John Smith" vs "john smith" (first_name + last_name combination)

```sql
ALTER TABLE users 
ADD COLUMN full_name_normalized VARCHAR(255) 
GENERATED ALWAYS AS (normalize_name(COALESCE(first_name, '') || ' ' || COALESCE(last_name, ''))) STORED;

CREATE INDEX idx_users_full_name_normalized ON users(full_name_normalized);
```

**Benefits:**
- Prevents duplicate user creation
- Improves user search

---

## Implementation Priority

### **Phase 1: Critical Tables (Implement First)**
1. **customers** - Highest impact on AI agent accuracy
2. **vendors** - Critical for expense processing
3. **accounts** - Essential for financial integrity
4. **products_and_services** - Important for sales/inventory

### **Phase 2: Supporting Tables**
5. **organizations** - Multi-tenant data integrity
6. **classes** - Transaction categorization
7. **projects** - Project-based accounting

### **Phase 3: Administrative Tables**
8. **tax_groups** - Tax processing
9. **email_templates** - Template management
10. **users** - User management

---

## Database Functions Required

### Core Normalization Function
```sql
CREATE OR REPLACE FUNCTION normalize_name(input_name TEXT)
RETURNS TEXT
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
  IF input_name IS NULL THEN
    RETURN NULL;
  END IF;
  
  -- Normalize the name:
  -- 1. Convert to lowercase
  -- 2. Trim whitespace
  -- 3. Replace multiple spaces with single space
  -- 4. Remove common punctuation
  -- 5. Remove common business suffixes
  RETURN TRIM(
    REGEXP_REPLACE(
      REGEXP_REPLACE(
        REGEXP_REPLACE(
          LOWER(TRIM(input_name)),
          '[.,;:!?()"\-]', '', 'g'
        ),
        '\s+', ' ', 'g'
      ),
      '\s+(inc|corp|corporation|ltd|limited|llc|llp|co|company)\s*$', '', 'g'
    )
  );
END;
$$;
```

### Fuzzy Search Function
```sql
CREATE OR REPLACE FUNCTION search_customers_fuzzy(
  p_search_term TEXT,
  p_organization_id UUID,
  p_min_similarity NUMERIC DEFAULT 0.3,
  p_limit INTEGER DEFAULT 10,
  p_is_active BOOLEAN DEFAULT TRUE
)
RETURNS TABLE(
  customer_id UUID,
  customer_name VARCHAR(100),
  customer_email VARCHAR(100),
  customer_phone VARCHAR(20),
  customer_address TEXT,
  is_active BOOLEAN,
  organization_id UUID,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  similarity_score NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.customer_id,
    c.customer_name,
    c.customer_email,
    c.customer_phone,
    c.customer_address,
    c.is_active,
    c.organization_id,
    c.created_at,
    c.updated_at,
    similarity(c.customer_name_normalized, normalize_name(p_search_term)) as similarity_score
  FROM customers c
  WHERE c.organization_id = p_organization_id
    AND (p_is_active IS NULL OR c.is_active = p_is_active)
    AND similarity(c.customer_name_normalized, normalize_name(p_search_term)) >= p_min_similarity
  ORDER BY similarity_score DESC
  LIMIT p_limit;
END;
$$;
```

---

## Expected Impact

### **Duplicate Reduction**
- **90%+ reduction** in duplicate entities
- **Improved data quality** across all tables
- **Reduced manual cleanup** time

### **Search Accuracy**
- **95%+ search accuracy** for AI agents
- **Faster entity resolution** in workflows
- **Better user experience** with consistent results

### **Performance Benefits**
- **Indexed normalized columns** = faster searches
- **Reduced database size** (fewer duplicates)
- **Better query optimization**

---

## Next Steps

1. **Review and approve** this implementation plan
2. **Implement Phase 1** tables first (customers, vendors, accounts, products_and_services)
3. **Update AI agent tools** to use normalized search
4. **Test with existing data** to validate improvements
5. **Roll out Phase 2 and 3** incrementally

---

## Maintenance Notes

- **Normalized columns are auto-maintained** by the database
- **No application code changes** required for basic functionality
- **Indexes need periodic maintenance** like any database index
- **Monitor query performance** after implementation
