import { NodeSD<PERSON> } from '@opentelemetry/sdk-node';
import { Resource } from '@opentelemetry/resources';
import { ATTR_SERVICE_NAME, ATTR_SERVICE_VERSION } from '@opentelemetry/semantic-conventions';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { ConsoleSpanExporter } from '@opentelemetry/sdk-trace-node';
import { config } from 'dotenv';

// Load environment variables
config({ override: true });

// Initialize OpenTelemetry SDK
export function register() {
  const sdk = new NodeSDK({
    resource: new Resource({
      [ATTR_SERVICE_NAME]: 'deepledger-ai',
      [ATTR_SERVICE_VERSION]: '1.0.0',
    }),
    
    // Auto-instrumentations for common libraries
    instrumentations: [
      getNodeAutoInstrumentations({
        // Disable some instrumentations that might be too verbose
        '@opentelemetry/instrumentation-fs': {
          enabled: false,
        },
        '@opentelemetry/instrumentation-http': {
          enabled: true,
          requestHook: (span, request) => {
            // Add custom attributes to HTTP spans
            span.setAttributes({
              'deepledger.request.path': request.url || '',
              'deepledger.request.method': request.method || '',
            });
          },
        },
        '@opentelemetry/instrumentation-express': {
          enabled: true,
        },
        '@opentelemetry/instrumentation-pg': {
          enabled: true,
        },
      }),
    ],
    
    // Configure trace exporter
    traceExporter: new ConsoleSpanExporter(),
    
    // Configure sampling
    // In production, you might want to use a different sampler
    // sampler: new TraceIdRatioBasedSampler(0.1), // Sample 10% of traces
  });

  // Start the SDK
  sdk.start();
  
  console.log('🔍 OpenTelemetry instrumentation initialized for DeepLedger');
  
  // Graceful shutdown
  process.on('SIGTERM', () => {
    sdk.shutdown()
      .then(() => console.log('🔍 OpenTelemetry terminated'))
      .catch((error) => console.log('🔍 Error terminating OpenTelemetry', error))
      .finally(() => process.exit(0));
  });
}
