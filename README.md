# DeepLedger AI - Standalone Backend

DeepLedger is an AI-powered accounting assistant that helps non-accountants record financial transactions in plain English into a double-entry accounting system. Built with Mastra.ai and Supabase, DeepLedger understands natural language descriptions of financial activities and converts them into proper accounting entries.

## 🚀 Standalone Mode

This backend now operates with **dynamic runtime context**, meaning:
- ✅ **No frontend authentication required** - Perfect for API-only usage
- ✅ **Dynamic runtime context** - Configure user and organization context via Mastra playground
- ✅ **Immediate testing** - Start using the agent right away with custom context
- ✅ **Mastra playground included** - Interactive web interface at `http://localhost:4111`
- ✅ **Production-ready API** - Full REST API for integration with any frontend


## Features

- **Natural Language Processing**: Describe transactions in plain English, and DeepLedger will handle the accounting details
- **Double-Entry Accounting**: Automatically creates balanced debit and credit entries for each transaction
- **Multiple Transaction Types**:
  - Sales Invoice
  - Sales Receipt
  - Customer Payment
  - Customer Refund
  - Bill
  - Expense
  - Vendor Payment
  - Vendor Refund
  - Journal Entry
- **Customer & Vendor Management**: Create and manage customer and vendor records
- **Account Management**: View and manage the chart of accounts
- **Item & Inventory Management**:
  - Create and manage products and services
  - Track inventory items with purchase and sale prices
  - Organize items with hierarchical categories
- **Tax Management**:
  - Define tax rates (percentage or fixed amount)
  - Create tax groups with multiple tax rates
  - Apply taxes to transactions
- **Class Tracking**: Segment transactions by department, location, or project
- **Financial Reporting**:
  - Trial Balance
  - Profit and Loss Statement (Income Statement)
  - Balance Sheet
  - Account Transaction History
- **Transaction History**: Retrieve and analyze past transactions
- **Educational**: Learn accounting principles through DeepLedger's explanations
- **Memory**: Remembers user preferences, common accounts, and recent transactions

## Technology Stack

- **Mastra.ai**: Framework for building AI agents (v0.10.0)
- **Anthropic Claude**: Advanced language model for natural language understanding (Claude 3.5 Sonnet Latest)
- **OpenAI**: Embeddings for semantic search in memory system (text-embedding-3-small)
- **Supabase**: PostgreSQL database for storing accounting data and vector storage
- **TypeScript**: Type-safe programming language for reliability
- **AI SDK**: Integration with AI models (@ai-sdk/anthropic, @ai-sdk/openai)
- **Zod**: Schema validation for type safety and data integrity

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or pnpm

### Quick Start (Standalone Mode)

1. Clone the repository:
   ```bash
   git clone https://github.com/naga-deepledger/deepledger-ai.git
   cd deepledger-ai
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. **Environment variables are pre-configured!** The `.env` file includes:
   - Supabase credentials for data storage
   - Anthropic API key for the AI agent
   - OpenAI API key for embeddings/memory

4. Start the development server:
   ```bash
   npm run dev
   ```

5. **Start using DeepLedger immediately:**
   - **Mastra Playground**: Visit `http://localhost:4111` for interactive chat with runtime context configuration
   - **REST API**: Use `http://localhost:4111/api/agents/deepLedgerAgent/generate` for API calls

### Runtime Context Configuration

The agent now uses dynamic runtime context instead of hardcoded values. You can configure:
- **User ID**: Unique identifier for the user
- **Organization ID**: Unique identifier for the organization
- **User Name**: Display name for the user
- **User Email**: Email address for the user
- **Organization Name**: Display name for the organization

Configure these values in the Mastra playground's runtime context section.

### API Usage Example

```bash
curl -X POST http://localhost:4111/api/agents/deepLedgerAgent/generate \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      { "role": "user", "content": "Record a $500 sale to ABC Company for consulting services" }
    ],
    "runtimeContext": {
      "userId": "user-123",
      "organizationId": "org-456",
      "userName": "John Doe",
      "userEmail": "<EMAIL>",
      "organizationName": "ABC Company"
    }
  }'
```

### Custom Setup (Optional)

If you want to use your own Supabase instance:

1. Create a new Supabase project
2. The database schema is already implemented directly in Supabase
3. Update the `.env` file with your Supabase credentials
4. Update the organization ID in `.env` to match your data

## Implementation Status

DeepLedger is a fully functional AI accounting assistant with the following implemented features:

### ✅ Fully Implemented
- **Complete Tool Suite**: All 95+ tools are implemented and functional
- **Workflow System**: Advanced workflow engine with intelligent account creation workflow
- **Double-Entry Accounting**: Full support for balanced debit/credit transactions
- **Transaction Types**: Sales invoices, receipts, payments, bills, expenses, journal entries
- **Entity Management**: Customers, vendors, items, accounts, classes, projects, tax rates
- **Financial Reporting**: Trial balance, balance sheet, profit & loss statements with optimized versions
- **Memory System**: Conversation history, semantic recall, and working memory
- **Payment Processing**: Customer and vendor payment applications
- **Outstanding Tracking**: Monitor unpaid invoices and bills
- **Tax Management**: Complex tax rates and tax groups
- **Item Categories**: Hierarchical organization of products and services
- **Project Tracking**: Comprehensive project management and tracking capabilities

### 🔧 Core Architecture
- **Mastra.ai Framework**: Latest version (0.10.0) with full agent capabilities
- **Supabase Integration**: PostgreSQL database with vector storage for memory
- **TypeScript**: Fully typed codebase for reliability
- **Anthropic Claude**: Claude 3.5 Sonnet for natural language processing
- **OpenAI Embeddings**: For semantic search in conversation history

### 🎯 Ready for Production
The system is production-ready with:
- Comprehensive error handling and retry logic
- Transaction logging and monitoring
- Data validation and duplicate detection
- Multi-organization support (schema ready)
- Extensible tool architecture

## Project Structure

```
deepledger-ai/
├── src/
│   ├── mastra/
│   │   ├── agents/
│   │   │   ├── deepLedgerAgent.ts # DeepLedger agent definition
│   │   │   └── index.ts         # Export all agents
│   │   ├── memory/
│   │   │   └── index.ts         # Memory configuration
│   │   ├── storage/
│   │   │   └── index.ts         # Storage configuration
│   │   ├── workflows/
│   │   │   ├── create-new-account/  # Intelligent account creation workflow
│   │   │   │   ├── index.ts         # Main workflow definition
│   │   │   │   ├── steps/           # Workflow steps
│   │   │   │   │   ├── search-existing-accounts-1.ts # Step 1: Search for duplicates
│   │   │   │   │   ├── create-or-inform-2.ts         # Step 2: Create or inform
│   │   │   │   │   └── index.ts     # Export all steps
│   │   │   │   ├── schemas/         # Type definitions and validation
│   │   │   │   │   ├── input.ts     # Input schemas
│   │   │   │   │   ├── output.ts    # Output schemas
│   │   │   │   │   └── index.ts     # Export all schemas
│   │   │   │   └── utils/           # Workflow utilities
│   │   │   │       ├── account-code-generator.ts # Smart code generation
│   │   │   │       └── index.ts     # Export all utilities
│   │   │   └── index.ts             # Export all workflows
│   │   ├── tools/
│   │   │   ├── accounts.ts      # Account-related tools
│   │   │   ├── classes.ts       # Class tracking tools
│   │   │   ├── projects.ts      # Project tracking tools
│   │   │   ├── customers.ts     # Customer-related tools
│   │   │   ├── products_and_services.ts # Product and service management tools
│   │   │   ├── tax_rates.ts     # Tax rate tools
│   │   │   ├── tax_groups.ts    # Tax group tools
│   │   │   ├── tax_group_items.ts # Tax group item tools
│   │   │   ├── record_transactions.ts  # Transaction recording tools
│   │   │   ├── payment_transactions.ts # Payment processing tools
│   │   │   ├── vendors.ts       # Vendor-related tools
│   │   │   ├── get_transactions.ts # Transaction retrieval tools
│   │   │   ├── get_outstanding_invoices.ts # Outstanding invoice tools
│   │   │   ├── get_outstanding_bills.ts # Outstanding bill tools
│   │   │   ├── get_account_balance.ts # Account balance tool
│   │   │   ├── get_account_balance_optimized.ts # Optimized balance tools
│   │   │   ├── get_balance_sheet.ts # Balance sheet tool
│   │   │   ├── get_profit_and_loss_account.ts # P&L tool
│   │   │   ├── get_trial_balance.ts # Trial balance tool
│   │   │   ├── get_trial_balance_optimized.ts # Optimized trial balance tools
│   │   │   ├── create_new_account_workflow_tool.ts # Workflow-based account creation
│   │   │   └── index.ts         # Export all tools
│   │   ├── evals/
│   │   │   └── index.ts         # Evaluation configuration
│   │   ├── voice/
│   │   │   └── index.ts         # Voice configuration (placeholder)
│   │   └── index.ts             # Initialize Mastra
│   ├── services/
│   │   └── balanceService.ts    # Balance calculation service
│   ├── utils/
│   │   └── supabase.ts          # Supabase client
│   └── index.ts                 # Main entry point
├── database/
│   ├── schema.sql               # Complete database schema
│   └── migrations/
│       └── add_projects_table.sql # Project table migration
├── docs/
│   ├── OBSERVABILITY.md         # Observability and monitoring guide
│   ├── WORKFLOW_AS_TOOL_COMPREHENSIVE_BEST_PRACTICES.md # Workflow best practices
│   ├── MANUAL_ACCOUNTING_LINES_ARCHITECTURE.md # Manual accounting lines architecture
│   ├── record_transaction_workflow.md # Transaction workflow documentation
│   ├── record_transaction_workflow_part2.md # Extended workflow docs
│   └── workflow_usage_examples.md # Workflow usage examples
├── tests/
│   └── evals/                   # Evaluation tests
├── .env                         # Environment variables
├── package.json                 # Project dependencies
├── tsconfig.json                # TypeScript configuration
├── instrumentation.ts           # OpenTelemetry instrumentation
├── TOOLS_ASSESSMENT_DOCUMENT.md # Tool assessment documentation
└── TOOLS_SUMMARY_TABLE.md       # Tool summary table
```

## Workflow System

DeepLedger now includes an advanced workflow system built on Mastra.ai's workflow engine. Workflows provide multi-step, intelligent processing with enhanced error handling, validation, and business logic.

### Workflow Organization

Workflows are organized in a structured directory pattern for scalability and maintainability:

```
src/mastra/workflows/
├── workflow-name/              # Each workflow has its own directory
│   ├── index.ts               # Main workflow definition
│   ├── steps/                 # Workflow steps with numbered sequence
│   │   ├── step-name-1.ts     # Step 1 (numbered for clear sequence)
│   │   ├── step-name-2.ts     # Step 2
│   │   └── index.ts           # Export all steps
│   ├── schemas/               # Type definitions and validation
│   │   ├── input.ts           # Input schemas and types
│   │   ├── output.ts          # Output schemas and types
│   │   └── index.ts           # Export all schemas
│   └── utils/                 # Workflow-specific utilities
│       ├── helper-functions.ts # Utility functions
│       └── index.ts           # Export all utilities
└── index.ts                   # Export all workflows
```

### Current Workflows

#### Create New Account Workflow (`create-new-account/`)

An intelligent account creation workflow that provides:

- **Duplicate Detection**: Comprehensive search for existing accounts by name and code
- **Smart Code Generation**: Automatic account code generation based on account type
- **Enhanced Validation**: Type-safe input validation with detailed error messages
- **Conflict Resolution**: Clear feedback when duplicates are found
- **Multi-step Processing**: Separate steps for search and creation with proper error handling

**Workflow Steps:**
1. **Search Existing Accounts** (`search-existing-accounts-1.ts`): Searches for potential duplicates using fuzzy matching
2. **Create or Inform** (`create-or-inform-2.ts`): Either creates the account or provides detailed duplicate information

**Workflow Structure:**
- **Main Definition**: `create-new-account/index.ts`
- **Input Schemas**: `create-new-account/schemas/input.ts`
- **Output Schemas**: `create-new-account/schemas/output.ts`
- **Utilities**: `create-new-account/utils/account-code-generator.ts`

**Usage via Tool**: The workflow is exposed through the `createNewAccountWorkflowTool` which can be called by the AI agent.

### Workflow Architecture

Workflows in DeepLedger follow Mastra.ai best practices:

- **Type Safety**: Full TypeScript support with Zod schemas
- **Error Handling**: Comprehensive error handling at each step
- **Reusability**: Workflows can be called from tools or other workflows
- **Observability**: Full tracing and logging support
- **Runtime Context**: Dynamic context handling for multi-tenant support

### Adding New Workflows

To add a new workflow following the organized structure:

1. **Create the workflow directory structure:**
   ```bash
   mkdir src/mastra/workflows/your-workflow-name
   mkdir src/mastra/workflows/your-workflow-name/steps
   mkdir src/mastra/workflows/your-workflow-name/schemas
   mkdir src/mastra/workflows/your-workflow-name/utils
   ```

2. **Define schemas first** (`schemas/input.ts` and `schemas/output.ts`)
3. **Create workflow steps** with numbered files (`steps/step-name-1.ts`, `steps/step-name-2.ts`)
4. **Create the main workflow** (`index.ts`)
5. **Export from subdirectories** (each subdirectory needs an `index.ts`)
6. **Update main workflows index** (`workflows/index.ts`)
7. **Optionally create a tool wrapper** in `tools/` to expose the workflow to the agent

**Example Directory Structure:**
```
src/mastra/workflows/process-invoice/
├── index.ts                    # Main workflow definition
├── steps/
│   ├── validate-invoice-1.ts   # Step 1: Validate invoice data
│   ├── process-payment-2.ts    # Step 2: Process payment
│   ├── update-records-3.ts     # Step 3: Update accounting records
│   └── index.ts               # Export all steps
├── schemas/
│   ├── input.ts               # Input validation schemas
│   ├── output.ts              # Output schemas
│   └── index.ts               # Export all schemas
└── utils/
    ├── invoice-validator.ts    # Invoice validation utilities
    └── index.ts               # Export all utilities
```

**Example Step Implementation:**
```typescript
// steps/validate-invoice-1.ts
import { createStep } from '@mastra/core/workflows';
import { z } from 'zod';

export const validateInvoiceStep = createStep({
  id: 'validateInvoice',
  description: 'Validate invoice data and check for duplicates',
  inputSchema: z.object({
    invoiceNumber: z.string(),
    amount: z.number(),
    customerId: z.string().uuid(),
  }),
  outputSchema: z.object({
    isValid: z.boolean(),
    validationErrors: z.array(z.string()),
    invoiceData: z.object({
      invoiceNumber: z.string(),
      amount: z.number(),
      customerId: z.string().uuid(),
    }),
  }),
  execute: async ({ inputData }) => {
    // Your validation logic here
    return {
      isValid: true,
      validationErrors: [],
      invoiceData: inputData,
    };
  },
});
```

**Example Main Workflow:**
```typescript
// index.ts
import { createWorkflow } from '@mastra/core/workflows';
import { WorkflowInputSchema, WorkflowOutputSchema } from './schemas/index.js';
import { validateInvoiceStep, processPaymentStep, updateRecordsStep } from './steps/index.js';

const workflow = createWorkflow({
  id: 'processInvoiceWorkflow',
  description: 'Process invoice with validation and payment handling',
  inputSchema: WorkflowInputSchema,
  outputSchema: WorkflowOutputSchema,
  steps: [validateInvoiceStep, processPaymentStep, updateRecordsStep],
});

workflow
  .then(validateInvoiceStep)
  .map({
    // Map data between steps
    isValid: { step: validateInvoiceStep, path: 'isValid' },
    invoiceData: { step: validateInvoiceStep, path: 'invoiceData' },
  })
  .then(processPaymentStep)
  .then(updateRecordsStep)
  .commit();

export const processInvoiceWorkflow = workflow;
```

## Tool Organization

DeepLedger uses a flat organization for its tools, making the codebase simple and easy to navigate. Each tool file is responsible for a specific domain of functionality.

### Current Tool Structure

- **Account Management**: `accounts.ts`
  - `getAccounts`: Retrieve all accounts for an organization
  - `createAccount`: Create new accounts in the chart of accounts
  - `updateAccount`: Update existing account information
  - `getAccountByCode`: Find accounts by their numeric code
  - `searchAccountsByName`: Search accounts by name

- **Class Tracking**: `classes.ts`
  - `getClasses`: Retrieve all classes for business segment tracking
  - `createClass`: Create new classes for departments, locations, or projects
  - `updateClass`: Update existing class information

- **Project Tracking**: `projects.ts`
  - `getProjects`: Retrieve all projects for business initiative tracking
  - `createProject`: Create new projects for campaigns, initiatives, or specific work segments
  - `updateProject`: Update existing project information
  - `searchProjects`: Search projects by name
  - `getHierarchicalProjects`: Get projects in hierarchical tree structure

- **Customer Management**: `customers.ts`
  - `getCustomers`: Retrieve all customers for an organization
  - `createCustomer`: Create new customer records
  - `updateCustomer`: Update existing customer information
  - `searchCustomersByName`: Search customers by name

- **Product and Service Management**: `products_and_services.ts`
  - `getProductsAndServices`: Retrieve all products and services with automatic quantity tracking
  - `createProductOrService`: Create new products (physical goods) or services (intangible offerings)
  - `updateProductOrService`: Update existing product/service information including pricing and accounts
  - `searchProductsAndServices`: Search products and services by name
  - `getProductServiceAnalytics`: Get detailed analytics including profit margins and sales performance
  - `getInventoryStatus`: Check current inventory levels, stock status, and inventory values

- **Tax Management**: `tax_rates.ts`, `tax_groups.ts`, and `tax_group_items.ts`
  - `getTaxRates`: Retrieve all tax rates
  - `createTaxRate`: Define new tax rates (percentage or fixed amount)
  - `updateTaxRate`: Update existing tax rates
  - `getTaxGroups`: Retrieve tax groups
  - `createTaxGroup`: Create groups of related tax rates
  - `updateTaxGroup`: Update tax group information
  - `getTaxGroupItems`: Get tax rates in a group
  - `addTaxRateToGroup`: Associate tax rates with groups
  - `removeTaxRateFromGroup`: Remove tax rates from groups
  - `getTaxRatesNotInGroup`: Find available tax rates for a group

- **Transaction Recording**: `record_transactions.ts`
  - `recordTransaction`: Core functionality for recording financial transactions with double-entry accounting

- **Transaction Management**: `get_transactions.ts`, `payment_transactions.ts`
  - `getTransactions`: Retrieve transaction history and details
  - `recordCustomerPayment`: Process customer payments against invoices
  - `recordVendorPayment`: Process vendor payments against bills
  - `getOutstandingInvoices`: Get unpaid customer invoices
  - `getOutstandingBills`: Get unpaid vendor bills

- **Vendor Management**: `vendors.ts`
  - `getVendors`: Retrieve all vendors for an organization
  - `createVendor`: Create new vendor records
  - `updateVendor`: Update existing vendor information
  - `searchVendorsByName`: Search vendors by name

- **Financial Reporting**: Various reporting tools with optimized versions
  - `getAccountBalance`: Retrieve balance for specific accounts
  - `getAccountBalanceOptimized`: High-performance balance retrieval with caching
  - `getMultipleAccountBalances`: Batch account balance retrieval
  - `checkRealTimeDataStatus`: Verify data freshness for reporting
  - `getBalanceSheet`: Generate balance sheet reports
  - `getProfitAndLossAccount`: Generate income statements (P&L)
  - `getTrialBalance`: Generate trial balance reports
  - `getTrialBalanceOptimized`: High-performance trial balance with summary options
  - `getTrialBalanceSummary`: Condensed trial balance view
  - `compareTrialBalances`: Compare trial balances across periods

- **Workflow Tools**: Advanced workflow-based tools
  - `createNewAccountWorkflowTool`: Intelligent account creation with duplicate detection

### Adding New Tools

To add a new tool:

1. Create a new file in `src/mastra/tools/` for your domain (e.g., `bank_accounts.ts`)
2. Implement your tool using the Mastra.ai `createTool` function
3. Export your tool from the file
4. Update the main `tools/index.ts` file to import and export your new tool

Example for adding a bank account tool:

```typescript
// 1. Create src/mastra/tools/bank_accounts.ts
import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

export const createBankAccount = createTool({
  id: 'Create Bank Account',
  description: 'Create a new bank account linked to a GL account',
  inputSchema: z.object({
    name: z.string().describe('Bank account name'),
    accountNumber: z.string().describe('Bank account number'),
    institution: z.string().describe('Banking institution'),
    accountId: z.string().uuid().describe('GL account ID'),
    organizationId: z.string().uuid().describe('Organization ID'),
  }),
  execute: async ({ input }) => {
    const { data, error } = await supabase
      .from('bank_accounts')
      .insert({
        name: input.name,
        account_number: input.accountNumber,
        institution: input.institution,
        account_id: input.accountId,
        organization_id: input.organizationId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create bank account: ${error.message}`);
    }

    return {
      bankAccount: data,
      message: `Bank account "${input.name}" created successfully.`,
    };
  },
});

// 2. Update src/mastra/tools/index.ts
import { createBankAccount } from './bank_accounts.js';

export const tools = {
  // Existing tools...
  createBankAccount,
};
```


## Memory System

DeepLedger uses Mastra.ai's memory system to provide a personalized experience for users. The memory system includes:

- **Conversation History**: Remembers the last 40 messages for context
- **Semantic Recall**: Uses vector search to find relevant past conversations
- **Working Memory**: Tracks important user information such as:
  - User details (name, company, role)
  - Accounting preferences (fiscal year, accounting method, currency)
  - Commonly used accounts
  - Recent transactions

This memory system allows DeepLedger to provide more contextual and personalized responses over time, reducing the need for users to repeat information.

## Storage Configuration

DeepLedger uses Supabase PostgreSQL for both regular storage and vector storage:

- **Regular Storage**: Stores conversation history, threads, and working memory
- **Vector Storage**: Enables semantic search across past conversations

The storage is configured to use a dedicated `mastra` schema within the Supabase database to keep the AI agent data separate from the accounting data.

## Documentation

DeepLedger includes comprehensive documentation in the `docs/` directory:

### Available Documentation

- **`OBSERVABILITY.md`**: Complete guide to monitoring, logging, and observability features
- **`WORKFLOW_AS_TOOL_COMPREHENSIVE_BEST_PRACTICES.md`**: Best practices for implementing workflows as tools
- **`MANUAL_ACCOUNTING_LINES_ARCHITECTURE.md`**: Architecture guide for simplified manual accounting lines approach
- **`record_transaction_workflow.md`**: Documentation for transaction recording workflows
- **`record_transaction_workflow_part2.md`**: Extended transaction workflow documentation
- **`workflow_usage_examples.md`**: Practical examples of workflow usage

### Assessment Documents

- **`TOOLS_ASSESSMENT_DOCUMENT.md`**: Comprehensive assessment of all tools and their capabilities
- **`TOOLS_SUMMARY_TABLE.md`**: Quick reference table of all available tools

### Database Documentation

- **`database/schema.sql`**: Complete database schema with all tables and relationships
- **`database/migrations/`**: Database migration files for schema updates

## Recent Updates

### Database Schema Enhancements
- **Projects Table**: Added comprehensive project tracking with hierarchical support
- **Account Type Details**: Enhanced account classification with detailed sub-types
- **Optimized Indexes**: Improved query performance for financial reporting

### Performance Optimizations
- **Optimized Balance Tools**: High-performance account balance retrieval with caching
- **Batch Operations**: Multi-account balance retrieval for improved efficiency
- **Trial Balance Optimization**: Enhanced trial balance generation with summary options

### Workflow System
- **Intelligent Account Creation**: Advanced workflow for account creation with duplicate detection
- **Multi-step Processing**: Robust error handling and validation across workflow steps
- **Type Safety**: Full TypeScript support with comprehensive Zod schemas