import { createClient } from '@supabase/supabase-js';
import 'dotenv/config';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_KEY || '';

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase credentials. Please check your .env file.');
}

export const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function to get a Supabase query with organization context
export const getOrganizationQuery = (table: string, organizationId: string) => {
  return supabase.from(table).select('*').eq('organization_id', organizationId);
};

// Helper function to extract user context from runtime context
export const getUserContext = (runtimeContext: any) => {
  const organizationId = runtimeContext.get('organizationId') as string;
  const userId = runtimeContext.get('userId') as string;

  if (!organizationId) {
    throw new Error('Organization ID not found in runtime context');
  }
  if (!userId) {
    throw new Error('User ID not found in runtime context');
  }

  return { organizationId, userId };
};

// Helper function for create operations - adds organization_id, created_by, and updated_by
export const getCreateData = (data: any, runtimeContext: any) => {
  const { organizationId, userId } = getUserContext(runtimeContext);
  return {
    ...data,
    organization_id: organizationId,
    created_by: userId,
    updated_by: userId,
  };
};

// Helper function for update operations - adds updated_by and updated_at
export const getUpdateData = (data: any, runtimeContext: any) => {
  const { userId } = getUserContext(runtimeContext);
  return {
    ...data,
    updated_by: userId,
    updated_at: new Date().toISOString(),
  };
};
