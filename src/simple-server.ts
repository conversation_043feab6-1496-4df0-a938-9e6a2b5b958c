import express from 'express';
import cors from 'cors';
import { tools } from './mastra/tools/index.js';
import { config } from 'dotenv';

// Load environment variables
config({ override: true });

const app = express();
const port = process.env.PORT || 4112;

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-mastra-client-type'],
  exposedHeaders: ['Content-Length', 'X-Requested-With'],
  credentials: false,
}));

app.use(express.json());

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json({ status: 'ok', message: 'DeepLedger backend is running' });
});

// List available tools
app.get('/api/tools', (_req, res) => {
  const toolNames = Object.keys(tools);
  res.json({ tools: toolNames });
});

// Execute a tool
(app as any).post('/api/tools/:toolName', async (req: any, res: any) => {
  try {
    const { toolName } = req.params;
    const { args = {}, organizationId, userId } = req.body;

    if (!tools[toolName as keyof typeof tools]) {
      return res.status(404).json({ error: `Tool '${toolName}' not found` });
    }

    const tool = tools[toolName as keyof typeof tools];

    // Use organizationId and userId from request body if provided, otherwise fall back to env vars
    let finalOrganizationId = organizationId || process.env.DEV_ORGANIZATION_ID || '00000000-0000-0000-0000-000000000001';
    let finalUserId = userId || process.env.DEV_USER_ID || '00000000-0000-0000-0000-000000000001';

    // If we have an organizationId, try to find a valid user for that organization
    if (organizationId && (!userId || userId === 'undefined')) {
      try {
        // Import supabase here to avoid circular dependencies
        const { createClient } = await import('@supabase/supabase-js');
        const supabaseUrl = process.env.SUPABASE_URL;
        const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

        if (supabaseUrl && supabaseKey) {
          const supabase = createClient(supabaseUrl, supabaseKey);

          // Find a user associated with this organization
          const { data: orgUsers, error } = await supabase
            .from('organization_users')
            .select('user_id, role')
            .eq('organization_id', organizationId)
            .eq('role', 'admin')
            .limit(1);

          if (!error && orgUsers && orgUsers.length > 0) {
            finalUserId = orgUsers[0].user_id;
            console.log(`🔄 Using organization admin user: ${finalUserId} for organization: ${organizationId}`);
          }
        }
      } catch (dbError) {
        console.warn('⚠️ Could not find organization user, using fallback:', dbError);
      }
    }

    console.log(`🚀 Executing tool ${toolName} with org: ${finalOrganizationId}, user: ${finalUserId}`);

    // Merge args with context for tools that expect context properties directly
    const contextWithArgs = {
      ...args,
      organizationId: finalOrganizationId,
      userId: finalUserId,
    };

    // Execute the tool with proper context
    const result = await (tool as any).execute({ context: contextWithArgs });

    res.json({ success: true, data: result });
  } catch (error) {
    console.error(`Error executing tool ${req.params.toolName}:`, error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Simple agent endpoint for chat-like interactions
(app as any).post('/api/agents/DeepLedger/generate', async (req: any, res: any) => {
  try {
    const { messages } = req.body;
    
    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ error: 'Messages array is required' });
    }
    
    const lastMessage = messages[messages.length - 1];
    const userMessage = lastMessage?.content || '';
    
    // Simple response for now - in a full implementation, this would use the AI model
    const response = {
      text: `I received your message: "${userMessage}". This is a simplified response from the DeepLedger backend. The tools are available at /api/tools endpoints.`,
      toolCalls: []
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error in agent generate:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
});

// Stream endpoint (simplified)
(app as any).post('/api/agents/DeepLedger/stream', async (req: any, res: any) => {
  try {
    const { messages } = req.body;
    
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Transfer-Encoding', 'chunked');
    
    const lastMessage = messages[messages.length - 1];
    const userMessage = lastMessage?.content || '';
    
    // Simple streaming response
    const response = `data: {"type": "text", "content": "I received your message: \\"${userMessage}\\". This is a streaming response from the DeepLedger backend."}\n\n`;
    
    res.write(response);
    res.end();
  } catch (error) {
    console.error('Error in agent stream:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
});

// Start server
app.listen(port, () => {
  console.log(`🚀 DeepLedger backend server is running on port ${port}`);
  console.log(`🌐 Health check: http://localhost:${port}/health`);
  console.log(`🔧 Tools API: http://localhost:${port}/api/tools`);
  console.log(`🤖 Agent API: http://localhost:${port}/api/agents/DeepLedger/generate`);
  console.log(`📊 Available tools: ${Object.keys(tools).length}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});
