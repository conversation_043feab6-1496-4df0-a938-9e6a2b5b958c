import { mastra } from './mastra/index.js';

async function startServer() {
  try {
    console.log('🚀 Starting DeepLedger Mastra server...');
    
    // Start the Mastra server
    await mastra.serve();
    
    console.log(`✅ DeepLedger server is running on port ${process.env.PORT || 4111}`);
    console.log(`🌐 Mastra playground available at: http://localhost:${process.env.PORT || 4111}`);
    console.log(`🔧 API endpoints available at: http://localhost:${process.env.PORT || 4111}/api`);
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});

// Start the server
startServer();
