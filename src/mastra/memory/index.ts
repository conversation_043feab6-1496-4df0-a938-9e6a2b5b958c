import { Memory } from '@mastra/memory';
import { TokenLimiter, ToolCallFilter } from '@mastra/memory/processors';
import { openai } from '@ai-sdk/openai';
import { mastraStorage, vectorStorage } from '../storage/index.js';

// Optimized memory configuration following Mastra best practices
export const agentMemory = new Memory({
  storage: mastraStorage,
  vector: vectorStorage,
  embedder: openai.embedding('text-embedding-3-small'),

  // Add memory processors for better performance
  processors: [
    // Filter out verbose tool calls to save tokens
    new ToolCallFilter({ exclude: ['getTransactionStats'] }),
    // Ensure we don't exceed <PERSON>'s context window (200k tokens)
    new TokenLimiter(180000), // Leave some buffer for new messages
  ],

  options: {
    // Optimized for accounting context - keep more recent messages
    lastMessages: 30,

    // Semantic recall configuration for accounting context
    semanticRecall: {
      topK: 3, // Retrieve 3 most similar messages for better context
      messageRange: 2, // Include 2 messages before and after each match
    },

    // Thread configuration
    threads: {
      generateTitle: true,
    },

    // Enhanced working memory template for accounting context
    workingMemory: {
      enabled: true,
      template: `
# User Profile
- Name:
- Company:
- Role:
- Preferred Currency:
- Timezone:

# Accounting Preferences
- Fiscal Year End:
- Default Payment Terms:
- Tax Jurisdiction:
- Chart of Accounts Style:

# Current Session Context
- Last Topic:
- Active Transaction Type:
- Current Amount Being Discussed:

# Frequently Used Accounts
- Primary Revenue Account:
- Primary Expense Account:
- Primary Asset Account:
- Primary Liability Account:
- Default Tax Account:

# Recent Activity Summary
- Last Transaction Date:
- Last Transaction Type:
- Last Transaction Amount:
- Last Customer/Vendor:
`
    }
  }
});
