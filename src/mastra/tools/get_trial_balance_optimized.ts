import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { BalanceService } from '../../services/balanceService.js';

// Define trial balance entry schema
const trialBalanceEntrySchema = z.object({
  account_id: z.string().uuid(),
  code: z.number(),
  name: z.string(),
  type: z.string(),
  debit_balance: z.number(),
  credit_balance: z.number(),
  net_balance: z.number(),
});

// Optimized tool to generate a trial balance report
export const getTrialBalanceOptimized = createTool({
  id: 'Get Trial Balance Optimized',
  description: 'Generate a trial balance report using optimized database functions for significantly better performance. Shows all account balances to verify that debits equal credits. Use this for financial reporting, month-end procedures, or when the user needs to verify the accounting equation is balanced. This optimized version is much faster for organizations with many accounts and transactions.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the trial balance'),
    asOfDate: z.string().optional().describe('Generate trial balance as of this date (YYYY-MM-DD)'),
    includeZeroBalances: z.boolean().optional().default(false).describe('Include accounts with zero balances in the report'),
    accountTypes: z.array(z.enum(['asset', 'liability', 'equity', 'revenue', 'expense'])).optional().describe('Filter by specific account types'),
  }),
  outputSchema: z.object({
    entries: z.array(trialBalanceEntrySchema),
    summary: z.object({
      total_debits: z.number().describe('Total of all debit balances'),
      total_credits: z.number().describe('Total of all credit balances'),
      difference: z.number().describe('Difference between debits and credits'),
      is_balanced: z.boolean().describe('Whether the trial balance is balanced (debits = credits)'),
      account_count: z.number().describe('Number of accounts included in the trial balance'),
    }),
    asOfDate: z.string().describe('Date the trial balance was calculated as of'),
    performanceInfo: z.object({
      usedOptimizedQuery: z.boolean().describe('Whether optimized database functions were used'),
      queryTime: z.string().describe('Time taken for the query'),
      accountsProcessed: z.number().describe('Number of accounts processed'),
    }),
    message: z.string().describe('Summary message about the trial balance'),
  }),
  execute: async ({ context }) => {
    const organizationId = context.organizationId;

    const startTime = Date.now();
    const asOfDate = context.asOfDate || new Date().toISOString().split('T')[0];

    try {
      // Use the optimized BalanceService
      const trialBalanceData = await BalanceService.getTrialBalance(
        organizationId,
        asOfDate,
        context.includeZeroBalances || false
      );

      let filteredEntries = trialBalanceData.entries;

      // Filter by account types if specified
      if (context.accountTypes && context.accountTypes.length > 0) {
        filteredEntries = trialBalanceData.entries.filter(entry => 
          context.accountTypes!.includes(entry.type as any)
        );
      }

      // Recalculate totals for filtered entries
      const total_debits = filteredEntries.reduce((sum, entry) => sum + entry.debit_balance, 0);
      const total_credits = filteredEntries.reduce((sum, entry) => sum + entry.credit_balance, 0);
      const difference = total_debits - total_credits;
      const is_balanced = Math.abs(difference) < 0.01;

      const queryTime = `${Date.now() - startTime}ms`;

      // Generate summary message
      const balanceStatus = is_balanced ? 'balanced' : 'NOT balanced';
      const differenceText = is_balanced ? '' : ` with a difference of $${Math.abs(difference).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      
      const message = `Trial balance as of ${asOfDate} shows ${filteredEntries.length} accounts with total debits of $${total_debits.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} and total credits of $${total_credits.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}. The trial balance is ${balanceStatus}${differenceText}.`;

      return {
        entries: filteredEntries,
        summary: {
          total_debits,
          total_credits,
          difference,
          is_balanced,
          account_count: filteredEntries.length,
        },
        asOfDate,
        performanceInfo: {
          usedOptimizedQuery: true,
          queryTime,
          accountsProcessed: filteredEntries.length,
        },
        message,
      };
    } catch (error: any) {
      throw new Error(`Failed to generate trial balance: ${error.message}`);
    }
  },
});

// Tool to get trial balance summary (faster version with just totals)
export const getTrialBalanceSummary = createTool({
  id: 'Get Trial Balance Summary',
  description: 'Get a quick summary of the trial balance without detailed account listings. Much faster when you only need to verify if the books are balanced.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the trial balance summary'),
    asOfDate: z.string().optional().describe('Generate trial balance summary as of this date (YYYY-MM-DD)'),
  }),
  outputSchema: z.object({
    summary: z.object({
      total_debits: z.number(),
      total_credits: z.number(),
      difference: z.number(),
      is_balanced: z.boolean(),
      account_count: z.number(),
      accounts_with_balances: z.number(),
    }),
    asOfDate: z.string(),
    performanceInfo: z.object({
      queryTime: z.string(),
    }),
    message: z.string(),
  }),
  execute: async ({ context }) => {
    const organizationId = context.organizationId;

    const startTime = Date.now();
    const asOfDate = context.asOfDate || new Date().toISOString().split('T')[0];

    try {
      // Use the optimized BalanceService but only get summary data
      const trialBalanceData = await BalanceService.getTrialBalance(
        organizationId,
        asOfDate,
        false // Don't include zero balances for summary
      );

      const queryTime = `${Date.now() - startTime}ms`;

      const balanceStatus = trialBalanceData.is_balanced ? 'balanced' : 'NOT balanced';
      const differenceText = trialBalanceData.is_balanced ? '' : ` with a difference of $${Math.abs(trialBalanceData.total_debits - trialBalanceData.total_credits).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      
      const message = `Trial balance summary as of ${asOfDate}: ${trialBalanceData.entries.length} accounts with balances, total debits $${trialBalanceData.total_debits.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}, total credits $${trialBalanceData.total_credits.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}. Books are ${balanceStatus}${differenceText}.`;

      return {
        summary: {
          total_debits: trialBalanceData.total_debits,
          total_credits: trialBalanceData.total_credits,
          difference: trialBalanceData.total_debits - trialBalanceData.total_credits,
          is_balanced: trialBalanceData.is_balanced,
          account_count: trialBalanceData.entries.length,
          accounts_with_balances: trialBalanceData.entries.filter(e => e.net_balance !== 0).length,
        },
        asOfDate,
        performanceInfo: {
          queryTime,
        },
        message,
      };
    } catch (error: any) {
      throw new Error(`Failed to generate trial balance summary: ${error.message}`);
    }
  },
});

// Tool to compare trial balances between two dates
export const compareTrialBalances = createTool({
  id: 'Compare Trial Balances',
  description: 'Compare trial balances between two dates to analyze changes in account balances over time.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the trial balance comparison'),
    fromDate: z.string().describe('Start date for comparison (YYYY-MM-DD)'),
    toDate: z.string().describe('End date for comparison (YYYY-MM-DD)'),
    significantChangeThreshold: z.number().optional().default(100).describe('Minimum change amount to highlight (default: $100)'),
  }),
  outputSchema: z.object({
    comparison: z.object({
      fromDate: z.string(),
      toDate: z.string(),
      fromSummary: z.object({
        total_debits: z.number(),
        total_credits: z.number(),
        is_balanced: z.boolean(),
      }),
      toSummary: z.object({
        total_debits: z.number(),
        total_credits: z.number(),
        is_balanced: z.boolean(),
      }),
      changes: z.object({
        debit_change: z.number(),
        credit_change: z.number(),
        net_change: z.number(),
      }),
    }),
    significantChanges: z.array(z.object({
      account_id: z.string(),
      code: z.number(),
      name: z.string(),
      type: z.string(),
      from_balance: z.number(),
      to_balance: z.number(),
      change: z.number(),
      change_percentage: z.number(),
    })),
    performanceInfo: z.object({
      queryTime: z.string(),
    }),
    message: z.string(),
  }),
  execute: async ({ context }) => {
    const organizationId = context.organizationId;

    const startTime = Date.now();

    try {
      // Get trial balances for both dates
      const [fromTrialBalance, toTrialBalance] = await Promise.all([
        BalanceService.getTrialBalance(organizationId, context.fromDate, false),
        BalanceService.getTrialBalance(organizationId, context.toDate, false),
      ]);

      // Create maps for easier comparison
      const fromBalances = new Map(fromTrialBalance.entries.map(entry => [entry.account_id, entry]));
      const toBalances = new Map(toTrialBalance.entries.map(entry => [entry.account_id, entry]));

      // Find significant changes
      const significantChanges = [];
      const allAccountIds = new Set([...fromBalances.keys(), ...toBalances.keys()]);

      for (const accountId of allAccountIds) {
        const fromEntry = fromBalances.get(accountId);
        const toEntry = toBalances.get(accountId);
        
        const fromBalance = fromEntry?.net_balance || 0;
        const toBalance = toEntry?.net_balance || 0;
        const change = toBalance - fromBalance;

        if (Math.abs(change) >= context.significantChangeThreshold!) {
          const changePercentage = fromBalance !== 0 ? (change / Math.abs(fromBalance)) * 100 : 100;
          
          significantChanges.push({
            account_id: accountId,
            code: (toEntry || fromEntry)!.code,
            name: (toEntry || fromEntry)!.name,
            type: (toEntry || fromEntry)!.type,
            from_balance: fromBalance,
            to_balance: toBalance,
            change,
            change_percentage: changePercentage,
          });
        }
      }

      // Sort by absolute change amount (descending)
      significantChanges.sort((a, b) => Math.abs(b.change) - Math.abs(a.change));

      const queryTime = `${Date.now() - startTime}ms`;

      const debitChange = toTrialBalance.total_debits - fromTrialBalance.total_debits;
      const creditChange = toTrialBalance.total_credits - fromTrialBalance.total_credits;
      const netChange = debitChange - creditChange;

      const message = `Trial balance comparison from ${context.fromDate} to ${context.toDate}: Found ${significantChanges.length} accounts with significant changes (>${context.significantChangeThreshold}). Total debit change: $${debitChange.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}, credit change: $${creditChange.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}.`;

      return {
        comparison: {
          fromDate: context.fromDate,
          toDate: context.toDate,
          fromSummary: {
            total_debits: fromTrialBalance.total_debits,
            total_credits: fromTrialBalance.total_credits,
            is_balanced: fromTrialBalance.is_balanced,
          },
          toSummary: {
            total_debits: toTrialBalance.total_debits,
            total_credits: toTrialBalance.total_credits,
            is_balanced: toTrialBalance.is_balanced,
          },
          changes: {
            debit_change: debitChange,
            credit_change: creditChange,
            net_change: netChange,
          },
        },
        significantChanges,
        performanceInfo: {
          queryTime,
        },
        message,
      };
    } catch (error: any) {
      throw new Error(`Failed to compare trial balances: ${error.message}`);
    }
  },
});
