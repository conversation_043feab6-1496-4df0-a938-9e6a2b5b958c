import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';





// Define transaction status enum for validation
const returnStatusEnum = z.enum([
  'for_review',
  'approved'
]).optional();



// Helper function to validate date format (YYYY-MM-DD)
const isValidDateFormat = (dateString: string): boolean => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;

  const date = new Date(dateString);
  const timestamp = date.getTime();

  if (isNaN(timestamp)) return false;

  return date.toISOString().slice(0, 10) === dateString;
};

// Custom Zod transform for currency amounts - rounds to 2 decimal places
const currencyAmount = z.number()
  .transform(val => Math.round(val * 100) / 100)
  .refine(val => val >= 0, { message: 'Amount must be non-negative' });

// Helper function to check if an entity exists in the database
const entityExists = async (
  table: string,
  idField: string,
  id: string,
  organizationId: string
): Promise<boolean> => {
  const { data, error } = await supabase
    .from(table)
    .select(idField)
    .eq(idField, id)
    .eq('organization_id', organizationId)
    .single();

  if (error || !data) {
    return false;
  }

  return true;
};

// Helper function to check for duplicate sales returns
const isDuplicateSalesReturn = async (
  referenceNumber: string | undefined,
  returnDate: string,
  organizationId: string,
  customerId: string
): Promise<boolean> => {
  if (!referenceNumber) return false; // Can't check for duplicates without a reference number

  const { data, error } = await supabase
    .from('transactions')
    .select('transaction_id')
    .eq('transaction_type', 'sales_return')
    .eq('reference_number', referenceNumber)
    .eq('transaction_date', returnDate)
    .eq('organization_id', organizationId)
    .eq('customer_id', customerId);

  if (error) {
    console.error('Error checking for duplicate sales return:', error);
    return false; // If we can't check, assume it's not a duplicate
  }

  return data && data.length > 0;
};

// Helper function to validate original transaction and get details
const validateOriginalTransaction = async (
  originalTransactionId: string,
  customerId: string,
  organizationId: string
): Promise<{ transaction: any; customerName: string }> => {
  const { data: transaction, error: transactionError } = await supabase
    .from('transactions')
    .select('*')
    .eq('transaction_id', originalTransactionId)
    .eq('organization_id', organizationId)
    .single();

  if (transactionError || !transaction) {
    throw new Error(`Original transaction with ID ${originalTransactionId} not found`);
  }

  // Validate transaction type is returnable
  const returnableTypes = ['sales_invoice', 'sales_receipt'];
  if (!returnableTypes.includes(transaction.transaction_type)) {
    throw new Error(`Cannot create return for transaction type: ${transaction.transaction_type}. Only sales invoices and sales receipts can be returned.`);
  }

  // Validate customer matches
  if (transaction.customer_id !== customerId) {
    throw new Error(`Customer ID mismatch. Return customer (${customerId}) must match original transaction customer (${transaction.customer_id})`);
  }

  // Get customer name for logging
  const { data: customer, error: customerError } = await supabase
    .from('customers')
    .select('customer_name')
    .eq('customer_id', customerId)
    .eq('organization_id', organizationId)
    .single();

  if (customerError || !customer) {
    throw new Error(`Customer with ID ${customerId} not found`);
  }

  return {
    transaction,
    customerName: customer.customer_name
  };
};

// Enhanced accounting line schema that can optionally include product/service details
const enhancedAccountingLineSchema = z.object({
  accountId: z.string().uuid().describe('Account ID for this line'),
  description: z.string().describe('Line item description'),
  debitAmount: currencyAmount.default(0).describe('Debit amount (automatically rounded to 2 decimal places)'),
  creditAmount: currencyAmount.default(0).describe('Credit amount (automatically rounded to 2 decimal places)'),
  classId: z.string().uuid().optional().describe('Class ID for tracking business segments'),
  projectId: z.string().uuid().optional().describe('Project ID for tracking project-based work'),

  // Optional product/service fields for return lines
  productServiceId: z.string().uuid().optional().describe('Product/Service ID (for returned items)'),
  quantity: z.number().positive().optional().describe('Quantity being returned'),
  unitPrice: currencyAmount.optional().describe('Unit price (automatically rounded to 2 decimal places)'),
  discountPercentage: z.number().min(0).max(100).default(0).describe('Discount percentage'),
  discountAmount: currencyAmount.default(0).describe('Discount amount (automatically rounded to 2 decimal places)'),
  taxRateId: z.string().uuid().optional().describe('Tax rate ID'),
  taxRate: z.number().min(0).max(100).default(0).describe('Tax rate percentage (0-100)'),
});

// Define sales return schema for output
const salesReturnSchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.string(),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  document_number: z.string().nullable(),
  description: z.string(),
  customer_id: z.string().uuid(),
  status: z.string(),
  subtotal_amount: z.number().nullable(),
  tax_amount: z.number().nullable(),
  total_amount: z.number(),
  original_transaction_id: z.string().uuid().nullable(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Tool to create a sales return
export const createSalesReturn = createTool({
  id: 'Create Sales Return',
  description: 'Create a sales return (credit memo) when customers return goods or services. This reduces accounts receivable and records the return transaction. IMPORTANT: All monetary amounts (debitAmount, creditAmount, unitPrice, discountAmount, subtotalAmount, taxAmount, totalAmount) are automatically rounded to exactly 2 decimal places to ensure precision and eliminate floating-point errors. WORKFLOW: First search and get customer ID using searchCustomersByName, original transaction ID using getTransactions, account ID for sales returns/revenue using searchAccountsByName, accounts receivable account ID, tax rate ID using getTaxRates/searchTaxRatesByName (if applicable), product/service ID using searchProductsAndServices (if specific products returned), project ID using searchProjects (if applicable), and class ID using searchClassesByName (if applicable). Then use these IDs with user plain English input to record the return transaction. The agent must provide all accounting lines manually (minimum 2 for double-entry). Lines can optionally include product/service details (productServiceId, quantity, unitPrice, taxRateId) for automatic inventory tracking and detailed reporting. Perfect for processing customer returns with full accounting control.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the sales return'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    returnDate: z.string()
      .refine(val => isValidDateFormat(val), {
        message: 'Return date must be in YYYY-MM-DD format',
      })
      .describe('Date of the return (YYYY-MM-DD)'),
    originalTransactionId: z.string().uuid()
      .describe('Original sales invoice or sales receipt transaction ID that is being returned'),
    customerId: z.string().uuid()
      .describe('Customer ID (must match the original transaction customer)'),
    referenceNumber: z.string().optional().describe('Reference or return authorization number'),
    description: z.string()
      .min(3, { message: 'Description must be at least 3 characters' })
      .describe('Description of the return'),
    status: returnStatusEnum.default('approved').describe('Return status (for_review or approved)'),
    subtotalAmount: currencyAmount.optional()
      .describe('Subtotal amount before tax (automatically rounded to 2 decimal places)'),
    taxAmount: currencyAmount.optional()
      .describe('Tax amount (automatically rounded to 2 decimal places)'),
    totalAmount: currencyAmount
      .describe('Total return amount (automatically rounded to 2 decimal places)'),
    taxAccountId: z.string().uuid().optional()
      .describe('Tax account ID (required if there are taxes on the return). Agent must get this from the chart of accounts before calling this tool.'),

    lines: z.array(enhancedAccountingLineSchema)
    .min(2, { message: 'At least 2 line items are required for double-entry accounting' })
    .describe('Accounting line items (must have at least 2 for double-entry). Can optionally include product/service details for returned items. Tax can be specified using taxRate (percentage) or taxRateId (reference).'),
    skipDuplicateCheck: z.boolean().optional().default(false).describe('Skip checking for duplicate returns'),
    maxRetries: z.number().optional().default(1).describe('Maximum number of retries for database operations'),
  }),
  outputSchema: z.object({
    salesReturn: salesReturnSchema,
    returnLines: z.array(z.object({
      transaction_line_id: z.string().uuid(),
      account_id: z.string().uuid(),
      description: z.string(),
      debit_amount: z.number(),
      credit_amount: z.number(),
      class_id: z.string().uuid().nullable(),
      project_id: z.string().uuid().nullable(),
    })),
    message: z.string().describe('Success message with return details'),
    processingTimeMs: z.number().describe('Time taken to process the return'),
  }),
  execute: async ({ context }) => {
    // Extract organization and user IDs from context
    const organizationId = context.organizationId;
    const userId = context.userId;
    // Start timing the return processing
    const startTime = Date.now();
    let maxRetryCount = 0;
    let customerName = '';
    let originalTransactionNumber = '';

    try {
      // Validate that lines are provided
      if (!context.lines || context.lines.length < 2) {
        throw new Error('At least 2 accounting line items are required for double-entry accounting.');
      }

      // Validate that each line has either debit OR credit (not both)
      for (const line of context.lines) {
        if (line.debitAmount > 0 && line.creditAmount > 0) {
          throw new Error('Each line must have either debit OR credit amount, not both');
        }
        if (line.debitAmount === 0 && line.creditAmount === 0) {
          throw new Error('Each line must have either a debit or credit amount');
        }
      }

      // Validate that debits equal credits (with automatic rounding to 2 decimal places)
      const totalDebits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.debitAmount, 0) * 100) / 100;
      const totalCredits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.creditAmount, 0) * 100) / 100;

      if (totalDebits !== totalCredits) {
        throw new Error(`Debits (${totalDebits}) must exactly equal credits (${totalCredits}). No tolerance allowed.`);
      }

      // Validate original transaction and get details
      const { transaction: originalTransaction, customerName: custName } = await validateOriginalTransaction(
        context.originalTransactionId,
        context.customerId,
        organizationId
      );
      customerName = custName;
      originalTransactionNumber = originalTransaction.document_number || originalTransaction.reference_number || 'N/A';

      // Validate return date is not before original transaction date
      if (context.returnDate < originalTransaction.transaction_date) {
        throw new Error(`Return date (${context.returnDate}) cannot be before original transaction date (${originalTransaction.transaction_date})`);
      }

      // Validate that each account exists
      for (const line of context.lines) {
        const accountExists = await entityExists('accounts', 'account_id', line.accountId, organizationId);
        if (!accountExists) {
          throw new Error(`Account with ID ${line.accountId} not found`);
        }

        if (line.classId) {
          const classExists = await entityExists('classes', 'class_id', line.classId, organizationId);
          if (!classExists) {
            throw new Error(`Class with ID ${line.classId} not found`);
          }
        }

        if (line.projectId) {
          const projectExists = await entityExists('projects', 'project_id', line.projectId, organizationId);
          if (!projectExists) {
            throw new Error(`Project with ID ${line.projectId} not found`);
          }
        }
      }

      // Check for duplicate returns if not explicitly skipped
      if (!context.skipDuplicateCheck && context.referenceNumber) {
        const isDuplicate = await isDuplicateSalesReturn(
          context.referenceNumber,
          context.returnDate,
          organizationId,
          context.customerId
        );

        if (isDuplicate) {
          throw new Error(`Duplicate sales return detected with reference number ${context.referenceNumber}`);
        }
      }

      // Validate tax account if provided
      if (context.taxAccountId) {
        const taxAccountExists = await entityExists('accounts', 'account_id', context.taxAccountId, organizationId);
        if (!taxAccountExists) {
          throw new Error(`Tax account with ID ${context.taxAccountId} not found`);
        }
      }

      // Validate lines with product/service information
      const linesWithProducts = context.lines.filter(line => line.productServiceId);
      if (linesWithProducts.length > 0) {
        // Check if any lines have tax rates and ensure tax account is provided
        const hasLinesWithTax = linesWithProducts.some(line => line.taxRateId);
        if (hasLinesWithTax && !context.taxAccountId) {
          throw new Error('Tax account ID is required when lines have tax rates. Please provide taxAccountId parameter.');
        }

        // Validate each product/service exists
        for (const line of linesWithProducts) {
          const { data: productService, error: productServiceError } = await supabase
            .from('products_and_services')
            .select('*')
            .eq('product_service_id', line.productServiceId)
            .eq('organization_id', organizationId)
            .single();

          if (productServiceError || !productService) {
            throw new Error(`Product/Service with ID ${line.productServiceId} not found`);
          }

          // Validate tax rate if provided
          if (line.taxRateId) {
            const { data: taxRate, error: taxRateError } = await supabase
              .from('tax_rates')
              .select('*')
              .eq('tax_rate_id', line.taxRateId)
              .eq('organization_id', organizationId)
              .single();

            if (taxRateError || !taxRate) {
              throw new Error(`Tax rate with ID ${line.taxRateId} not found`);
            }
          }
        }
      }

      // Generate document number using the database function
      const { data: documentNumberResult, error: documentNumberError } = await supabase
        .rpc('generate_document_number', {
          p_transaction_type: 'sales_return',
          p_organization_id: organizationId
        });

      if (documentNumberError) {
        throw new Error(`Failed to generate return number: ${documentNumberError.message}`);
      }

      const documentNumber = documentNumberResult;

      // Start a Supabase transaction with retry logic
      let salesReturn: any;
      let returnError;
      let retryCount = 0;
      const maxRetries = context.maxRetries || 1;

      while (retryCount < maxRetries) {
        const returnData = {
          transaction_type: 'sales_return',
          transaction_date: context.returnDate,
          reference_number: context.referenceNumber,
          document_number: documentNumber,
          description: context.description,
          customer_id: context.customerId,
          status: context.status || 'approved',
          subtotal_amount: context.subtotalAmount || context.totalAmount,
          tax_amount: context.taxAmount || 0,
          total_amount: context.totalAmount,
          original_transaction_id: context.originalTransactionId,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        };

        const result = await supabase
          .from('transactions')
          .insert(returnData)
          .select()
          .single();

        salesReturn = result.data;
        returnError = result.error;

        if (!returnError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (returnError) {
        throw new Error(`Failed to create sales return after ${retryCount} attempts: ${returnError.message}`);
      }

      // Insert transaction lines with retry logic (including optional product/service details)
      const transactionLines = context.lines.map((line: any) => {
        const lineData: any = {
          transaction_id: salesReturn.transaction_id,
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
        };

        // Add product/service fields if provided
        if (line.productServiceId) {
          lineData.product_service_id = line.productServiceId;
          lineData.quantity = line.quantity || null;
          lineData.unit_price = line.unitPrice || null;
          lineData.discount_percentage = line.discountPercentage || 0;
          lineData.discount_amount = line.discountAmount || 0;
          lineData.tax_rate_id = line.taxRateId || null;
          lineData.tax_rate = line.taxRate || 0;
        }

        return {
          ...lineData,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        };
      });

      retryCount = 0;
      let linesError;

      while (retryCount < maxRetries) {
        const result = await supabase
          .from('transaction_lines')
          .insert(transactionLines);

        linesError = result.error;

        if (!linesError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (linesError) {
        throw new Error(`Failed to create return lines after ${retryCount} attempts: ${linesError.message}`);
      }

      // Note: Inventory tracking is now handled automatically by database triggers
      // The products_and_services table tracks quantities automatically when transaction_lines
      // with product_service_id are inserted/updated/deleted via database triggers

      // Update max retry count
      maxRetryCount = Math.max(maxRetryCount, context.maxRetries || 1);



      return {
        salesReturn,
        returnLines: context.lines.map((line: any, index: number) => ({
          transaction_line_id: `line_${index + 1}`, // This would be the actual ID from the database
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
        })),
        message: `Successfully created sales return ${documentNumber} for ${customerName} with ${context.lines.length} line items${linesWithProducts.length > 0 ? ` (${linesWithProducts.length} with product/service details)` : ''}. Original transaction: ${originalTransactionNumber}.`,
        processingTimeMs: Date.now() - startTime
      };

    } catch (error: any) {
      throw error;
    }
  },
});


