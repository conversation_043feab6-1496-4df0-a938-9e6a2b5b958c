import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define transaction type enum for validation
const transactionTypeEnum = z.enum([
  'sales_invoice',
  'sales_receipt',
  'customer_payment',
  'sales_return',           // ✅ Replaces customer_refund
  'customer_cash_refund',   // ✅ New - Cash refund to customer
  'bill',
  'expense',
  'vendor_payment',
  'purchase_return',        // ✅ Replaces vendor_refund
  'vendor_cash_refund',     // ✅ New - Cash refund from vendor
  'journal_entry'
]);

// Define transaction status enum for validation
const transactionStatusEnum = z.enum([
  'for_review',
  'approved'
]);

// Define payment terms enum for validation
const paymentTermsEnum = z.enum([
  'due_on_receipt',
  'net_15',
  'net_30',
  'net_45',
  'net_60',
  'custom'
]).optional();
// Define transaction schema for output
const transactionSchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.enum(['sales_invoice', 'sales_receipt', 'customer_payment', 'sales_return', 'customer_cash_refund', 'bill', 'expense', 'vendor_payment', 'purchase_return', 'vendor_cash_refund', 'journal_entry']),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  description: z.string(),
  customer_id: z.string().uuid().nullable(),
  vendor_id: z.string().uuid().nullable(),
  due_date: z.string().nullable(),
  status: z.enum(['for_review', 'approved']),
  payment_terms: z.string().nullable(),
  subtotal_amount: z.number().nullable(),
  tax_amount: z.number().nullable(),
  total_amount: z.number(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
  lines: z.array(z.any()).optional(),
  invoiceItems: z.array(z.any()).optional(),
  billItems: z.array(z.any()).optional(),
  inventoryTransactions: z.array(z.any()).optional(),
  payments: z.array(z.any()).optional(),
});





// Tool to get transactions with enhanced filtering and aggregation capabilities
export const getTransactions = createTool({
  id: 'Get Transactions',
  description: 'Comprehensive tool to retrieve and analyze all types of financial transactions including sales invoices, receipts, bills, expenses, payments, and journal entries. Use this when you need to display transaction history, find specific transactions, analyze financial data, generate reports, track business activity, or when the user asks about any transactions. This is the primary tool for transaction analysis and supports extensive filtering by date, amount, type, customer/vendor, and includes related data like transaction lines, payments, and detailed customer/vendor information. Essential for financial reporting, audit trails, and business intelligence.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter transactions'),
    // Basic filters
    transactionId: z.string().uuid().optional().describe('Specific transaction ID to retrieve'),
    transactionType: transactionTypeEnum.optional().describe('Filter by transaction type'),
    status: transactionStatusEnum.optional().describe('Filter by transaction status (for_review or approved)'),
    customerId: z.string().uuid().optional().describe('Filter by customer ID'),
    vendorId: z.string().uuid().optional().describe('Filter by vendor ID'),
    // Simple limit parameter to restrict the number of results
    limit: z.number().min(1).max(100).optional().describe('Maximum number of transactions to return'),

    // Date filters
    startDate: z.string().optional().describe('Start date for filtering (YYYY-MM-DD)'),
    endDate: z.string().optional().describe('End date for filtering (YYYY-MM-DD)'),
    dueDateStart: z.string().optional().describe('Start due date for filtering (YYYY-MM-DD)'),
    dueDateEnd: z.string().optional().describe('End due date for filtering (YYYY-MM-DD)'),

    // Amount filters
    minAmount: z.number().optional().describe('Minimum transaction amount'),
    maxAmount: z.number().optional().describe('Maximum transaction amount'),
    minTaxAmount: z.number().optional().describe('Minimum tax amount'),
    maxTaxAmount: z.number().optional().describe('Maximum tax amount'),

    // Text search
    searchTerm: z.string().optional().describe('Search in reference number, description, etc.'),

    // Account and class filters
    accountId: z.string().uuid().optional().describe('Filter by account ID (transactions with lines using this account)'),
    classId: z.string().uuid().optional().describe('Filter by class ID (transactions with lines using this class)'),
    projectId: z.string().uuid().optional().describe('Filter by project ID (transactions with lines using this project)'),

    // Payment terms filter
    paymentTerms: paymentTermsEnum.optional().describe('Filter by payment terms'),

    // Include related data
    includeLines: z.boolean().optional().default(false).describe('Include transaction lines in response'),
    includeItemLines: z.boolean().optional().default(false).describe('Include invoice/bill items in response'),
    includeInventoryTransactions: z.boolean().optional().default(false).describe('Include inventory transactions in response'),
    includePayments: z.boolean().optional().default(false).describe('Include payment information for invoices and bills'),
    includeCustomerVendorDetails: z.boolean().optional().default(false).describe('Include detailed customer/vendor information'),

    // Pagination and sorting
    page: z.number().optional().default(1).describe('Page number for pagination'),
    pageSize: z.number().optional().default(20).describe('Number of transactions per page'),
    sortBy: z.enum(['transaction_date', 'due_date', 'total_amount', 'reference_number']).optional().default('transaction_date').describe('Field to sort by'),
    sortDirection: z.enum(['asc', 'desc']).optional().default('desc').describe('Sort direction'),

    // Aggregation options
    aggregate: z.enum(['none', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly']).optional().default('none').describe('Aggregate transactions by time period'),


  }),
  outputSchema: z.object({
    transactions: z.array(transactionSchema),
    pagination: z.object({
      page: z.number(),
      pageSize: z.number(),
      totalCount: z.number(),
      totalPages: z.number(),
    }).optional(),
    message: z.string().describe('Summary message about the transaction query results and analysis'),
  }),
  execute: async ({ context }) => {
    // Handle aggregation if requested
    if (context.aggregate !== 'none') {
      // For now, we'll return a message that aggregation is not implemented
      // In a future update, we can implement the aggregation functionality
      return {
        transactions: [],
        message: `Aggregation by ${context.aggregate} is not implemented yet. Please use the standard transaction query options with filtering and sorting to analyze transaction data.`
      };
    }

    // Start with a base query
    let query = supabase
      .from('transactions')
      .select('*')
      .eq('organization_id', context.organizationId);

    // Apply basic filters if provided
    if (context.transactionId) {
      query = query.eq('transaction_id', context.transactionId);
    }

    if (context.transactionType) {
      query = query.eq('transaction_type', context.transactionType);
    }

    if (context.status) {
      query = query.eq('status', context.status);
    }

    if (context.customerId) {
      query = query.eq('customer_id', context.customerId);
    }

    if (context.vendorId) {
      query = query.eq('vendor_id', context.vendorId);
    }

    if (context.paymentTerms) {
      query = query.eq('payment_terms', context.paymentTerms);
    }

    // Apply date filters
    if (context.startDate) {
      query = query.gte('transaction_date', context.startDate);
    }

    if (context.endDate) {
      query = query.lte('transaction_date', context.endDate);
    }

    if (context.dueDateStart) {
      query = query.gte('due_date', context.dueDateStart);
    }

    if (context.dueDateEnd) {
      query = query.lte('due_date', context.dueDateEnd);
    }

    // Apply amount filters
    if (context.minAmount) {
      query = query.gte('total_amount', context.minAmount);
    }

    if (context.maxAmount) {
      query = query.lte('total_amount', context.maxAmount);
    }

    if (context.minTaxAmount) {
      query = query.gte('tax_amount', context.minTaxAmount);
    }

    if (context.maxTaxAmount) {
      query = query.lte('tax_amount', context.maxTaxAmount);
    }

    // Apply text search if provided
    if (context.searchTerm) {
      const searchPattern = `%${context.searchTerm}%`;
      query = query.or(`reference_number.ilike.${searchPattern},description.ilike.${searchPattern}`);
    }

    // Apply account, class, and project filters (these require a subquery or join)
    if (context.accountId || context.classId || context.projectId) {
      // Get transaction IDs that match the account, class, or project filter
      let lineQuery = supabase
        .from('transaction_lines')
        .select('transaction_id')
        .eq('organization_id', context.organizationId);

      if (context.accountId) {
        lineQuery = lineQuery.eq('account_id', context.accountId);
      }

      if (context.classId) {
        lineQuery = lineQuery.eq('class_id', context.classId);
      }

      if (context.projectId) {
        lineQuery = lineQuery.eq('project_id', context.projectId);
      }

      const { data: matchingLines, error: lineQueryError } = await lineQuery;

      if (lineQueryError) {
        throw new Error(`Failed to filter by account/class/project: ${lineQueryError.message}`);
      }

      if (matchingLines && matchingLines.length > 0) {
        const matchingTransactionIds = matchingLines.map((line: any) => line.transaction_id);
        query = query.in('transaction_id', matchingTransactionIds);
      } else {
        // No transactions match the account/class/project filter
        return {
          transactions: [],
          pagination: {
            page: context.page,
            pageSize: context.pageSize,
            totalCount: 0,
            totalPages: 0
          },
          message: `No transactions found matching the specified account, class, or project filter criteria.`
        };
      }
    }

    // For pagination, we'll use a simplified approach
    // First, get all transaction IDs that match our criteria to count them
    let countQuery = supabase
      .from('transactions')
      .select('transaction_id')
      .eq('organization_id', context.organizationId);

    // Apply the same filters as the main query
    if (context.transactionId) {
      countQuery = countQuery.eq('transaction_id', context.transactionId);
    }

    if (context.transactionType) {
      countQuery = countQuery.eq('transaction_type', context.transactionType);
    }

    if (context.status) {
      countQuery = countQuery.eq('status', context.status);
    }

    if (context.customerId) {
      countQuery = countQuery.eq('customer_id', context.customerId);
    }

    if (context.vendorId) {
      countQuery = countQuery.eq('vendor_id', context.vendorId);
    }

    if (context.paymentTerms) {
      countQuery = countQuery.eq('payment_terms', context.paymentTerms);
    }

    // Apply date filters to count query
    if (context.startDate) {
      countQuery = countQuery.gte('transaction_date', context.startDate);
    }

    if (context.endDate) {
      countQuery = countQuery.lte('transaction_date', context.endDate);
    }

    if (context.dueDateStart) {
      countQuery = countQuery.gte('due_date', context.dueDateStart);
    }

    if (context.dueDateEnd) {
      countQuery = countQuery.lte('due_date', context.dueDateEnd);
    }

    // Apply amount filters to count query
    if (context.minAmount) {
      countQuery = countQuery.gte('total_amount', context.minAmount);
    }

    if (context.maxAmount) {
      countQuery = countQuery.lte('total_amount', context.maxAmount);
    }

    if (context.minTaxAmount) {
      countQuery = countQuery.gte('tax_amount', context.minTaxAmount);
    }

    if (context.maxTaxAmount) {
      countQuery = countQuery.lte('tax_amount', context.maxTaxAmount);
    }

    // Apply text search to count query
    if (context.searchTerm) {
      const searchPattern = `%${context.searchTerm}%`;
      countQuery = countQuery.or(`reference_number.ilike.${searchPattern},description.ilike.${searchPattern}`);
    }

    // Execute the count query
    const { data: countData, error: countError } = await countQuery;

    if (countError) {
      throw new Error(`Failed to count transactions: ${countError.message}`);
    }

    // Calculate pagination values
    const totalCount = countData ? countData.length : 0;
    const totalPages = Math.ceil(totalCount / context.pageSize);
    const offset = (context.page - 1) * context.pageSize;

    // Apply sorting and pagination
    // If limit is provided, use it instead of pagination
    if (context.limit) {
      query = query
        .order('transaction_date', { ascending: false })
        .limit(context.limit);
    } else {
      query = query
        .order(context.sortBy, { ascending: context.sortDirection === 'asc' })
        .range(offset, offset + context.pageSize - 1);
    }

    // Execute the query
    const { data: transactions, error: transactionsError } = await query;

    if (transactionsError) {
      throw new Error(`Failed to get transactions: ${transactionsError.message}`);
    }

    // If no transactions found, return early with pagination info
    if (!transactions || transactions.length === 0) {
      let message = 'No transactions found';
      if (context.startDate || context.endDate) {
        message += ` for the specified date range`;
      }
      if (context.transactionType) {
        message += ` of type "${context.transactionType}"`;
      }
      if (context.customerId || context.vendorId) {
        message += ` for the specified ${context.customerId ? 'customer' : 'vendor'}`;
      }
      message += '. Try adjusting your filter criteria to find transactions.';

      return {
        transactions: [],
        pagination: {
          page: context.page,
          pageSize: context.pageSize,
          totalCount,
          totalPages
        },
        message
      };
    }

    const transactionIds = transactions.map((t: any) => t.transaction_id);

    // If includeLines is true, fetch the transaction lines for each transaction
    if (context.includeLines) {
      const { data: lines, error: linesError } = await supabase
        .from('transaction_lines')
        .select(`
          *,
          accounts:account_id (account_id, account_code, account_name, account_type),
          classes:class_id (class_id, class_name, description),
          projects:project_id (project_id, project_name, description),
          products_and_services:product_service_id (product_service_id, product_service_name, product_service_type, sku, description, sales_price, purchase_price),
          tax_rates:tax_rate_id (tax_rate_id, tax_rate_name, tax_rate_percentage, tax_rate_type)
        `)
        .in('transaction_id', transactionIds);

      if (linesError) {
        throw new Error(`Failed to get transaction lines: ${linesError.message}`);
      }

      // Group lines by transaction_id
      const linesByTransaction = lines.reduce((acc: any, line: any) => {
        if (!acc[line.transaction_id]) {
          acc[line.transaction_id] = [];
        }
        acc[line.transaction_id].push(line);
        return acc;
      }, {});

      // Add lines to each transaction
      transactions.forEach((transaction: any) => {
        transaction.lines = linesByTransaction[transaction.transaction_id] || [];
      });
    }

    // Note: includeItemLines functionality is now handled through transaction_lines
    // with product_service_id and tax_rate_id references. The separate invoice_items
    // and bill_items tables have been consolidated into the transaction_lines table.

    // Note: includeInventoryTransactions functionality is now handled through transaction_lines
    // with product_service_id references and quantity tracking in the products_and_services table.

    // If includePayments is true, fetch payment information for invoices and bills
    if (context.includePayments) {
      // Get payments for invoices
      const invoiceIds = transactions
        .filter((t: any) => t.transaction_type === 'sales_invoice')
        .map((t: any) => t.transaction_id);

      if (invoiceIds.length > 0) {
        const { data: invoicePayments, error: paymentsError } = await supabase
          .from('invoice_payments')
          .select(`
            *,
            payment_transactions:payment_transaction_id (transaction_id, transaction_type, transaction_date, reference_number, total_amount)
          `)
          .in('transaction_id', invoiceIds);

        if (paymentsError) {
          throw new Error(`Failed to get invoice payments: ${paymentsError.message}`);
        }

        // Group payments by transaction_id
        const paymentsByInvoice = invoicePayments.reduce((acc: any, payment: any) => {
          if (!acc[payment.transaction_id]) {
            acc[payment.transaction_id] = [];
          }
          acc[payment.transaction_id].push(payment);
          return acc;
        }, {});

        // Add payments to each invoice transaction
        transactions
          .filter((t: any) => t.transaction_type === 'sales_invoice')
          .forEach((transaction: any) => {
            transaction.payments = paymentsByInvoice[transaction.transaction_id] || [];
          });
      }

      // Get payments for bills
      const billIds = transactions
        .filter((t: any) => t.transaction_type === 'bill')
        .map((t: any) => t.transaction_id);

      if (billIds.length > 0) {
        const { data: billPayments, error: paymentsError } = await supabase
          .from('bill_payments')
          .select(`
            *,
            payment_transactions:payment_transaction_id (transaction_id, transaction_type, transaction_date, reference_number, total_amount)
          `)
          .in('transaction_id', billIds);

        if (paymentsError) {
          throw new Error(`Failed to get bill payments: ${paymentsError.message}`);
        }

        // Group payments by transaction_id
        const paymentsByBill = billPayments.reduce((acc: any, payment: any) => {
          if (!acc[payment.transaction_id]) {
            acc[payment.transaction_id] = [];
          }
          acc[payment.transaction_id].push(payment);
          return acc;
        }, {});

        // Add payments to each bill transaction
        transactions
          .filter((t: any) => t.transaction_type === 'bill')
          .forEach((transaction: any) => {
            transaction.payments = paymentsByBill[transaction.transaction_id] || [];
          });
      }
    }

    // Fetch customer and vendor information
    const customerIds = transactions
      .filter((t: any) => t.customer_id)
      .map((t: any) => t.customer_id);

    const vendorIds = transactions
      .filter((t: any) => t.vendor_id)
      .map((t: any) => t.vendor_id);

    if (customerIds.length > 0) {
      // Determine what customer fields to fetch based on includeCustomerVendorDetails
      const customerFields = context.includeCustomerVendorDetails
        ? '*'
        : 'customer_id, customer_name, customer_email';

      const { data: customers, error: customersError } = await supabase
        .from('customers')
        .select(customerFields)
        .in('customer_id', customerIds);

      if (customersError) {
        throw new Error(`Failed to get customer information: ${customersError.message}`);
      }

      // Create a map of customer data by ID
      const customerMap = customers.reduce((acc: any, customer: any) => {
        acc[customer.customer_id] = customer;
        return acc;
      }, {});

      // Add customer data to transactions
      transactions
        .filter((t: any) => t.customer_id)
        .forEach((transaction: any) => {
          transaction.customer = customerMap[transaction.customer_id] || null;
        });
    }

    if (vendorIds.length > 0) {
      // Determine what vendor fields to fetch based on includeCustomerVendorDetails
      const vendorFields = context.includeCustomerVendorDetails
        ? '*'
        : 'vendor_id, vendor_name, vendor_email';

      const { data: vendors, error: vendorsError } = await supabase
        .from('vendors')
        .select(vendorFields)
        .in('vendor_id', vendorIds);

      if (vendorsError) {
        throw new Error(`Failed to get vendor information: ${vendorsError.message}`);
      }

      // Create a map of vendor data by ID
      const vendorMap = vendors.reduce((acc: any, vendor: any) => {
        acc[vendor.vendor_id] = vendor;
        return acc;
      }, {});

      // Add vendor data to transactions
      transactions
        .filter((t: any) => t.vendor_id)
        .forEach((transaction: any) => {
          transaction.vendor = vendorMap[transaction.vendor_id] || null;
        });
    }

    // Generate comprehensive summary message
    const totalAmount = transactions.reduce((sum: number, t: any) => sum + (t.total_amount || 0), 0);
    const transactionTypes = [...new Set(transactions.map((t: any) => t.transaction_type))];

    let message = `Found ${transactions.length} transaction(s)`;
    if (totalCount > transactions.length) {
      message += ` (showing ${transactions.length} of ${totalCount} total)`;
    }
    message += ` with total value of $${totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;

    if (transactionTypes.length === 1) {
      message += `. All transactions are of type "${transactionTypes[0]}"`;
    } else if (transactionTypes.length > 1) {
      message += `. Transaction types include: ${transactionTypes.join(', ')}`;
    }

    if (context.startDate || context.endDate) {
      message += ` for the specified date range`;
    }

    if (context.includeLines || context.includePayments) {
      message += '. Detailed transaction data included';
    }

    // Return format depends on whether we're using limit or pagination
    if (context.limit) {
      // Simple format when using limit
      return {
        transactions,
        message
      };
    } else {
      // Full format with pagination info
      return {
        transactions,
        pagination: {
          page: context.page,
          pageSize: context.pageSize,
          totalCount,
          totalPages
        },
        message
      };
    }
  },
});

// Examples of using getTransactions:
// With limit: getTransactions({ limit: 10, status: 'approved', ... })
// For detailed transaction: getTransactions({ transactionId: 'uuid', includeLines: true, includeCustomerVendorDetails: true })
