import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define vendor schema for reuse
const vendorSchema = z.object({
  vendor_id: z.string().uuid(),
  vendor_name: z.string(),
  vendor_email: z.string().nullable(),
  vendor_phone: z.string().nullable(),
  vendor_address: z.string().nullable(),
  is_active: z.boolean(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Define Vendor interface for type safety
interface Vendor {
  vendor_id?: string;
  vendor_name?: string;
  vendor_email?: string;
  vendor_phone?: string;
  vendor_address?: string;
  is_active?: boolean;
  organization_id?: string;
}

// Tool to get vendors
export const getVendors = createTool({
  id: 'Get Vendors',
  description: 'Retrieve vendor information from the vendor database. Use this when you need to display vendors, find vendors for expense recording or bill payments, or when the user asks about vendors. You can filter by active status or get a specific vendor.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter vendors'),
    vendorId: z.string().uuid().optional().describe('Specific vendor ID to retrieve'),
    isActive: z.boolean().optional().describe('Filter by active status'),
  }),
  outputSchema: z.object({
    vendors: z.array(vendorSchema),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('vendors')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.vendorId) {
      query = query.eq('vendor_id', context.vendorId);
    }

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query.order('vendor_name');

    if (error) {
      throw new Error(`Failed to get vendors: ${error.message}`);
    }

    return { vendors: data };
  },
});

// Tool to search for vendors by name
export const searchVendorsByName = createTool({
  id: 'Search Vendors By Name',
  description: 'Search for vendors by name when you need to find specific vendors for expense recording, bill payments, or when the user asks for vendors containing certain text. Use this when you know part of a vendor name but need to find the exact vendor.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter vendors'),
    nameContains: z.string().describe('Text to search for in vendor names'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    limit: z.number().min(1).max(100).default(10).describe('Maximum number of vendors to return'),
  }),
  outputSchema: z.object({
    vendors: z.array(vendorSchema),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('vendors')
      .select('*')
      .ilike('vendor_name', `%${context.nameContains}%`)
      .eq('organization_id', context.organizationId);

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query
      .order('vendor_name')
      .limit(context.limit);

    if (error) {
      throw new Error(`Failed to search for vendors: ${error.message}`);
    }

    return { vendors: data };
  },
});

// Tool to create a vendor
export const createVendor = createTool({
  id: 'Create Vendor',
  description: 'Create a new vendor record in the vendor database. Use this when the user wants to add a new vendor for expense tracking, bill payments, or vendor management. Vendor information helps track expenses and payables.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the vendor'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    vendor_name: z.string().describe('Vendor name'),
    vendor_email: z.string().email().optional().describe('Vendor email'),
    vendor_phone: z.string().optional().describe('Vendor phone number'),
    vendor_address: z.string().optional().describe('Vendor address'),
  }),
  outputSchema: z.object({
    vendor: vendorSchema,
  }),
  execute: async ({ context }) => {
    const { data, error } = await supabase
      .from('vendors')
      .insert({
        vendor_name: context.vendor_name,
        vendor_email: context.vendor_email,
        vendor_phone: context.vendor_phone,
        vendor_address: context.vendor_address,
        organization_id: context.organizationId,
        created_by: context.userId,
        updated_by: context.userId,
        is_active: true,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create vendor: ${error.message}`);
    }

    return { vendor: data };
  },
});

// Tool to update a vendor
export const updateVendor = createTool({
  id: 'Update Vendor',
  description: 'Update an existing vendor record in the vendor database. Use this when the user wants to modify vendor details like contact information, address, or active status. This helps maintain accurate vendor records for expenses and payments.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the vendor'),
    userId: z.string().uuid().describe('User ID for updated_by field'),
    vendorId: z.string().uuid().describe('Vendor ID to update'),
    vendor_name: z.string().optional().describe('Updated vendor name'),
    vendor_email: z.string().email().optional().describe('Updated vendor email'),
    vendor_phone: z.string().optional().describe('Updated vendor phone number'),
    vendor_address: z.string().optional().describe('Updated vendor address'),
    isActive: z.boolean().optional().describe('Update active status'),
  }),
  outputSchema: z.object({
    vendor: vendorSchema,
    message: z.string().describe('Success message with details about the updated vendor'),
  }),
  execute: async ({ context }) => {
    // First check if the vendor exists in this organization
    const { data: existingVendor, error: checkError } = await supabase
      .from('vendors')
      .select('*')
      .eq('vendor_id', context.vendorId)
      .eq('organization_id', context.organizationId)
      .single();

    if (checkError) {
      throw new Error(`Failed to find vendor: ${checkError.message}`);
    }

    if (!existingVendor) {
      throw new Error(`Vendor with ID ${context.vendorId} not found in this organization`);
    }

    // Prepare update data (only include fields that were provided)
    const updateData: Record<string, any> = {};

    if (context.vendor_name !== undefined) updateData.vendor_name = context.vendor_name;
    if (context.vendor_email !== undefined) updateData.vendor_email = context.vendor_email;
    if (context.vendor_phone !== undefined) updateData.vendor_phone = context.vendor_phone;
    if (context.vendor_address !== undefined) updateData.vendor_address = context.vendor_address;
    if (context.isActive !== undefined) updateData.is_active = context.isActive;

    // Only update if there are changes
    if (Object.keys(updateData).length === 0) {
      return {
        vendor: existingVendor,
        message: 'No changes provided for update'
      };
    }

    // Update the vendor
    const { data: updatedVendor, error: updateError } = await supabase
      .from('vendors')
      .update({
        ...updateData,
        updated_by: context.userId,
        updated_at: new Date().toISOString(),
      })
      .eq('vendor_id', context.vendorId)
      .eq('organization_id', context.organizationId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update vendor: ${updateError.message}`);
    }

    return {
      vendor: updatedVendor,
      message: `Successfully updated vendor ${updatedVendor.vendor_name}`
    };
  },
});
