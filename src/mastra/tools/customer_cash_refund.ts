import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define refund status enum for validation
const refundStatusEnum = z.enum([
  'for_review',
  'approved'
]).optional();

// Helper function to validate date format (YYYY-MM-DD)
const isValidDateFormat = (dateString: string): boolean => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;

  const date = new Date(dateString);
  const timestamp = date.getTime();

  if (isNaN(timestamp)) return false;

  return date.toISOString().slice(0, 10) === dateString;
};

// Custom Zod transform for currency amounts - rounds to 2 decimal places
const currencyAmount = z.number()
  .transform(val => Math.round(val * 100) / 100)
  .refine(val => val >= 0, { message: 'Amount must be non-negative' });

// Helper function to check if an entity exists in the database
const entityExists = async (
  table: string,
  idField: string,
  id: string,
  organizationId: string
): Promise<boolean> => {
  const { data, error } = await supabase
    .from(table)
    .select(idField)
    .eq(idField, id)
    .eq('organization_id', organizationId)
    .single();

  if (error || !data) {
    return false;
  }

  return true;
};

// Helper function to check for duplicate customer cash refunds
const isDuplicateCustomerCashRefund = async (
  referenceNumber: string | undefined,
  refundDate: string,
  organizationId: string,
  customerId: string
): Promise<boolean> => {
  if (!referenceNumber) return false; // Can't check for duplicates without a reference number

  const { data, error } = await supabase
    .from('transactions')
    .select('transaction_id')
    .eq('transaction_type', 'customer_cash_refund')
    .eq('reference_number', referenceNumber)
    .eq('transaction_date', refundDate)
    .eq('organization_id', organizationId)
    .eq('customer_id', customerId);

  if (error) {
    console.error('Error checking for duplicate customer cash refund:', error);
    return false; // If we can't check, assume it's not a duplicate
  }

  return data && data.length > 0;
};

// Define payment application schema for linking refunds to customer transactions
const paymentApplicationSchema = z.object({
  transactionId: z.string().uuid().describe('Customer transaction ID (invoice, receipt, return) being refunded'),
  amount: currencyAmount.describe('Amount being applied to this transaction (automatically rounded to 2 decimal places)'),
});

// Enhanced accounting line schema
const enhancedAccountingLineSchema = z.object({
  accountId: z.string().uuid().describe('Account ID for this line'),
  description: z.string().describe('Line item description'),
  debitAmount: currencyAmount.default(0).describe('Debit amount (automatically rounded to 2 decimal places)'),
  creditAmount: currencyAmount.default(0).describe('Credit amount (automatically rounded to 2 decimal places)'),
  classId: z.string().uuid().optional().describe('Class ID for tracking business segments'),
  projectId: z.string().uuid().optional().describe('Project ID for tracking project-based work'),
});

// Define customer cash refund schema for output
const customerCashRefundSchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.string(),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  document_number: z.string().nullable(),
  description: z.string(),
  customer_id: z.string().uuid(),
  status: z.string(),
  total_amount: z.number(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Tool to record customer cash refund
export const recordCustomerCashRefund = createTool({
  id: 'Record Customer Cash Refund',
  description: 'Record cash refund to customer for returns, overpayments, or billing corrections. This reduces cash/bank accounts and can be applied to outstanding customer transactions. IMPORTANT: All monetary amounts (debitAmount, creditAmount, totalAmount) are automatically rounded to exactly 2 decimal places to ensure precision and eliminate floating-point errors. WORKFLOW: First search and get customer ID using searchCustomersByName, account IDs for cash/bank and customer accounts using searchAccountsByName, project ID using searchProjects (if applicable), and class ID using searchClassesByName (if applicable). Then use these IDs with user plain English input to record the refund transaction. The agent must provide all accounting lines manually (minimum 2 for double-entry). Typically includes cash/bank account debit and customer account credit. Perfect for processing customer refunds with full accounting control.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the refund'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    refundDate: z.string()
      .refine(val => isValidDateFormat(val), {
        message: 'Refund date must be in YYYY-MM-DD format',
      })
      .describe('Date of the refund (YYYY-MM-DD)'),
    customerId: z.string().uuid()
      .describe('Customer ID receiving the refund'),
    referenceNumber: z.string().optional().describe('Reference or check number for the refund'),
    description: z.string()
      .min(3, { message: 'Description must be at least 3 characters' })
      .describe('Description of the refund'),
    status: refundStatusEnum.default('approved').describe('Refund status (for_review or approved)'),
    totalAmount: currencyAmount
      .describe('Total refund amount (automatically rounded to 2 decimal places)'),

    lines: z.array(enhancedAccountingLineSchema)
    .min(2, { message: 'At least 2 line items are required for double-entry accounting' })
    .describe('Accounting line items (must have at least 2 for double-entry). Typically includes cash/bank account debit and customer account credit.'),

    paymentApplications: z.array(paymentApplicationSchema).optional()
      .describe('Optional applications to specific customer transactions (invoices, receipts, returns)'),

    skipDuplicateCheck: z.boolean().optional().default(false).describe('Skip checking for duplicate refunds'),
    maxRetries: z.number().optional().default(1).describe('Maximum number of retries for database operations'),
  }),
  outputSchema: z.object({
    refund: customerCashRefundSchema,
    refundLines: z.array(z.object({
      transaction_line_id: z.string().uuid(),
      account_id: z.string().uuid(),
      description: z.string(),
      debit_amount: z.number(),
      credit_amount: z.number(),
      class_id: z.string().uuid().nullable(),
      project_id: z.string().uuid().nullable(),
    })),
    refundApplications: z.array(z.object({
      invoice_payment_id: z.string().uuid(),
      transaction_id: z.string().uuid(),
      payment_transaction_id: z.string().uuid(),
      amount: z.number(),
      payment_date: z.string(),
      payment_method: z.string(),
      reference: z.string().nullable(),
    })).optional(),
    message: z.string().describe('Success message with refund details'),
    processingTimeMs: z.number().describe('Time taken to process the refund'),
  }),
  execute: async ({ context }) => {
    // Extract organization and user IDs from context
    const organizationId = context.organizationId;
    const userId = context.userId;
    // Start timing the refund processing
    const startTime = Date.now();
    let maxRetryCount = 0;
    let customerName = '';

    try {
      // Validate that lines are provided
      if (!context.lines || context.lines.length < 2) {
        throw new Error('At least 2 accounting line items are required for double-entry accounting.');
      }

      // Validate that each line has either debit OR credit (not both)
      for (const line of context.lines) {
        if (line.debitAmount > 0 && line.creditAmount > 0) {
          throw new Error('Each line must have either debit OR credit amount, not both');
        }
        if (line.debitAmount === 0 && line.creditAmount === 0) {
          throw new Error('Each line must have either a debit or credit amount');
        }
      }

      // Validate that debits equal credits (with automatic rounding to 2 decimal places)
      const totalDebits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.debitAmount, 0) * 100) / 100;
      const totalCredits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.creditAmount, 0) * 100) / 100;

      if (totalDebits !== totalCredits) {
        throw new Error(`Debits (${totalDebits}) must exactly equal credits (${totalCredits}). No tolerance allowed.`);
      }

      // Validate total amount matches accounting lines
      if (Math.round(context.totalAmount * 100) / 100 !== totalDebits) {
        throw new Error(`Total amount (${context.totalAmount}) must equal total debits/credits (${totalDebits})`);
      }

      // Validate all accounts exist
      for (const line of context.lines) {
        const accountExists = await entityExists('accounts', 'account_id', line.accountId, organizationId);
        if (!accountExists) {
          throw new Error(`Account with ID ${line.accountId} not found`);
        }

        // Validate optional classification fields
        if (line.classId) {
          const classExists = await entityExists('classes', 'class_id', line.classId, organizationId);
          if (!classExists) {
            throw new Error(`Class with ID ${line.classId} not found`);
          }
        }

        if (line.projectId) {
          const projectExists = await entityExists('projects', 'project_id', line.projectId, organizationId);
          if (!projectExists) {
            throw new Error(`Project with ID ${line.projectId} not found`);
          }
        }
      }

      // Validate customer exists
      const { data: customer, error: customerError } = await supabase
        .from('customers')
        .select('customer_name')
        .eq('customer_id', context.customerId)
        .eq('organization_id', organizationId)
        .single();

      if (customerError || !customer) {
        throw new Error(`Customer with ID ${context.customerId} not found`);
      }

      customerName = customer.customer_name;

      // Validate payment applications if provided
      if (context.paymentApplications && context.paymentApplications.length > 0) {
        const totalApplicationAmount = Math.round(context.paymentApplications.reduce((sum, app) => sum + app.amount, 0) * 100) / 100;
        if (totalApplicationAmount !== Math.round(context.totalAmount * 100) / 100) {
          throw new Error(`Payment applications total (${totalApplicationAmount}) must equal refund amount (${context.totalAmount})`);
        }

        // Validate each transaction being refunded
        for (const application of context.paymentApplications) {
          const { data: transaction, error: transactionError } = await supabase
            .from('transactions')
            .select('*')
            .eq('transaction_id', application.transactionId)
            .eq('customer_id', context.customerId)
            .eq('organization_id', organizationId)
            .in('transaction_type', ['sales_invoice', 'sales_receipt', 'sales_return'])
            .single();

          if (transactionError || !transaction) {
            throw new Error(`Transaction with ID ${application.transactionId} not found for this customer`);
          }
        }
      }

      // Check for duplicate refunds if not explicitly skipped
      if (!context.skipDuplicateCheck && context.referenceNumber) {
        const isDuplicate = await isDuplicateCustomerCashRefund(
          context.referenceNumber,
          context.refundDate,
          organizationId,
          context.customerId
        );

        if (isDuplicate) {
          throw new Error(`Duplicate customer cash refund detected with reference number ${context.referenceNumber}`);
        }
      }

      // Generate document number using the database function
      const { data: documentNumberResult, error: documentNumberError } = await supabase
        .rpc('generate_document_number', {
          p_transaction_type: 'customer_cash_refund',
          p_organization_id: organizationId
        });

      if (documentNumberError) {
        throw new Error(`Failed to generate refund number: ${documentNumberError.message}`);
      }

      const documentNumber = documentNumberResult;

      // Start a Supabase transaction with retry logic
      let refund: any;
      let refundError;
      let retryCount = 0;
      const maxRetries = context.maxRetries || 1;

      while (retryCount < maxRetries) {
        const refundData = {
          transaction_type: 'customer_cash_refund',
          transaction_date: context.refundDate,
          reference_number: context.referenceNumber,
          document_number: documentNumber,
          description: context.description,
          customer_id: context.customerId,
          status: context.status || 'approved',
          total_amount: context.totalAmount,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        };

        const result = await supabase
          .from('transactions')
          .insert(refundData)
          .select()
          .single();

        refund = result.data;
        refundError = result.error;

        if (!refundError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (refundError) {
        throw new Error(`Failed to create customer cash refund after ${retryCount} attempts: ${refundError.message}`);
      }

      // Insert transaction lines with retry logic
      const transactionLines = context.lines.map((line: any) => ({
        transaction_id: refund.transaction_id,
        account_id: line.accountId,
        description: line.description,
        debit_amount: line.debitAmount,
        credit_amount: line.creditAmount,
        class_id: line.classId || null,
        project_id: line.projectId || null,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      retryCount = 0;
      let linesError;

      while (retryCount < maxRetries) {
        const result = await supabase
          .from('transaction_lines')
          .insert(transactionLines);

        linesError = result.error;

        if (!linesError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (linesError) {
        throw new Error(`Failed to create refund lines after ${retryCount} attempts: ${linesError.message}`);
      }

      // Handle payment applications if provided
      let refundApplications: any[] = [];
      if (context.paymentApplications && context.paymentApplications.length > 0) {
        const applicationData = context.paymentApplications.map((app: any) => ({
          transaction_id: app.transactionId,
          payment_transaction_id: refund.transaction_id,
          amount: app.amount,
          payment_date: context.refundDate,
          payment_method: 'cash_refund',
          reference: context.referenceNumber || null,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        retryCount = 0;
        let applicationsError;

        while (retryCount < maxRetries) {
          const result = await supabase
            .from('invoice_payments')
            .insert(applicationData)
            .select();

          refundApplications = result.data || [];
          applicationsError = result.error;

          if (!applicationsError) {
            break; // Success, exit the retry loop
          }

          retryCount++;
          // Update max retry count for logging
          maxRetryCount = Math.max(maxRetryCount, retryCount);

          if (retryCount < maxRetries) {
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
          }
        }

        if (applicationsError) {
          throw new Error(`Failed to create refund applications after ${retryCount} attempts: ${applicationsError.message}`);
        }
      }

      // Update max retry count
      maxRetryCount = Math.max(maxRetryCount, context.maxRetries || 1);

      return {
        refund,
        refundLines: context.lines.map((line: any, index: number) => ({
          transaction_line_id: `line_${index + 1}`, // This would be the actual ID from the database
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
        })),
        refundApplications: refundApplications.length > 0 ? refundApplications.map((app: any, index: number) => ({
          invoice_payment_id: app.invoice_payment_id || `app_${index + 1}`,
          transaction_id: app.transaction_id,
          payment_transaction_id: app.payment_transaction_id,
          amount: app.amount,
          payment_date: app.payment_date,
          payment_method: app.payment_method,
          reference: app.reference,
        })) : undefined,
        message: `Successfully recorded customer cash refund ${documentNumber} to ${customerName} for $${context.totalAmount}${context.paymentApplications && context.paymentApplications.length > 0 ? ` applied to ${context.paymentApplications.length} transaction(s)` : ''}.`,
        processingTimeMs: Date.now() - startTime
      };

    } catch (error: any) {
      throw error;
    }
  },
});
