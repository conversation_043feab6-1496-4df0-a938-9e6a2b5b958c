import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define customer schema for reuse
const customerSchema = z.object({
  customer_id: z.string().uuid(),
  customer_name: z.string(),
  customer_email: z.string().nullable(),
  customer_phone: z.string().nullable(),
  customer_address: z.string().nullable(),
  is_active: z.boolean(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Define Customer interface for type safety
interface Customer {
  customer_id?: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  is_active?: boolean;
  organization_id?: string;
}

// Tool to get customers
export const getCustomers = createTool({
  id: 'Get Customers',
  description: 'Retrieve customer information from the customer database. Use this when you need to display customers, find customers for transaction recording, or when the user asks about customers. You can filter by active status or get a specific customer.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter customers'),
    customerId: z.string().uuid().optional().describe('Specific customer ID to retrieve'),
    isActive: z.boolean().optional().describe('Filter by active status'),
  }),
  outputSchema: z.object({
    customers: z.array(customerSchema),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('customers')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.customerId) {
      query = query.eq('customer_id', context.customerId);
    }

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query.order('customer_name');

    if (error) {
      throw new Error(`Failed to get customers: ${error.message}`);
    }

    return { customers: data };
  },
});

// Tool to search for customers by name
export const searchCustomersByName = createTool({
  id: 'Search Customers By Name',
  description: 'Search for customers by name when you need to find specific customers for transaction recording or when the user asks for customers containing certain text. Use this when you know part of a customer name but need to find the exact customer.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter customers'),
    nameContains: z.string().describe('Text to search for in customer names'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    limit: z.number().min(1).max(100).default(10).describe('Maximum number of customers to return'),
  }),
  outputSchema: z.object({
    customers: z.array(customerSchema),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('customers')
      .select('*')
      .ilike('customer_name', `%${context.nameContains}%`)
      .eq('organization_id', context.organizationId);

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query
      .order('customer_name')
      .limit(context.limit);

    if (error) {
      throw new Error(`Failed to search for customers: ${error.message}`);
    }

    return { customers: data };
  },
});

// Tool to create a customer
export const createCustomer = createTool({
  id: 'Create Customer',
  description: 'Create a new customer record in the customer database. Use this when the user wants to add a new customer for sales transactions, invoicing, or customer management. Customer information helps track sales and receivables.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the customer'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    customer_name: z.string().describe('Customer name'),
    customer_email: z.string().email().optional().describe('Customer email'),
    customer_phone: z.string().optional().describe('Customer phone number'),
    customer_address: z.string().optional().describe('Customer address'),
  }),
  outputSchema: z.object({
    customer: customerSchema,
  }),
  execute: async ({ context }) => {
    const { data, error } = await supabase
      .from('customers')
      .insert({
        customer_name: context.customer_name,
        customer_email: context.customer_email,
        customer_phone: context.customer_phone,
        customer_address: context.customer_address,
        organization_id: context.organizationId,
        created_by: context.userId,
        updated_by: context.userId,
        is_active: true,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create customer: ${error.message}`);
    }

    return { customer: data };
  },
});

// Tool to update a customer
export const updateCustomer = createTool({
  id: 'Update Customer',
  description: 'Update an existing customer record in the customer database. Use this when the user wants to modify customer details like contact information, address, or active status. This helps maintain accurate customer records for transactions.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the customer'),
    userId: z.string().uuid().describe('User ID for updated_by field'),
    customerId: z.string().uuid().describe('Customer ID to update'),
    customer_name: z.string().optional().describe('Updated customer name'),
    customer_email: z.string().email().optional().describe('Updated customer email'),
    customer_phone: z.string().optional().describe('Updated customer phone number'),
    customer_address: z.string().optional().describe('Updated customer address'),
    isActive: z.boolean().optional().describe('Update active status'),
  }),
  outputSchema: z.object({
    customer: customerSchema,
    message: z.string().describe('Success message with details about the updated customer'),
  }),
  execute: async ({ context }) => {
    // First check if the customer exists in this organization
    const { data: existingCustomer, error: checkError } = await supabase
      .from('customers')
      .select('*')
      .eq('customer_id', context.customerId)
      .eq('organization_id', context.organizationId)
      .single();

    if (checkError) {
      throw new Error(`Failed to find customer: ${checkError.message}`);
    }

    if (!existingCustomer) {
      throw new Error(`Customer with ID ${context.customerId} not found in this organization`);
    }

    // Prepare update data (only include fields that were provided)
    const updateData: Record<string, any> = {};

    if (context.customer_name !== undefined) updateData.customer_name = context.customer_name;
    if (context.customer_email !== undefined) updateData.customer_email = context.customer_email;
    if (context.customer_phone !== undefined) updateData.customer_phone = context.customer_phone;
    if (context.customer_address !== undefined) updateData.customer_address = context.customer_address;
    if (context.isActive !== undefined) updateData.is_active = context.isActive;

    // Only update if there are changes
    if (Object.keys(updateData).length === 0) {
      return {
        customer: existingCustomer,
        message: 'No changes provided for update'
      };
    }

    // Update the customer
    const { data: updatedCustomer, error: updateError } = await supabase
      .from('customers')
      .update({
        ...updateData,
        updated_by: context.userId,
        updated_at: new Date().toISOString(),
      })
      .eq('customer_id', context.customerId)
      .eq('organization_id', context.organizationId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update customer: ${updateError.message}`);
    }

    return {
      customer: updatedCustomer,
      message: `Successfully updated customer ${updatedCustomer.customer_name}`
    };
  },
});
