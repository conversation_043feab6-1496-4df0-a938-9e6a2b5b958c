import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define trial balance entry schema
const trialBalanceEntrySchema = z.object({
  account_id: z.string().uuid(),
  code: z.string(),
  name: z.string(),
  type: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']),
  debitBalance: z.number(),
  creditBalance: z.number(),
});

// Tool to generate a trial balance report
export const getTrialBalance = createTool({
  id: 'Get Trial Balance',
  description: 'Generate a trial balance report showing all account balances to verify that debits equal credits. Use this for financial reporting, month-end procedures, or when the user needs to verify the accounting equation is balanced. The trial balance is a fundamental accounting report that lists all accounts with their debit and credit balances.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the trial balance'),
    asOfDate: z.string().optional().describe('Generate trial balance as of this date (YYYY-MM-DD)'),
    startDate: z.string().optional().describe('Start date for the trial balance period (YYYY-MM-DD)'),
    endDate: z.string().optional().describe('End date for the trial balance period (YYYY-MM-DD)'),
    includeZeroBalances: z.boolean().default(false).describe('Include accounts with zero balances'),
  }),
  outputSchema: z.object({
    trialBalance: z.array(trialBalanceEntrySchema),
    totalDebits: z.number().describe('Total of all debit balances'),
    totalCredits: z.number().describe('Total of all credit balances'),
    isBalanced: z.boolean().describe('Whether total debits equal total credits'),
    dateRange: z.object({
      asOfDate: z.string().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
    }),
    message: z.string().describe('Information about the trial balance and its purpose'),
  }),
  execute: async ({ context }) => {
    const organizationId = context.organizationId;
    // First, get all accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('accounts')
      .select('*')
      .eq('is_active', true)
      .eq('organization_id', organizationId)
      .order('code');

    if (accountsError) {
      throw new Error(`Failed to get accounts: ${accountsError.message}`);
    }

    // For each account, calculate the balance
    const trialBalanceEntries = [];
    let totalDebits = 0;
    let totalCredits = 0;

    for (const account of accounts) {
      // Get all transaction lines for this account
      let linesQuery = supabase
        .from('transaction_lines')
        .select(`
          *,
          transactions:transaction_id (transaction_date)
        `)
        .eq('account_id', account.account_id);

      // Apply date filters if provided
      if (context.asOfDate) {
        linesQuery = linesQuery.lte('transactions.transaction_date', context.asOfDate);
      } else if (context.startDate && context.endDate) {
        linesQuery = linesQuery
          .gte('transactions.transaction_date', context.startDate)
          .lte('transactions.transaction_date', context.endDate);
      } else if (context.startDate) {
        linesQuery = linesQuery.gte('transactions.transaction_date', context.startDate);
      } else if (context.endDate) {
        linesQuery = linesQuery.lte('transactions.transaction_date', context.endDate);
      }

      const { data: lines, error: linesError } = await linesQuery;

      if (linesError) {
        throw new Error(`Failed to get transaction lines for account ${account.code}: ${linesError.message}`);
      }

      // Calculate the balance
      let debitBalance = 0;
      let creditBalance = 0;

      for (const line of lines) {
        debitBalance += line.debit_amount || 0;
        creditBalance += line.credit_amount || 0;
      }

      // Calculate the net balance based on account type
      let netBalance = 0;

      // For asset and expense accounts, debits increase the balance
      if (account.type === 'asset' || account.type === 'expense') {
        netBalance = debitBalance - creditBalance;
      }
      // For liability, equity, and revenue accounts, credits increase the balance
      else {
        netBalance = creditBalance - debitBalance;
      }

      // Only include accounts with non-zero balances unless includeZeroBalances is true
      if (netBalance !== 0 || context.includeZeroBalances) {
        // For the trial balance, we want to show the raw debit and credit totals
        const finalDebitBalance = netBalance > 0 && (account.type === 'asset' || account.type === 'expense') ?
          Math.abs(netBalance) :
          netBalance < 0 && (account.type === 'liability' || account.type === 'equity' || account.type === 'revenue') ?
            Math.abs(netBalance) : 0;

        const finalCreditBalance = netBalance < 0 && (account.type === 'asset' || account.type === 'expense') ?
          Math.abs(netBalance) :
          netBalance > 0 && (account.type === 'liability' || account.type === 'equity' || account.type === 'revenue') ?
            Math.abs(netBalance) : 0;

        trialBalanceEntries.push({
          account_id: account.account_id,
          code: account.code,
          name: account.account_name,
          type: account.type,
          debitBalance: finalDebitBalance,
          creditBalance: finalCreditBalance
        });

        totalDebits += finalDebitBalance;
        totalCredits += finalCreditBalance;
      }
    }

    // Check if the trial balance is balanced
    const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01; // Allow for small rounding differences

    const message = `Trial Balance Report: ${trialBalanceEntries.length} accounts listed. Total Debits: $${totalDebits.toFixed(2)}, Total Credits: $${totalCredits.toFixed(2)}. ${isBalanced ? 'The trial balance is balanced ✓' : 'WARNING: Trial balance is NOT balanced - there may be errors in the accounting records'}. The trial balance verifies that the fundamental accounting equation (Assets = Liabilities + Equity) is maintained.`;

    return {
      trialBalance: trialBalanceEntries,
      totalDebits,
      totalCredits,
      isBalanced,
      dateRange: context.asOfDate
        ? { asOfDate: context.asOfDate }
        : { startDate: context.startDate, endDate: context.endDate },
      message
    };
  },
});
