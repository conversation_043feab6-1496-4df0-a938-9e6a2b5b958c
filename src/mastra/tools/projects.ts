import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define project schema for reuse
const projectSchema = z.object({
  project_id: z.string().uuid(),
  project_name: z.string(),
  description: z.string().nullable(),
  parent_id: z.string().uuid().nullable(),
  is_active: z.boolean(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Define Project interface for type safety
interface Project {
  project_id?: string;
  project_name?: string;
  description?: string | null;
  parent_id?: string | null;
  is_active?: boolean;
  organization_id?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
}

// Tool to get projects
export const getProjects = createTool({
  id: 'Get Projects',
  description: 'Retrieve projects for tracking business initiatives, campaigns, or specific work segments. Use this when you need to display available projects for transaction categorization or when the user asks about project tracking. Projects help organize transactions for better project-based reporting.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter projects'),
    projectId: z.string().uuid().optional().describe('Specific project ID to retrieve'),
    parentId: z.string().uuid().optional().describe('Filter by parent project ID'),
    isActive: z.boolean().optional().describe('Filter by active status'),
  }),
  outputSchema: z.object({
    projects: z.array(projectSchema),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('projects')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.projectId) {
      query = query.eq('project_id', context.projectId);
    }

    if (context.parentId) {
      query = query.eq('parent_id', context.parentId);
    }

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query.order('project_name');

    if (error) {
      throw new Error(`Failed to retrieve projects: ${error.message}`);
    }

    return { projects: data };
  },
});

// Tool to create a project
export const createProject = createTool({
  id: 'Create Project',
  description: 'Create a new project for tracking business initiatives, campaigns, or specific work segments. Use this when the user wants to add a new way to categorize transactions for better project-based reporting and analysis. Projects can be hierarchical for detailed organization.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the project'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    name: z.string().describe('Project name'),
    description: z.string().optional().describe('Project description'),
    parentId: z.string().uuid().optional().describe('Parent project ID for hierarchical projects'),
    isActive: z.boolean().optional().default(true).describe('Whether the project is active'),
  }),
  outputSchema: z.object({
    project: projectSchema,
  }),
  execute: async ({ context }) => {
    // Check if parent project exists if provided
    if (context.parentId) {
      const { data: parentProject, error: parentError } = await supabase
        .from('projects')
        .select('project_id')
        .eq('project_id', context.parentId)
        .eq('organization_id', context.organizationId)
        .single();

      if (parentError || !parentProject) {
        throw new Error(`Parent project with ID ${context.parentId} not found`);
      }
    }

    // Create the new project
    const { data, error } = await supabase
      .from('projects')
      .insert({
        project_name: context.name,
        description: context.description || null,
        parent_id: context.parentId || null,
        is_active: context.isActive,
        organization_id: context.organizationId,
        created_by: context.userId,
        updated_by: context.userId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create project: ${error.message}`);
    }

    return { project: data };
  },
});

// Tool to update a project
export const updateProject = createTool({
  id: 'Update Project',
  description: 'Update an existing project used for tracking business initiatives. Use this when the user wants to modify project details like name, description, hierarchy, or active status. Be careful when changing hierarchy as this affects transaction categorization.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the project'),
    userId: z.string().uuid().describe('User ID for updated_by field'),
    projectId: z.string().uuid().describe('Project ID to update'),
    name: z.string().optional().describe('Updated project name'),
    description: z.string().optional().describe('Updated project description'),
    parentId: z.string().uuid().optional().describe('Updated parent project ID'),
    isActive: z.boolean().optional().describe('Updated active status'),
  }),
  outputSchema: z.object({
    project: projectSchema,
  }),
  execute: async ({ context }) => {
    // Check if project exists
    const { data: existingProject, error: existingError } = await supabase
      .from('projects')
      .select('project_id')
      .eq('project_id', context.projectId)
      .eq('organization_id', context.organizationId)
      .single();

    if (existingError || !existingProject) {
      throw new Error(`Project with ID ${context.projectId} not found`);
    }

    // Check if parent project exists if provided
    if (context.parentId) {
      // Prevent circular references
      if (context.parentId === context.projectId) {
        throw new Error('A project cannot be its own parent');
      }

      const { data: parentProject, error: parentError } = await supabase
        .from('projects')
        .select('project_id')
        .eq('project_id', context.parentId)
        .eq('organization_id', context.organizationId)
        .single();

      if (parentError || !parentProject) {
        throw new Error(`Parent project with ID ${context.parentId} not found`);
      }
    }

    // Update the project
    const updateData: Partial<Project> = {};
    if (context.name !== undefined) updateData.project_name = context.name;
    if (context.description !== undefined) updateData.description = context.description;
    if (context.parentId !== undefined) updateData.parent_id = context.parentId;
    if (context.isActive !== undefined) updateData.is_active = context.isActive;

    // Add updated_by field
    updateData.updated_by = context.userId;

    const { data, error } = await supabase
      .from('projects')
      .update(updateData)
      .eq('project_id', context.projectId)
      .eq('organization_id', context.organizationId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update project: ${error.message}`);
    }

    return { project: data };
  },
});

// Tool to search projects
export const searchProjects = createTool({
  id: 'Search Projects',
  description: 'Search for projects by name. Use this when the user wants to find specific projects for transaction categorization or reporting. Useful for finding projects when there are many in the system.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter projects'),
    nameContains: z.string().describe('Search term to match against project names'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    limit: z.number().optional().default(50).describe('Maximum number of results to return'),
  }),
  outputSchema: z.object({
    projects: z.array(projectSchema),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('projects')
      .select('*')
      .ilike('project_name', `%${context.nameContains}%`)
      .eq('organization_id', context.organizationId);

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query
      .order('project_name')
      .limit(context.limit);

    if (error) {
      throw new Error(`Failed to search for projects: ${error.message}`);
    }

    return { projects: data };
  },
});

// Define hierarchical project schema
const hierarchicalProjectSchema: z.ZodType<any> = z.lazy(() =>
  projectSchema.extend({
    children: z.array(hierarchicalProjectSchema),
  })
);

// Tool to get hierarchical projects
export const getHierarchicalProjects = createTool({
  id: 'Get Hierarchical Projects',
  description: 'Retrieve projects in a hierarchical structure showing parent-child relationships. Use this when you need to display projects in a tree format for better organization visualization.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter projects'),
    isActive: z.boolean().optional().describe('Filter by active status'),
  }),
  outputSchema: z.object({
    projects: z.array(hierarchicalProjectSchema),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('projects')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query.order('project_name');

    if (error) {
      throw new Error(`Failed to retrieve hierarchical projects: ${error.message}`);
    }

    // Build hierarchy
    const projectMap = new Map();
    const rootProjects: any[] = [];

    // First pass: create map of all projects
    data.forEach(project => {
      projectMap.set(project.project_id, { ...project, children: [] });
    });

    // Second pass: build hierarchy
    data.forEach(project => {
      const projectWithChildren = projectMap.get(project.project_id);
      if (project.parent_id) {
        const parent = projectMap.get(project.parent_id);
        if (parent) {
          parent.children.push(projectWithChildren);
        } else {
          rootProjects.push(projectWithChildren);
        }
      } else {
        rootProjects.push(projectWithChildren);
      }
    });

    return { projects: rootProjects };
  },
});
