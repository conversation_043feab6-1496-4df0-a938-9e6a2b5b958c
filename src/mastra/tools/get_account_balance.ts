import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define account schema for output
const accountSchema = z.object({
  account_id: z.string().uuid(),
  account_code: z.number(),
  account_name: z.string(),
  account_type: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']),
  account_type_detail: z.string(),
});

// Tool to get a single account balance
export const getAccountBalance = createTool({
  id: 'Get Account Balance',
  description: 'Calculate and retrieve the balance for a specific account in the current organization. Use this when you need to check the current balance of an account for financial reporting, transaction verification, or when the user asks about account balances. Essential for balance inquiries and account analysis.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the account'),
    accountId: z.string().uuid().describe('Account ID to retrieve balance for'),
    asOfDate: z.string().optional().describe('Calculate balance as of this date (YYYY-MM-DD). If not provided, uses current date'),
  }),
  outputSchema: z.object({
    account: accountSchema,
    balance: z.number().describe('Current account balance calculated according to accounting principles'),
    transactionCount: z.number().describe('Number of transactions affecting this account'),
    lastTransactionDate: z.string().nullable().describe('Date of the most recent transaction for this account'),
    asOfDate: z.string().describe('Date the balance was calculated as of'),
    balanceInterpretation: z.string().describe('Explanation of what the balance means for this account type'),
    message: z.string().describe('Summary message about the account balance and its significance'),
  }),
  execute: async ({ context }) => {
    const organizationId = context.organizationId;
    // First, get the account
    const { data: account, error: accountError } = await supabase
      .from('accounts')
      .select('*')
      .eq('account_id', context.accountId)
      .eq('organization_id', organizationId)
      .single();

    if (accountError) {
      throw new Error(`Failed to get account: ${accountError.message}`);
    }

    if (!account) {
      throw new Error(`Account with ID ${context.accountId} not found`);
    }

    // Get all transaction lines for this account
    let query = supabase
      .from('transaction_lines')
      .select(`
        *,
        transactions:transaction_id (*)
      `)
      .eq('account_id', account.account_id);

    if (context.asOfDate) {
      // First get all transaction IDs with dates before or equal to asOfDate
      const { data: validTransactions, error: txError } = await supabase
        .from('transactions')
        .select('transaction_id')
        .eq('organization_id', organizationId)
        .lte('transaction_date', context.asOfDate);

      if (txError) {
        throw new Error(`Failed to filter transactions by date: ${txError.message}`);
      }

      if (validTransactions && validTransactions.length > 0) {
        // Get the IDs of valid transactions
        const validTransactionIds = validTransactions.map(tx => tx.transaction_id);
        // Add this filter to our query
        query = query.in('transaction_id', validTransactionIds);
      } else {
        // No transactions found for this date range, return empty array
        const asOfDateFormatted = context.asOfDate!;
        const balanceInterpretation = `Zero balance as no transactions were found for this ${account.account_type} account as of ${asOfDateFormatted}`;
        const message = `Account "${account.account_name}" (${account.account_code}) has a zero balance as of ${asOfDateFormatted}. No transactions were found for this ${account.account_type} account within the specified date range.`;

        return {
          account: {
            account_id: account.account_id,
            account_code: account.account_code,
            account_name: account.account_name,
            account_type: account.account_type,
            account_type_detail: account.account_type_detail,
          },
          balance: 0,
          transactionCount: 0,
          lastTransactionDate: null,
          asOfDate: asOfDateFormatted,
          balanceInterpretation,
          message,
        };
      }
    }

    const { data: lines, error: linesError } = await query;

    if (linesError) {
      throw new Error(`Failed to get transaction lines for account ${account.account_code}: ${linesError.message}`);
    }

    // Calculate the balance
    let balance = 0;

    for (const line of lines) {
      // For asset and expense accounts, debits increase the balance
      if (account.account_type === 'asset' || account.account_type === 'expense') {
        balance += line.debit_amount - line.credit_amount;
      }
      // For liability, equity, and revenue accounts, credits increase the balance
      else {
        balance += line.credit_amount - line.debit_amount;
      }
    }

    // Get transaction count
    const transactionCount = lines.length;

    // Calculate last transaction date if there are any transactions
    let lastTransactionDate = null;
    if (transactionCount > 0) {
      // Get all transaction IDs from the lines
      const transactionIds = [...new Set(lines.map(line => line.transaction_id))];

      if (transactionIds.length > 0) {
        // Fetch the actual transaction dates
        const { data: transactions, error: txDatesError } = await supabase
          .from('transactions')
          .select('transaction_date')
          .in('transaction_id', transactionIds)
          .order('transaction_date', { ascending: false })
          .limit(1);

        if (!txDatesError && transactions && transactions.length > 0) {
          lastTransactionDate = transactions[0].transaction_date;
        }
      }
    }

    // Generate balance interpretation based on account type
    let balanceInterpretation = '';
    const absBalance = Math.abs(balance);
    const formattedBalance = `$${absBalance.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;

    switch (account.account_type) {
      case 'asset':
        balanceInterpretation = balance >= 0
          ? `Positive balance of ${formattedBalance} represents the value of this asset account`
          : `Negative balance of ${formattedBalance} indicates a credit balance, which is unusual for asset accounts`;
        break;
      case 'liability':
        balanceInterpretation = balance >= 0
          ? `Positive balance of ${formattedBalance} represents the amount owed (liability)`
          : `Negative balance of ${formattedBalance} indicates an overpayment or credit balance`;
        break;
      case 'equity':
        balanceInterpretation = balance >= 0
          ? `Positive balance of ${formattedBalance} represents owner's equity or retained earnings`
          : `Negative balance of ${formattedBalance} represents a deficit in equity`;
        break;
      case 'revenue':
        balanceInterpretation = balance >= 0
          ? `Positive balance of ${formattedBalance} represents income earned`
          : `Negative balance of ${formattedBalance} represents revenue reversals or adjustments`;
        break;
      case 'expense':
        balanceInterpretation = balance >= 0
          ? `Positive balance of ${formattedBalance} represents expenses incurred`
          : `Negative balance of ${formattedBalance} represents expense reversals or credits`;
        break;
    }

    // Generate summary message
    const asOfDateFormatted = context.asOfDate || new Date().toISOString().split('T')[0];
    const message = `Account "${account.account_name}" (${account.account_code}) has a balance of $${balance.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} as of ${asOfDateFormatted}. This ${account.account_type} account has ${transactionCount} transaction(s)${lastTransactionDate ? ` with the most recent on ${lastTransactionDate}` : ' with no transaction history'}.`;

    return {
      account: {
        account_id: account.account_id,
        account_code: account.account_code,
        account_name: account.account_name,
        account_type: account.account_type,
        account_type_detail: account.account_type_detail,
      },
      balance,
      transactionCount,
      lastTransactionDate,
      asOfDate: asOfDateFormatted,
      balanceInterpretation,
      message,
    };
  },
});
