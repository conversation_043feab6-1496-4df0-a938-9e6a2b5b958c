/**
 * SPECIALIZED JOURNAL ENTRY TOOL - Manual Accounting Lines Architecture
 *
 * This tool implements a specialized journal entry interface where:
 * - AI agents must provide all accounting lines manually
 * - Full control over debits, credits, and account assignments
 * - Minimum 2 lines required for proper double-entry bookkeeping
 * - Optional product/service details can be included in lines
 * - Strict validation with zero tolerance for balance violations
 * - Automatic floating-point precision handling
 * - Optional customer/vendor fields for tracking purposes
 * - No payment applications (adjusting entries only)
 */

import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define journal entry status enum for validation
const journalEntryStatusEnum = z.enum([
  'for_review',
  'approved'
]).optional();

// Helper function to validate date format (YYYY-MM-DD)
const isValidDateFormat = (dateString: string): boolean => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;

  const date = new Date(dateString);
  const timestamp = date.getTime();

  if (isNaN(timestamp)) return false;

  return date.toISOString().slice(0, 10) === dateString;
};

// Custom Zod transform for currency amounts - rounds to 2 decimal places
const currencyAmount = z.number()
  .transform(val => Math.round(val * 100) / 100)
  .refine(val => val >= 0, { message: 'Amount must be non-negative' });

// Helper function to check if an entity exists in the database
const entityExists = async (
  table: string,
  idField: string,
  id: string,
  organizationId: string
): Promise<boolean> => {
  const { data, error } = await supabase
    .from(table)
    .select(idField)
    .eq(idField, id)
    .eq('organization_id', organizationId)
    .single();

  if (error || !data) {
    return false;
  }

  return true;
};

// Helper function to check for duplicate journal entries
const isDuplicateJournalEntry = async (
  referenceNumber: string | undefined,
  entryDate: string,
  organizationId: string
): Promise<boolean> => {
  if (!referenceNumber) return false; // Can't check for duplicates without a reference number

  const { data, error } = await supabase
    .from('transactions')
    .select('transaction_id')
    .eq('transaction_type', 'journal_entry')
    .eq('reference_number', referenceNumber)
    .eq('transaction_date', entryDate)
    .eq('organization_id', organizationId);

  if (error) {
    console.error('Error checking for duplicate journal entry:', error);
    return false; // If we can't check, assume it's not a duplicate
  }

  return data && data.length > 0;
};

// Enhanced accounting line schema with precision handling and product/service support
const enhancedAccountingLineSchema = z.object({
  // Required accounting fields
  accountId: z.string().uuid().describe('Account ID'),
  description: z.string().describe('Line description'),
  debitAmount: currencyAmount.default(0).describe('Debit amount (automatically rounded to 2 decimal places)'),
  creditAmount: currencyAmount.default(0).describe('Credit amount (automatically rounded to 2 decimal places)'),

  // Optional classification
  classId: z.string().uuid().optional().describe('Class ID'),
  projectId: z.string().uuid().optional().describe('Project ID'),

  // Optional product/service fields
  productServiceId: z.string().uuid().optional().describe('Product/Service ID'),
  quantity: z.number().positive().optional().describe('Quantity'),
  unitPrice: currencyAmount.optional().describe('Unit price (automatically rounded to 2 decimal places)'),
  discountPercentage: z.number().min(0).max(100).default(0).describe('Discount percentage'),
  discountAmount: currencyAmount.default(0).describe('Discount amount (automatically rounded to 2 decimal places)'),
  taxRateId: z.string().uuid().optional().describe('Tax rate ID'),
  taxRate: z.number().min(0).max(100).default(0).describe('Tax rate percentage')
});

// Define journal entry schema for output
const journalEntrySchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.string(),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  document_number: z.string().nullable(),
  description: z.string(),
  customer_id: z.string().uuid().nullable(),
  vendor_id: z.string().uuid().nullable(),
  due_date: z.string().nullable(),
  status: z.string(),
  payment_terms: z.string().nullable(),
  subtotal_amount: z.number().nullable(),
  tax_amount: z.number().nullable(),
  total_amount: z.number(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Tool to create a journal entry
export const createJournalEntry = createTool({
  id: 'Create Journal Entry',
  description: 'Create a journal entry with manual double-entry accounting lines. Use this for adjusting entries, corrections, accruals, deferrals, and other general ledger adjustments. Can optionally reference customers or vendors for tracking purposes but does not handle payments. IMPORTANT: All monetary amounts (debitAmount, creditAmount, unitPrice, discountAmount, totalAmount) are automatically rounded to exactly 2 decimal places to ensure precision and eliminate floating-point errors. WORKFLOW: First search and get account IDs using searchAccountsByName, customer ID using searchCustomersByName (if applicable), vendor ID using searchVendorsByName (if applicable), tax rate ID using getTaxRates/searchTaxRatesByName (if applicable), product/service ID using searchProductsAndServices (if applicable), project ID using searchProjects (if applicable), and class ID using searchClassesByName (if applicable). Then use these IDs with user plain English input to record the journal entry. The agent must provide all accounting lines manually (minimum 2 for double-entry). Lines can optionally include product/service details for tracking purposes. Perfect for month-end adjustments, corrections, and other general ledger entries.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the journal entry'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    entryDate: z.string()
      .refine(val => isValidDateFormat(val), {
        message: 'Entry date must be in YYYY-MM-DD format',
      })
      .describe('Date of the journal entry (YYYY-MM-DD)'),
    referenceNumber: z.string().optional().describe('Reference or journal entry number'),
    description: z.string()
      .min(3, { message: 'Description must be at least 3 characters' })
      .describe('Description of the journal entry'),
    customerId: z.string().uuid().optional()
      .describe('Customer ID (optional - for tracking purposes only, not for payments)'),
    vendorId: z.string().uuid().optional()
      .describe('Vendor ID (optional - for tracking purposes only, not for payments)'),
    status: journalEntryStatusEnum.default('approved').describe('Journal entry status (for_review or approved)'),
    totalAmount: currencyAmount
      .describe('Total journal entry amount (automatically rounded to 2 decimal places) - should equal the sum of all debits or credits'),

    lines: z.array(enhancedAccountingLineSchema)
      .min(2, { message: 'At least 2 line items are required for double-entry accounting' })
      .describe('Accounting line items (must have at least 2 for double-entry). Can optionally include product/service details for tracking purposes. Tax can be specified using taxRate (percentage) or taxRateId (reference).'),
    
    skipDuplicateCheck: z.boolean().optional().default(false).describe('Skip checking for duplicate journal entries'),
    maxRetries: z.number().optional().default(1).describe('Maximum number of retries for database operations'),
  }),
  outputSchema: z.object({
    journalEntry: journalEntrySchema,
    journalEntryLines: z.array(z.object({
      transaction_line_id: z.string().uuid(),
      account_id: z.string().uuid(),
      description: z.string(),
      debit_amount: z.number(),
      credit_amount: z.number(),
      class_id: z.string().uuid().nullable(),
      project_id: z.string().uuid().nullable(),
    })),
    message: z.string().describe('Success message with journal entry details'),
    processingTimeMs: z.number().describe('Time taken to process the journal entry'),
  }),
  execute: async ({ context }) => {
    // Extract organization ID from context
    const organizationId = context.organizationId;
    // Start timing the journal entry processing
    const startTime = Date.now();
    let maxRetryCount = 0;

    try {
      // Validate that lines are provided
      if (!context.lines || context.lines.length < 2) {
        throw new Error('At least 2 accounting line items are required for double-entry accounting.');
      }

      // STRICT VALIDATION: Each line must have either debit OR credit (not both)
      for (const line of context.lines) {
        if (line.debitAmount > 0 && line.creditAmount > 0) {
          throw new Error('Each line must have either debit OR credit amount, not both');
        }
        if (line.debitAmount === 0 && line.creditAmount === 0) {
          throw new Error('Each line must have either a debit or credit amount');
        }
      }

      // ZERO TOLERANCE: Debits must exactly equal credits (with floating-point precision handling)
      const totalDebits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.debitAmount, 0) * 100) / 100;
      const totalCredits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.creditAmount, 0) * 100) / 100;

      if (totalDebits !== totalCredits) {
        throw new Error(`Debits (${totalDebits}) must exactly equal credits (${totalCredits}). No tolerance allowed.`);
      }

      // Validate all accounts exist
      for (const line of context.lines) {
        const accountExists = await entityExists('accounts', 'account_id', line.accountId, organizationId);
        if (!accountExists) {
          throw new Error(`Account with ID ${line.accountId} not found`);
        }

        // Validate optional classification fields
        if (line.classId) {
          const classExists = await entityExists('classes', 'class_id', line.classId, organizationId);
          if (!classExists) {
            throw new Error(`Class with ID ${line.classId} not found`);
          }
        }

        if (line.projectId) {
          const projectExists = await entityExists('projects', 'project_id', line.projectId, organizationId);
          if (!projectExists) {
            throw new Error(`Project with ID ${line.projectId} not found`);
          }
        }
      }

      // Validate optional customer exists
      if (context.customerId) {
        const customerExists = await entityExists('customers', 'customer_id', context.customerId, organizationId);
        if (!customerExists) {
          throw new Error(`Customer with ID ${context.customerId} not found`);
        }
      }

      // Validate optional vendor exists
      if (context.vendorId) {
        const vendorExists = await entityExists('vendors', 'vendor_id', context.vendorId, organizationId);
        if (!vendorExists) {
          throw new Error(`Vendor with ID ${context.vendorId} not found`);
        }
      }

      // Enhanced product/service validation
      const linesWithProducts = context.lines.filter(line => line.productServiceId);
      if (linesWithProducts.length > 0) {
        // Validate each product/service and related entities
        for (const line of linesWithProducts) {
          const productExists = line.productServiceId ? await entityExists('products_and_services', 'product_service_id', line.productServiceId, organizationId) : true;
          if (!productExists) {
            throw new Error(`Product/Service with ID ${line.productServiceId} not found`);
          }

          // Validate tax rate if provided
          if (line.taxRateId) {
            const taxRateExists = await entityExists('tax_rates', 'tax_rate_id', line.taxRateId, organizationId);
            if (!taxRateExists) {
              throw new Error(`Tax rate with ID ${line.taxRateId} not found`);
            }
          }
        }
      }

      // Check for duplicate journal entries (if reference number provided)
      if (!context.skipDuplicateCheck && context.referenceNumber) {
        const isDuplicate = await isDuplicateJournalEntry(
          context.referenceNumber,
          context.entryDate,
          organizationId
        );

        if (isDuplicate) {
          throw new Error(`Duplicate journal entry detected with reference number ${context.referenceNumber}`);
        }
      }

      // Generate document number using the database function
      const { data: documentNumberResult, error: documentNumberError } = await supabase
        .rpc('generate_document_number', {
          p_transaction_type: 'journal_entry',
          p_organization_id: organizationId
        });

      if (documentNumberError) {
        throw new Error(`Failed to generate journal entry number: ${documentNumberError.message}`);
      }

      const documentNumber = documentNumberResult;

      // Start a Supabase transaction with retry logic
      let journalEntry: any;
      let journalEntryError;
      let retryCount = 0;
      const maxRetries = context.maxRetries || 1;

      while (retryCount < maxRetries) {
        const journalEntryData = {
          transaction_type: 'journal_entry',
          transaction_date: context.entryDate,
          reference_number: context.referenceNumber,
          document_number: documentNumber,
          description: context.description,
          customer_id: context.customerId || null, // Optional customer for tracking
          vendor_id: context.vendorId || null,     // Optional vendor for tracking
          due_date: null,    // Journal entries don't have due dates
          status: context.status || 'approved',
          payment_terms: null, // Journal entries don't have payment terms
          subtotal_amount: context.totalAmount,
          tax_amount: 0,
          total_amount: context.totalAmount,
          organization_id: context.organizationId,
          created_by: context.userId,
          updated_by: context.userId,
        };

        const result = await supabase
          .from('transactions')
          .insert(journalEntryData)
          .select()
          .single();

        journalEntry = result.data;
        journalEntryError = result.error;

        if (!journalEntryError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (journalEntryError) {
        throw new Error(`Failed to create journal entry after ${retryCount} attempts: ${journalEntryError.message}`);
      }

      // Create transaction lines with optional product/service details
      const transactionLines = context.lines.map((line: any) => {
        const lineData: any = {
          transaction_id: journalEntry.transaction_id,
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
          organization_id: context.organizationId,
          created_by: context.userId,
          updated_by: context.userId,
        };

        // Add product/service fields if provided
        if (line.productServiceId) {
          lineData.product_service_id = line.productServiceId;
          lineData.quantity = line.quantity || null;
          lineData.unit_price = line.unitPrice || null;
          lineData.discount_percentage = line.discountPercentage || 0;
          lineData.discount_amount = line.discountAmount || 0;
          lineData.tax_rate_id = line.taxRateId || null;
          lineData.tax_rate = line.taxRate || 0;
        }

        return lineData;
      });

      retryCount = 0;
      let linesError;

      while (retryCount < maxRetries) {
        const result = await supabase
          .from('transaction_lines')
          .insert(transactionLines);

        linesError = result.error;

        if (!linesError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (linesError) {
        throw new Error(`Failed to create journal entry lines after ${retryCount} attempts: ${linesError.message}`);
      }

      // Update max retry count
      maxRetryCount = Math.max(maxRetryCount, context.maxRetries || 1);

      // Build message with optional customer/vendor info
      let entityInfo = '';
      if (context.customerId && context.vendorId) {
        entityInfo = ' (with customer and vendor references)';
      } else if (context.customerId) {
        entityInfo = ' (with customer reference)';
      } else if (context.vendorId) {
        entityInfo = ' (with vendor reference)';
      }

      return {
        journalEntry,
        journalEntryLines: context.lines.map((line: any, index: number) => ({
          transaction_line_id: `line_${index + 1}`, // This would be the actual ID from the database
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
        })),
        message: `Successfully created journal entry ${documentNumber} with ${context.lines.length} accounting line items${linesWithProducts.length > 0 ? ` (${linesWithProducts.length} with product/service details)` : ''}${entityInfo}.`,
        processingTimeMs: Date.now() - startTime
      };
    } catch (error: any) {
      // Provide detailed error information - throw error to match expected output schema
      throw new Error(`Journal entry creation failed: ${error.message}`);
    }
  },
});
