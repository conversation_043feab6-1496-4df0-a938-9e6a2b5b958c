import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define refund status enum for validation
const refundStatusEnum = z.enum([
  'for_review',
  'approved'
]).optional();

// Helper function to validate date format (YYYY-MM-DD)
const isValidDateFormat = (dateString: string): boolean => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;

  const date = new Date(dateString);
  const timestamp = date.getTime();

  if (isNaN(timestamp)) return false;

  return date.toISOString().slice(0, 10) === dateString;
};

// Custom Zod transform for currency amounts - rounds to 2 decimal places
const currencyAmount = z.number()
  .transform(val => Math.round(val * 100) / 100)
  .refine(val => val >= 0, { message: 'Amount must be non-negative' });

// Helper function to check if an entity exists in the database
const entityExists = async (
  table: string,
  idField: string,
  id: string,
  organizationId: string
): Promise<boolean> => {
  const { data, error } = await supabase
    .from(table)
    .select(idField)
    .eq(idField, id)
    .eq('organization_id', organizationId)
    .single();

  if (error || !data) {
    return false;
  }

  return true;
};

// Helper function to check for duplicate vendor cash refunds
const isDuplicateVendorCashRefund = async (
  referenceNumber: string | undefined,
  refundDate: string,
  organizationId: string,
  vendorId: string
): Promise<boolean> => {
  if (!referenceNumber) return false; // Can't check for duplicates without a reference number

  const { data, error } = await supabase
    .from('transactions')
    .select('transaction_id')
    .eq('transaction_type', 'vendor_cash_refund')
    .eq('reference_number', referenceNumber)
    .eq('transaction_date', refundDate)
    .eq('organization_id', organizationId)
    .eq('vendor_id', vendorId);

  if (error) {
    console.error('Error checking for duplicate vendor cash refund:', error);
    return false; // If we can't check, assume it's not a duplicate
  }

  return data && data.length > 0;
};

// Define payment application schema for linking refunds to vendor transactions
const paymentApplicationSchema = z.object({
  transactionId: z.string().uuid().describe('Vendor transaction ID (bill, expense, return) being refunded'),
  amount: currencyAmount.describe('Amount being applied to this transaction (automatically rounded to 2 decimal places)'),
});

// Enhanced accounting line schema
const enhancedAccountingLineSchema = z.object({
  accountId: z.string().uuid().describe('Account ID for this line'),
  description: z.string().describe('Line item description'),
  debitAmount: currencyAmount.default(0).describe('Debit amount (automatically rounded to 2 decimal places)'),
  creditAmount: currencyAmount.default(0).describe('Credit amount (automatically rounded to 2 decimal places)'),
  classId: z.string().uuid().optional().describe('Class ID for tracking business segments'),
  projectId: z.string().uuid().optional().describe('Project ID for tracking project-based work'),
});

// Define vendor cash refund schema for output
const vendorCashRefundSchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.string(),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  document_number: z.string().nullable(),
  description: z.string(),
  vendor_id: z.string().uuid(),
  status: z.string(),
  total_amount: z.number(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Tool to record vendor cash refund
export const recordVendorCashRefund = createTool({
  id: 'Record Vendor Cash Refund',
  description: 'Record cash refund received from vendor for returns, overpayments, or credits. This increases cash/bank accounts and can be applied to outstanding vendor transactions. Follows manual accounting lines architecture with strict double-entry validation and automatic floating-point precision handling.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the refund'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    refundDate: z.string()
      .refine(val => isValidDateFormat(val), {
        message: 'Refund date must be in YYYY-MM-DD format',
      })
      .describe('Date of the refund (YYYY-MM-DD)'),
    vendorId: z.string().uuid()
      .describe('Vendor ID providing the refund'),
    referenceNumber: z.string().optional().describe('Reference or check number for the refund'),
    description: z.string()
      .min(3, { message: 'Description must be at least 3 characters' })
      .describe('Description of the refund'),
    status: refundStatusEnum.default('approved').describe('Refund status (for_review or approved)'),
    totalAmount: currencyAmount
      .describe('Total refund amount (automatically rounded to 2 decimal places)'),

    lines: z.array(enhancedAccountingLineSchema)
    .min(2, { message: 'At least 2 line items are required for double-entry accounting' })
    .describe('Accounting line items (must have at least 2 for double-entry). Typically includes cash/bank account debit and vendor account credit.'),

    paymentApplications: z.array(paymentApplicationSchema).optional()
      .describe('Optional applications to specific vendor transactions (bills, expenses, returns)'),

    skipDuplicateCheck: z.boolean().optional().default(false).describe('Skip checking for duplicate refunds'),
    maxRetries: z.number().optional().default(1).describe('Maximum number of retries for database operations'),
  }),
  outputSchema: z.object({
    refund: vendorCashRefundSchema,
    refundLines: z.array(z.object({
      transaction_line_id: z.string().uuid(),
      account_id: z.string().uuid(),
      description: z.string(),
      debit_amount: z.number(),
      credit_amount: z.number(),
      class_id: z.string().uuid().nullable(),
      project_id: z.string().uuid().nullable(),
    })),
    refundApplications: z.array(z.object({
      bill_payment_id: z.string().uuid(),
      transaction_id: z.string().uuid(),
      payment_transaction_id: z.string().uuid(),
      amount: z.number(),
      payment_date: z.string(),
      payment_method: z.string(),
      reference: z.string().nullable(),
    })).optional(),
    message: z.string().describe('Success message with refund details'),
    processingTimeMs: z.number().describe('Time taken to process the refund'),
  }),
  execute: async ({ context }) => {
    // Extract organization and user IDs from context
    const organizationId = context.organizationId;
    const userId = context.userId;
    // Start timing the refund processing
    const startTime = Date.now();
    let maxRetryCount = 0;
    let vendorName = '';

    try {
      const maxRetries = context.maxRetries || 1;
      let retryCount = 0;
      let refund: any;
      let refundError: any;

      // Validate that each line has either debit OR credit, but not both or neither
      for (const line of context.lines) {
        const hasDebit = line.debitAmount > 0;
        const hasCredit = line.creditAmount > 0;

        if (hasDebit && hasCredit) {
          throw new Error(`Line "${line.description}" cannot have both debit and credit amounts`);
        }

        if (!hasDebit && !hasCredit) {
          throw new Error(`Line "${line.description}" must have either a debit or credit amount`);
        }
      }

      // Validate that debits equal credits (with automatic rounding to 2 decimal places)
      const totalDebits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.debitAmount, 0) * 100) / 100;
      const totalCredits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.creditAmount, 0) * 100) / 100;

      if (totalDebits !== totalCredits) {
        throw new Error(`Debits (${totalDebits}) must exactly equal credits (${totalCredits}). No tolerance allowed.`);
      }

      // Validate total amount matches accounting lines
      if (Math.round(context.totalAmount * 100) / 100 !== totalDebits) {
        throw new Error(`Total amount (${context.totalAmount}) must equal total debits/credits (${totalDebits})`);
      }

      // Validate all accounts exist
      for (const line of context.lines) {
        const accountExists = await entityExists('accounts', 'account_id', line.accountId, organizationId);
        if (!accountExists) {
          throw new Error(`Account with ID ${line.accountId} not found`);
        }

        // Validate class if provided
        if (line.classId) {
          const classExists = await entityExists('classes', 'class_id', line.classId, organizationId);
          if (!classExists) {
            throw new Error(`Class with ID ${line.classId} not found`);
          }
        }

        // Validate project if provided
        if (line.projectId) {
          const projectExists = await entityExists('projects', 'project_id', line.projectId, organizationId);
          if (!projectExists) {
            throw new Error(`Project with ID ${line.projectId} not found`);
          }
        }
      }

      // Validate vendor exists
      const { data: vendor, error: vendorError } = await supabase
        .from('vendors')
        .select('vendor_name')
        .eq('vendor_id', context.vendorId)
        .eq('organization_id', organizationId)
        .single();

      if (vendorError || !vendor) {
        throw new Error(`Vendor with ID ${context.vendorId} not found`);
      }

      vendorName = vendor.vendor_name;

      // Validate payment applications if provided
      if (context.paymentApplications && context.paymentApplications.length > 0) {
        const totalApplicationAmount = Math.round(context.paymentApplications.reduce((sum, app) => sum + app.amount, 0) * 100) / 100;
        if (totalApplicationAmount !== Math.round(context.totalAmount * 100) / 100) {
          throw new Error(`Payment applications total (${totalApplicationAmount}) must equal refund amount (${context.totalAmount})`);
        }

        // Validate each transaction being refunded
        for (const application of context.paymentApplications) {
          const { data: transaction, error: transactionError } = await supabase
            .from('transactions')
            .select('*')
            .eq('transaction_id', application.transactionId)
            .eq('vendor_id', context.vendorId)
            .eq('organization_id', organizationId)
            .in('transaction_type', ['bill', 'expense', 'purchase_return'])
            .single();

          if (transactionError || !transaction) {
            throw new Error(`Transaction with ID ${application.transactionId} not found for this vendor`);
          }
        }
      }

      // Check for duplicate refunds if not explicitly skipped
      if (!context.skipDuplicateCheck && context.referenceNumber) {
        const isDuplicate = await isDuplicateVendorCashRefund(
          context.referenceNumber,
          context.refundDate,
          organizationId,
          context.vendorId
        );

        if (isDuplicate) {
          throw new Error(`Duplicate vendor cash refund detected with reference number ${context.referenceNumber}`);
        }
      }

      // Generate document number using the database function
      const { data: documentNumberResult, error: documentNumberError } = await supabase
        .rpc('generate_document_number', {
          p_transaction_type: 'vendor_cash_refund',
          p_organization_id: organizationId
        });

      if (documentNumberError) {
        throw new Error(`Failed to generate refund number: ${documentNumberError.message}`);
      }

      const documentNumber = documentNumberResult;

      // Insert the main refund transaction with retry logic
      while (retryCount < maxRetries) {
        const refundData = {
          transaction_type: 'vendor_cash_refund',
          transaction_date: context.refundDate,
          reference_number: context.referenceNumber,
          document_number: documentNumber,
          description: context.description,
          vendor_id: context.vendorId,
          status: context.status || 'approved',
          total_amount: context.totalAmount,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        };

        const result = await supabase
          .from('transactions')
          .insert(refundData)
          .select()
          .single();

        refund = result.data;
        refundError = result.error;

        if (!refundError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (refundError) {
        throw new Error(`Failed to create vendor cash refund after ${retryCount} attempts: ${refundError.message}`);
      }

      // Insert transaction lines with retry logic
      const transactionLines = context.lines.map((line: any) => ({
        transaction_id: refund.transaction_id,
        account_id: line.accountId,
        description: line.description,
        debit_amount: line.debitAmount,
        credit_amount: line.creditAmount,
        class_id: line.classId || null,
        project_id: line.projectId || null,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      retryCount = 0;
      let linesError;

      while (retryCount < maxRetries) {
        const result = await supabase
          .from('transaction_lines')
          .insert(transactionLines);

        linesError = result.error;

        if (!linesError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (linesError) {
        throw new Error(`Failed to create refund lines after ${retryCount} attempts: ${linesError.message}`);
      }

      // Handle payment applications if provided
      let refundApplications: any[] = [];
      if (context.paymentApplications && context.paymentApplications.length > 0) {
        const applicationData = context.paymentApplications.map((app: any) => ({
          transaction_id: app.transactionId,
          payment_transaction_id: refund.transaction_id,
          amount: app.amount,
          payment_date: context.refundDate,
          payment_method: 'cash_refund',
          reference: context.referenceNumber || null,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        retryCount = 0;
        let applicationsError;

        while (retryCount < maxRetries) {
          const result = await supabase
            .from('bill_payments')
            .insert(applicationData)
            .select();

          refundApplications = result.data || [];
          applicationsError = result.error;

          if (!applicationsError) {
            break; // Success, exit the retry loop
          }

          retryCount++;
          // Update max retry count for logging
          maxRetryCount = Math.max(maxRetryCount, retryCount);

          if (retryCount < maxRetries) {
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
          }
        }

        if (applicationsError) {
          throw new Error(`Failed to create refund applications after ${retryCount} attempts: ${applicationsError.message}`);
        }
      }

      // Update max retry count
      maxRetryCount = Math.max(maxRetryCount, context.maxRetries || 1);

      return {
        refund,
        refundLines: context.lines.map((line: any, index: number) => ({
          transaction_line_id: `line_${index + 1}`, // This would be the actual ID from the database
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
        })),
        refundApplications: refundApplications.length > 0 ? refundApplications.map((app: any, index: number) => ({
          bill_payment_id: app.bill_payment_id || `app_${index + 1}`,
          transaction_id: app.transaction_id,
          payment_transaction_id: app.payment_transaction_id,
          amount: app.amount,
          payment_date: app.payment_date,
          payment_method: app.payment_method,
          reference: app.reference,
        })) : undefined,
        message: `Successfully recorded vendor cash refund ${documentNumber} from ${vendorName} for $${context.totalAmount}${context.paymentApplications && context.paymentApplications.length > 0 ? ` applied to ${context.paymentApplications.length} transaction(s)` : ''}.`,
        processingTimeMs: Date.now() - startTime
      };

    } catch (error: any) {
      throw error;
    }
  },
});
