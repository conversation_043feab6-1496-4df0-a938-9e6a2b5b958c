import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { BalanceService } from '../../services/balanceService.js';

// Define account type enum for input validation (used in schema definitions)
// const accountTypeEnum = z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']);

// Define account schema for output
const accountSchema = z.object({
  account_id: z.string().uuid(),
  account_code: z.number(),
  account_name: z.string(),
  account_type: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']),
  account_type_detail: z.string(),
});

// Helper function to generate balance interpretation
function generateBalanceInterpretation(accountType: string, balance: number, transactionCount: number): string {
  const absBalance = Math.abs(balance);
  const formattedBalance = `$${absBalance.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;

  if (transactionCount === 0) {
    return `Zero balance with no transaction history for this ${accountType} account`;
  }

  switch (accountType) {
    case 'asset':
      return balance >= 0
        ? `Positive balance of ${formattedBalance} represents the value of this asset account`
        : `Negative balance of ${formattedBalance} indicates a credit balance, which is unusual for asset accounts`;
    case 'liability':
      return balance >= 0
        ? `Positive balance of ${formattedBalance} represents the amount owed (liability)`
        : `Negative balance of ${formattedBalance} indicates an overpayment or credit balance`;
    case 'equity':
      return balance >= 0
        ? `Positive balance of ${formattedBalance} represents owner's equity or retained earnings`
        : `Negative balance of ${formattedBalance} represents a deficit in equity`;
    case 'revenue':
      return balance >= 0
        ? `Positive balance of ${formattedBalance} represents income earned`
        : `Negative balance of ${formattedBalance} represents revenue reversals or adjustments`;
    case 'expense':
      return balance >= 0
        ? `Positive balance of ${formattedBalance} represents expenses incurred`
        : `Negative balance of ${formattedBalance} represents expense reversals or credits`;
    default:
      return `Balance of ${formattedBalance} for this ${accountType} account`;
  }
}

// Optimized tool to get a single account balance
export const getAccountBalanceOptimized = createTool({
  id: 'Get Account Balance Optimized',
  description: 'Calculate and retrieve the balance for a specific account using optimized database functions. This version provides significantly better performance for balance calculations, especially for accounts with many transactions. Use this when you need to check the current balance of an account for financial reporting, transaction verification, or when the user asks about account balances.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the account'),
    accountId: z.string().uuid().describe('Account ID to retrieve balance for'),
    asOfDate: z.string().optional().describe('Calculate balance as of this date (YYYY-MM-DD). If not provided, uses current date'),
  }),
  outputSchema: z.object({
    account: accountSchema,
    balance: z.number().describe('Current balance of the account'),
    totalDebits: z.number().describe('Total debit amount for the account'),
    totalCredits: z.number().describe('Total credit amount for the account'),
    transactionCount: z.number().describe('Number of transactions affecting this account'),
    lastTransactionDate: z.string().nullable().describe('Date of the most recent transaction for this account'),
    asOfDate: z.string().describe('Date the balance was calculated as of'),
    balanceInterpretation: z.string().describe('Explanation of what the balance means for this account type'),
    message: z.string().describe('Summary message about the account balance and its significance'),
    performanceInfo: z.object({
      usedOptimizedQuery: z.boolean().describe('Whether optimized database functions were used'),
      queryTime: z.string().describe('Time taken for the query'),
    }).describe('Performance information about the query'),
  }),
  execute: async ({ context }) => {
    const organizationId = context.organizationId;
    const startTime = Date.now();

    try {
      // Use the optimized BalanceService
      const balanceData = await BalanceService.getAccountBalance(
        context.accountId,
        organizationId,
        context.asOfDate
      );

      const queryTime = `${Date.now() - startTime}ms`;

      // Generate balance interpretation
      const balanceInterpretation = generateBalanceInterpretation(
        balanceData.account_type,
        balanceData.balance,
        balanceData.transaction_count
      );

      // Generate summary message
      const message = `Account "${balanceData.name}" (${balanceData.code}) has a balance of $${balanceData.balance.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} as of ${balanceData.as_of_date}. This ${balanceData.account_type} account has ${balanceData.transaction_count} transaction(s)${balanceData.last_transaction_date ? ` with the most recent on ${balanceData.last_transaction_date}` : ' with no transaction history'}.`;

      return {
        account: {
          account_id: balanceData.account_id,
          account_code: balanceData.code,
          account_name: balanceData.name,
          account_type: balanceData.account_type,
          account_type_detail: balanceData.account_type_detail,
        },
        balance: balanceData.balance,
        totalDebits: balanceData.total_debits,
        totalCredits: balanceData.total_credits,
        transactionCount: balanceData.transaction_count,
        lastTransactionDate: balanceData.last_transaction_date,
        asOfDate: balanceData.as_of_date,
        balanceInterpretation,
        message,
        performanceInfo: {
          usedOptimizedQuery: true,
          queryTime,
        },
      };
    } catch (error: any) {
      throw new Error(`Failed to get account balance: ${error.message}`);
    }
  },
});

// Tool to get multiple account balances efficiently
export const getMultipleAccountBalances = createTool({
  id: 'Get Multiple Account Balances',
  description: 'Efficiently retrieve balances for multiple accounts at once. This is much faster than calling individual balance queries when you need balances for several accounts.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the accounts'),
    accountIds: z.array(z.string().uuid()).describe('Array of account IDs to retrieve balances for'),
    asOfDate: z.string().optional().describe('Calculate balances as of this date (YYYY-MM-DD). If not provided, uses current date'),
  }),
  outputSchema: z.object({
    balances: z.array(z.object({
      account: accountSchema,
      balance: z.number(),
      totalDebits: z.number(),
      totalCredits: z.number(),
      transactionCount: z.number(),
      lastTransactionDate: z.string().nullable(),
      balanceInterpretation: z.string(),
    })),
    asOfDate: z.string(),
    totalAccounts: z.number(),
    performanceInfo: z.object({
      queryTime: z.string(),
      accountsProcessed: z.number(),
    }),
  }),
  execute: async ({ context }) => {
    const organizationId = context.organizationId;
    const startTime = Date.now();

    try {
      const balances = await BalanceService.getMultipleAccountBalances(
        context.accountIds,
        organizationId,
        context.asOfDate
      );

      const queryTime = `${Date.now() - startTime}ms`;

      const formattedBalances = balances.map(balanceData => ({
        account: {
          account_id: balanceData.account_id,
          account_code: balanceData.code,
          account_name: balanceData.name,
          account_type: balanceData.account_type,
          account_type_detail: balanceData.account_type_detail,
        },
        balance: balanceData.balance,
        totalDebits: balanceData.total_debits,
        totalCredits: balanceData.total_credits,
        transactionCount: balanceData.transaction_count,
        lastTransactionDate: balanceData.last_transaction_date,
        balanceInterpretation: generateBalanceInterpretation(
          balanceData.account_type,
          balanceData.balance,
          balanceData.transaction_count
        ),
      }));

      return {
        balances: formattedBalances,
        asOfDate: context.asOfDate || new Date().toISOString().split('T')[0],
        totalAccounts: balances.length,
        performanceInfo: {
          queryTime,
          accountsProcessed: balances.length,
        },
      };
    } catch (error: any) {
      throw new Error(`Failed to get multiple account balances: ${error.message}`);
    }
  },
});

// Tool to check real-time data status (replaces refresh functionality)
export const checkRealTimeDataStatus = createTool({
  id: 'Check Real-Time Data Status',
  description: 'Check the status of real-time balance data. Since we use real-time views, data is always current and no refresh is needed.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to check status for'),
  }),
  outputSchema: z.object({
    success: z.boolean(),
    message: z.string(),
    dataFreshness: z.string(),
    lastChecked: z.string(),
  }),
  execute: async ({ context }) => {
    // Real-time views don't need organization-specific status checks
    // organizationId is available in context but not needed for this operation
    const currentTime = new Date().toISOString();

    return {
      success: true,
      message: 'Real-time views are always current - no refresh needed! Data is updated immediately when transactions are recorded.',
      dataFreshness: 'real-time',
      lastChecked: currentTime,
    };
  },
});
