import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define outstanding bill schema
const outstandingBillSchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.string(),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  description: z.string().nullable(),
  total_amount: z.number(),
  due_date: z.string().nullable(),
  total_paid: z.number(),
  outstanding_amount: z.number(),
  is_overdue: z.boolean(),
});

// Tool to get outstanding bills for a vendor
export const getOutstandingBills = createTool({
  id: 'Get Outstanding Bills',
  description: 'Get list of unpaid or partially paid bills for a vendor in the current organization. Use this when you need to manage accounts payable, check what bills need to be paid, prioritize vendor payments by due dates, or when the user asks about outstanding vendor bills. Essential for cash flow management, vendor payment planning, and avoiding late payment penalties. This tool helps identify which bills are overdue and require immediate attention.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter bills'),
    vendorId: z.string().uuid().describe('Vendor ID to get outstanding bills for'),
  }),
  outputSchema: z.object({
    success: z.boolean().describe('Whether the operation was successful'),
    outstandingBills: z.array(outstandingBillSchema),
    totalOutstanding: z.number().describe('Total amount of all outstanding bills'),
    overdueCount: z.number().describe('Number of overdue bills'),
    totalOverdue: z.number().describe('Total amount of overdue bills'),
    message: z.string().describe('Summary message about the outstanding bills and payment status'),
  }),
  execute: async ({ context }) => {
    try {
      // Get all bills for the vendor
      const { data: bills, error: billsError } = await supabase
        .from('transactions')
        .select(`
          transaction_id,
          transaction_type,
          transaction_date,
          reference_number,
          description,
          total_amount,
          due_date
        `)
        .eq('vendor_id', context.vendorId)
        .eq('organization_id', context.organizationId)
        .in('transaction_type', ['bill', 'expense'])
        .order('transaction_date', { ascending: false });

      if (billsError) {
        throw new Error(`Failed to get bills: ${billsError.message}`);
      }

      if (!bills || bills.length === 0) {
        return {
          success: true,
          outstandingBills: [],
          totalOutstanding: 0,
          overdueCount: 0,
          totalOverdue: 0,
          message: 'No bills found for this vendor'
        };
      }

      // Get all payments for these bills
      const billIds = bills.map(bill => bill.transaction_id);
      const { data: payments, error: paymentsError } = await supabase
        .from('bill_payments')
        .select('transaction_id, amount')
        .in('transaction_id', billIds);

      if (paymentsError) {
        throw new Error(`Failed to get payments: ${paymentsError.message}`);
      }

      // Calculate outstanding amounts
      const paymentsByBill = (payments || []).reduce((acc: any, payment: any) => {
        if (!acc[payment.transaction_id]) {
          acc[payment.transaction_id] = 0;
        }
        acc[payment.transaction_id] += payment.amount;
        return acc;
      }, {});

      const outstandingBills = bills
        .map(bill => {
          const totalPaid = paymentsByBill[bill.transaction_id] || 0;
          const outstandingAmount = bill.total_amount - totalPaid;

          return {
            ...bill,
            total_paid: totalPaid,
            outstanding_amount: outstandingAmount,
            is_overdue: bill.due_date && new Date(bill.due_date) < new Date(),
          };
        })
        .filter(bill => bill.outstanding_amount > 0.001); // Only include bills with outstanding amounts

      const totalOutstanding = outstandingBills.reduce((sum, bill) => sum + bill.outstanding_amount, 0);

      // Calculate overdue statistics
      const overdueBills = outstandingBills.filter(bill => bill.is_overdue);
      const overdueCount = overdueBills.length;
      const totalOverdue = overdueBills.reduce((sum, bill) => sum + bill.outstanding_amount, 0);

      // Generate comprehensive message
      let message = `Found ${outstandingBills.length} outstanding bill(s) with total amount of $${totalOutstanding.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      if (overdueCount > 0) {
        message += `. URGENT: ${overdueCount} bill(s) are overdue with total overdue amount of $${totalOverdue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} - these require immediate payment to avoid penalties`;
      } else if (outstandingBills.length > 0) {
        message += '. All bills are current with no overdue amounts';
      }

      if (outstandingBills.length > 0) {
        const upcomingDue = outstandingBills.filter(bill => bill.due_date && new Date(bill.due_date) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));
        if (upcomingDue.length > 0) {
          message += `. ${upcomingDue.length} bill(s) are due within the next 7 days`;
        }
      }

      return {
        success: true,
        outstandingBills,
        totalOutstanding,
        overdueCount,
        totalOverdue,
        message
      };

    } catch (error: any) {
      return {
        success: false,
        outstandingBills: [],
        totalOutstanding: 0,
        overdueCount: 0,
        totalOverdue: 0,
        message: `Error retrieving outstanding bills: ${error.message}`,
      };
    }
  },
});