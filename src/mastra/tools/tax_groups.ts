import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define reusable tax group schema
const taxGroupSchema = z.object({
  tax_group_id: z.string().uuid(),
  tax_group_name: z.string(),
  description: z.string().nullable(),
  is_active: z.boolean(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Define TaxGroup interface for type safety
interface TaxGroup {
  tax_group_id?: string;
  tax_group_name?: string;
  description?: string | null;
  is_active?: boolean;
  organization_id?: string;
  created_at?: string;
  updated_at?: string;
}

// Tool to get tax groups
export const getTaxGroups = createTool({
  id: 'Get Tax Groups',
  description: 'Retrieve tax groups for organizing and managing tax rates. Use this when you need to display available tax groups, find tax groups for item configuration, or when the user asks about tax group management. Tax groups allow you to bundle multiple tax rates together for easier application to items and transactions.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter tax groups'),
    taxGroupId: z.string().uuid().optional().describe('Specific tax group ID to retrieve'),
    isActive: z.boolean().optional().describe('Filter by active status'),
  }),
  outputSchema: z.object({
    taxGroups: z.array(taxGroupSchema),
    message: z.string().describe('Information about tax groups and their purpose'),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('tax_groups')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.taxGroupId) {
      query = query.eq('tax_group_id', context.taxGroupId);
    }

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query.order('tax_group_name');

    if (error) {
      throw new Error(`Failed to get tax groups: ${error.message}`);
    }

    const message = `Tax groups allow you to bundle multiple tax rates together for easier application to items and transactions. Each tax group can contain multiple tax rates (e.g., state tax + city tax) that are applied together when the group is selected. This simplifies tax management by allowing you to create predefined combinations of taxes that apply to different types of items or transactions.`;

    return {
      taxGroups: data,
      message
    };
  },
});

// Tool to create a tax group
export const createTaxGroup = createTool({
  id: 'Create Tax Group',
  description: 'Create a new tax group for organizing multiple tax rates together. Use this when the user wants to create a new tax group that can bundle multiple tax rates (like state + city tax) for easier application to items and transactions. Tax groups simplify tax management by allowing predefined combinations.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the tax group'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    name: z.string().describe('Tax group name'),
    description: z.string().optional().describe('Tax group description'),
    isActive: z.boolean().optional().default(true).describe('Whether the tax group is active'),
  }),
  outputSchema: z.object({
    taxGroup: taxGroupSchema,
    message: z.string().describe('Success message with next steps for tax group setup'),
  }),
  execute: async ({ context }) => {
    const { data, error } = await supabase
      .from('tax_groups')
      .insert({
        tax_group_name: context.name,
        description: context.description || null,
        is_active: context.isActive,
        organization_id: context.organizationId,
        created_by: context.userId,
        updated_by: context.userId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create tax group: ${error.message}`);
    }

    return {
      taxGroup: data,
      message: `Successfully created tax group "${context.name}". Next, you can add tax rates to this group using the tax group items management tools. This will allow you to bundle multiple tax rates together for easier application to items and transactions.`
    };
  },
});

// Tool to update a tax group
export const updateTaxGroup = createTool({
  id: 'Update Tax Group',
  description: 'Update an existing tax group\'s details. Use this when the user wants to modify tax group information like name, description, or active status. Be careful when deactivating tax groups as this may affect items and transactions that use them.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the tax group'),
    userId: z.string().uuid().describe('User ID for updated_by field'),
    taxGroupId: z.string().uuid().describe('Tax group ID to update'),
    name: z.string().optional().describe('Updated tax group name'),
    description: z.string().optional().describe('Updated tax group description'),
    isActive: z.boolean().optional().describe('Updated active status'),
  }),
  outputSchema: z.object({
    taxGroup: taxGroupSchema,
    message: z.string().describe('Success message with update details'),
  }),
  execute: async ({ context }) => {
    // Check if tax group exists
    const { data: existingTaxGroup, error: existingError } = await supabase
      .from('tax_groups')
      .select('*')
      .eq('tax_group_id', context.taxGroupId)
      .eq('organization_id', context.organizationId)
      .single();

    if (existingError || !existingTaxGroup) {
      throw new Error(`Tax group with ID ${context.taxGroupId} not found`);
    }

    // Build update object
    const updateFields: any = {
      updated_by: context.userId,
    };

    if (context.name !== undefined) updateFields.tax_group_name = context.name;
    if (context.description !== undefined) updateFields.description = context.description;
    if (context.isActive !== undefined) updateFields.is_active = context.isActive;

    const { data, error } = await supabase
      .from('tax_groups')
      .update(updateFields)
      .eq('tax_group_id', context.taxGroupId)
      .eq('organization_id', context.organizationId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update tax group: ${error.message}`);
    }

    const changes = [];
    if (context.name !== undefined) changes.push(`name to "${context.name}"`);
    if (context.description !== undefined) changes.push(`description`);
    if (context.isActive !== undefined) changes.push(`status to ${context.isActive ? 'active' : 'inactive'}`);

    return {
      taxGroup: data,
      message: `Successfully updated tax group "${existingTaxGroup.tax_group_name}"${changes.length > 0 ? ` - changed ${changes.join(', ')}` : ''}.`
    };
  },
});

// Tool to search for tax groups by name
export const searchTaxGroupsByName = createTool({
  id: 'Search Tax Groups By Name',
  description: 'Search for tax groups by name when you need to find specific tax groups for item configuration or when the user asks for tax groups containing certain text. Use this when you know part of a tax group name but need to find the exact group.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter tax groups'),
    nameContains: z.string().describe('Text to search for in tax group names'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    limit: z.number().min(1).max(100).default(10).describe('Maximum number of tax groups to return'),
  }),
  outputSchema: z.object({
    found: z.boolean().describe('Whether any tax groups were found'),
    taxGroups: z.array(taxGroupSchema),
    count: z.number().optional().describe('Number of tax groups found'),
    message: z.string().describe('Search result message'),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('tax_groups')
      .select('*')
      .ilike('tax_group_name', `%${context.nameContains}%`)
      .eq('organization_id', context.organizationId);

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query
      .order('tax_group_name')
      .limit(context.limit);

    if (error) {
      throw new Error(`Failed to search for tax groups: ${error.message}`);
    }

    if (!data || data.length === 0) {
      return {
        found: false,
        taxGroups: [],
        message: `No tax groups found with name containing "${context.nameContains}"`
      };
    }

    return {
      found: true,
      taxGroups: data,
      count: data.length,
      message: `Found ${data.length} tax group(s) with name containing "${context.nameContains}"`
    };
  },
});

// Define tax rate schema for the combined result
const taxRateSchema = z.object({
  tax_rate_id: z.string().uuid(),
  name: z.string(),
  rate: z.number(),
  description: z.string().nullable(),
  is_active: z.boolean(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Tool to get tax group with its tax rates
export const getTaxGroupWithRates = createTool({
  id: 'Get Tax Group With Rates',
  description: 'Retrieve a tax group with all its associated tax rates. Use this when you need to see the complete tax configuration for a group, including all individual tax rates that will be applied together. This is useful for reviewing tax setups or when configuring items.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter tax group'),
    taxGroupId: z.string().uuid().describe('Tax group ID to retrieve'),
  }),
  outputSchema: z.object({
    taxGroup: taxGroupSchema,
    taxRates: z.array(taxRateSchema),
    totalTaxRate: z.number().describe('Combined tax rate percentage from all rates in the group'),
    message: z.string().describe('Information about the tax group and its rates'),
  }),
  execute: async ({ context }) => {
    // First get the tax group
    const { data: taxGroup, error: taxGroupError } = await supabase
      .from('tax_groups')
      .select('*')
      .eq('tax_group_id', context.taxGroupId)
      .eq('organization_id', context.organizationId)
      .single();

    if (taxGroupError || !taxGroup) {
      throw new Error(`Tax group with ID ${context.taxGroupId} not found`);
    }

    // Then get all tax rates associated with this group
    const { data: taxGroupItems, error: itemsError } = await supabase
      .from('tax_group_items')
      .select(`
        tax_group_item_id,
        tax_rate_id,
        tax_rates:tax_rate_id (*)
      `)
      .eq('tax_group_id', context.taxGroupId)
      .eq('organization_id', context.organizationId);

    if (itemsError) {
      throw new Error(`Failed to get tax rates for group: ${itemsError.message}`);
    }

    // Extract tax rates from the join result
    const taxRates = taxGroupItems.map(item => item.tax_rates).filter(rate => rate !== null) as any[];

    // Calculate total tax rate
    const totalTaxRate = taxRates.reduce((sum, rate) => sum + ((rate as any)?.rate || 0), 0);

    return {
      taxGroup,
      taxRates,
      totalTaxRate,
      message: `Tax group "${taxGroup.tax_group_name}" contains ${taxRates.length} tax rate(s) with a combined rate of ${totalTaxRate}%. When this tax group is applied to items or transactions, all these tax rates will be applied together.`
    };
  },
});
