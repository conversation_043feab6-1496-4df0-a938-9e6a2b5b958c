import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';




// Define transaction status enum for validation
const returnStatusEnum = z.enum([
  'for_review',
  'approved'
]).optional();



// Helper function to validate date format (YYYY-MM-DD)
const isValidDateFormat = (dateString: string): boolean => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;

  const date = new Date(dateString);
  const timestamp = date.getTime();

  if (isNaN(timestamp)) return false;

  return date.toISOString().slice(0, 10) === dateString;
};

// Custom Zod transform for currency amounts - rounds to 2 decimal places
const currencyAmount = z.number()
  .transform(val => Math.round(val * 100) / 100)
  .refine(val => val >= 0, { message: 'Amount must be non-negative' });

// Helper function to check if an entity exists in the database
const entityExists = async (
  table: string,
  idField: string,
  id: string,
  organizationId: string
): Promise<boolean> => {
  const { data, error } = await supabase
    .from(table)
    .select(idField)
    .eq(idField, id)
    .eq('organization_id', organizationId)
    .single();

  if (error || !data) {
    return false;
  }

  return true;
};

// Helper function to check for duplicate purchase returns
const isDuplicatePurchaseReturn = async (
  referenceNumber: string,
  returnDate: string,
  organizationId: string,
  vendorId: string
): Promise<boolean> => {
  const { data, error } = await supabase
    .from('transactions')
    .select('transaction_id')
    .eq('transaction_type', 'purchase_return')
    .eq('reference_number', referenceNumber)
    .eq('transaction_date', returnDate)
    .eq('organization_id', organizationId)
    .eq('vendor_id', vendorId)
    .single();

  return !error && !!data;
};

// Helper function to validate original transaction
const validateOriginalTransaction = async (
  originalTransactionId: string,
  vendorId: string,
  organizationId: string
): Promise<{ transaction: any; vendorName: string }> => {
  // Get original transaction
  const { data: transaction, error: transactionError } = await supabase
    .from('transactions')
    .select('*')
    .eq('transaction_id', originalTransactionId)
    .eq('organization_id', organizationId)
    .single();

  if (transactionError || !transaction) {
    throw new Error(`Original transaction with ID ${originalTransactionId} not found`);
  }

  // Validate transaction type is returnable
  const returnableTypes = ['bill', 'expense'];
  if (!returnableTypes.includes(transaction.transaction_type)) {
    throw new Error(`Cannot create return for transaction type: ${transaction.transaction_type}. Only bills and expenses can be returned.`);
  }

  // Validate vendor matches
  if (transaction.vendor_id !== vendorId) {
    throw new Error(`Vendor ID mismatch. Return vendor (${vendorId}) must match original transaction vendor (${transaction.vendor_id})`);
  }

  // Get vendor name for logging
  const { data: vendor, error: vendorError } = await supabase
    .from('vendors')
    .select('vendor_name')
    .eq('vendor_id', vendorId)
    .eq('organization_id', organizationId)
    .single();

  if (vendorError || !vendor) {
    throw new Error(`Vendor with ID ${vendorId} not found`);
  }

  return {
    transaction,
    vendorName: vendor.vendor_name
  };
};

// Enhanced accounting line schema that can optionally include product/service details
const enhancedAccountingLineSchema = z.object({
  accountId: z.string().uuid().describe('Account ID for this line'),
  description: z.string().describe('Line item description'),
  debitAmount: currencyAmount.default(0).describe('Debit amount (automatically rounded to 2 decimal places)'),
  creditAmount: currencyAmount.default(0).describe('Credit amount (automatically rounded to 2 decimal places)'),
  classId: z.string().uuid().optional().describe('Class ID for tracking business segments'),
  projectId: z.string().uuid().optional().describe('Project ID for tracking project-based work'),

  // Optional product/service fields for return lines
  productServiceId: z.string().uuid().optional().describe('Product/Service ID (for returned items)'),
  quantity: z.number().positive().optional().describe('Quantity being returned'),
  unitPrice: currencyAmount.optional().describe('Unit price (automatically rounded to 2 decimal places)'),
  discountPercentage: z.number().min(0).max(100).default(0).describe('Discount percentage'),
  discountAmount: currencyAmount.default(0).describe('Discount amount (automatically rounded to 2 decimal places)'),
  taxRateId: z.string().uuid().optional().describe('Tax rate ID'),
  taxRate: z.number().min(0).max(100).default(0).describe('Tax rate percentage (0-100)'),
});

// Define purchase return schema for output
const purchaseReturnSchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.string(),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  document_number: z.string().nullable(),
  description: z.string(),
  vendor_id: z.string().uuid(),
  status: z.string(),
  subtotal_amount: z.number().nullable(),
  tax_amount: z.number().nullable(),
  total_amount: z.number(),
  original_transaction_id: z.string().uuid().nullable(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Tool to create a purchase return
export const createPurchaseReturn = createTool({
  id: 'Create Purchase Return',
  description: 'Create a purchase return (debit memo) when returning goods to vendors. This reduces accounts payable through manual accounting lines. Follows manual accounting lines architecture with strict double-entry validation and automatic floating-point precision handling.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the purchase return'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    returnDate: z.string().refine(isValidDateFormat)
      .describe('Date of the return (YYYY-MM-DD)'),
    originalTransactionId: z.string().uuid()
      .describe('Original bill or expense transaction ID that is being returned'),
    vendorId: z.string().uuid()
      .describe('Vendor ID (must match the original transaction vendor)'),
    referenceNumber: z.string().optional().describe('Reference or return authorization number'),
    description: z.string()
      .min(3, { message: 'Description must be at least 3 characters' })
      .describe('Description of the return'),
    status: returnStatusEnum.default('approved').describe('Return status (for_review or approved)'),
    subtotalAmount: currencyAmount.optional()
      .describe('Subtotal amount before tax (automatically rounded to 2 decimal places)'),
    taxAmount: currencyAmount.optional()
      .describe('Tax amount (automatically rounded to 2 decimal places)'),
    totalAmount: currencyAmount
      .describe('Total return amount (automatically rounded to 2 decimal places)'),

    lines: z.array(enhancedAccountingLineSchema)
    .min(2, { message: 'At least 2 line items are required for double-entry accounting' })
    .describe('Accounting line items (must have at least 2 for double-entry). Can optionally include product/service details for returned items. Tax can be specified using taxRate (percentage) or taxRateId (reference).'),
    skipDuplicateCheck: z.boolean().optional().default(false).describe('Skip checking for duplicate returns'),
    maxRetries: z.number().optional().default(1).describe('Maximum number of retries for database operations'),
  }),
  outputSchema: z.object({
    purchaseReturn: purchaseReturnSchema,
    returnLines: z.array(z.object({
      transaction_line_id: z.string().uuid(),
      account_id: z.string().uuid(),
      description: z.string(),
      debit_amount: z.number(),
      credit_amount: z.number(),
      class_id: z.string().uuid().nullable(),
      project_id: z.string().uuid().nullable(),
    })),
    message: z.string().describe('Success message with return details'),
    processingTimeMs: z.number().describe('Time taken to process the return'),
  }),
  execute: async ({ context }) => {
    // Extract organization and user IDs from context
    const organizationId = context.organizationId;
    const userId = context.userId;
    // Start timing the return processing
    const startTime = Date.now();
    let maxRetryCount = 0;
    let vendorName = '';
    let originalTransactionNumber = '';

    try {
      // Validate that lines are provided
      if (!context.lines || context.lines.length < 2) {
        throw new Error('At least 2 accounting line items are required for double-entry accounting.');
      }

      // Validate that each line has either debit OR credit (not both)
      for (const line of context.lines) {
        if (line.debitAmount > 0 && line.creditAmount > 0) {
          throw new Error('Each line must have either debit OR credit amount, not both');
        }
        if (line.debitAmount === 0 && line.creditAmount === 0) {
          throw new Error('Each line must have either a debit or credit amount');
        }
      }

      // Validate that debits equal credits (with automatic rounding to 2 decimal places)
      const totalDebits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.debitAmount, 0) * 100) / 100;
      const totalCredits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.creditAmount, 0) * 100) / 100;

      if (totalDebits !== totalCredits) {
        throw new Error(`Debits (${totalDebits}) must exactly equal credits (${totalCredits}). No tolerance allowed.`);
      }

      // Validate original transaction and get details
      const { transaction: originalTransaction, vendorName: vendName } = await validateOriginalTransaction(
        context.originalTransactionId,
        context.vendorId,
        organizationId
      );

      vendorName = vendName;
      originalTransactionNumber = originalTransaction.document_number || originalTransaction.reference_number || 'Unknown';

      // Validate return date is not before original transaction date
      const returnDate = new Date(context.returnDate);
      const originalDate = new Date(originalTransaction.transaction_date);
      if (returnDate < originalDate) {
        throw new Error(`Return date (${context.returnDate}) cannot be before original transaction date (${originalTransaction.transaction_date})`);
      }

      // Validate that each account exists
      for (const line of context.lines) {
        const accountExists = await entityExists('accounts', 'account_id', line.accountId, organizationId);
        if (!accountExists) {
          throw new Error(`Account with ID ${line.accountId} not found`);
        }

        if (line.classId) {
          const classExists = await entityExists('classes', 'class_id', line.classId, organizationId);
          if (!classExists) {
            throw new Error(`Class with ID ${line.classId} not found`);
          }
        }

        if (line.projectId) {
          const projectExists = await entityExists('projects', 'project_id', line.projectId, organizationId);
          if (!projectExists) {
            throw new Error(`Project with ID ${line.projectId} not found`);
          }
        }

        if (line.productServiceId) {
          const productExists = await entityExists('products_and_services', 'product_service_id', line.productServiceId, organizationId);
          if (!productExists) {
            throw new Error(`Product/Service with ID ${line.productServiceId} not found`);
          }
        }

        if (line.taxRateId) {
          const taxRateExists = await entityExists('tax_rates', 'tax_rate_id', line.taxRateId, organizationId);
          if (!taxRateExists) {
            throw new Error(`Tax rate with ID ${line.taxRateId} not found`);
          }
        }
      }

      // Check for duplicate returns if not explicitly skipped
      if (!context.skipDuplicateCheck && context.referenceNumber) {
        const isDuplicate = await isDuplicatePurchaseReturn(
          context.referenceNumber,
          context.returnDate,
          organizationId,
          context.vendorId
        );

        if (isDuplicate) {
          throw new Error(`Duplicate purchase return detected with reference number ${context.referenceNumber}`);
        }
      }

      // Validate lines with product/service information
      const linesWithProducts = context.lines.filter(line => line.productServiceId);
      if (linesWithProducts.length > 0) {
        for (const line of linesWithProducts) {
          if (line.quantity && line.quantity <= 0) {
            throw new Error('Quantity must be positive for product/service lines');
          }
          if (line.unitPrice && line.unitPrice < 0) {
            throw new Error('Unit price cannot be negative');
          }
          if (line.discountPercentage < 0 || line.discountPercentage > 100) {
            throw new Error('Discount percentage must be between 0 and 100');
          }
          if (line.taxRate < 0 || line.taxRate > 100) {
            throw new Error('Tax rate must be between 0 and 100');
          }
        }
      }

      // Generate document number using the database function
      const { data: documentNumberResult, error: documentNumberError } = await supabase
        .rpc('generate_document_number', {
          p_transaction_type: 'purchase_return',
          p_organization_id: organizationId
        });

      if (documentNumberError) {
        throw new Error(`Failed to generate return number: ${documentNumberError.message}`);
      }

      const documentNumber = documentNumberResult;

      // Start a Supabase transaction with retry logic
      let purchaseReturn: any;
      let returnError;
      let retryCount = 0;
      const maxRetries = context.maxRetries || 1;

      while (retryCount < maxRetries) {
        const returnData = {
          transaction_type: 'purchase_return',
          transaction_date: context.returnDate,
          reference_number: context.referenceNumber,
          document_number: documentNumber,
          description: context.description,
          vendor_id: context.vendorId,
          status: context.status || 'approved',
          subtotal_amount: context.subtotalAmount || context.totalAmount,
          tax_amount: context.taxAmount || 0,
          total_amount: context.totalAmount,
          original_transaction_id: context.originalTransactionId,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        };

        const result = await supabase
          .from('transactions')
          .insert(returnData)
          .select()
          .single();

        purchaseReturn = result.data;
        returnError = result.error;

        if (!returnError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount >= maxRetries) {
          throw new Error(`Failed to create purchase return after ${maxRetries} attempts: ${returnError?.message}`);
        }

        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
      }

      // Insert transaction lines with retry logic (including optional product/service details)
      const transactionLines = context.lines.map((line: any) => {
        const lineData: any = {
          transaction_id: purchaseReturn.transaction_id,
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
        };

        // Add optional product/service details if provided
        if (line.productServiceId) {
          lineData.product_service_id = line.productServiceId;
          lineData.quantity = line.quantity || null;
          lineData.unit_price = line.unitPrice || null;
          lineData.discount_percentage = line.discountPercentage || 0;
          lineData.discount_amount = line.discountAmount || 0;
          lineData.tax_rate_id = line.taxRateId || null;
          lineData.tax_rate = line.taxRate || 0;
        }

        return {
          ...lineData,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        };
      });

      const { error: linesError } = await supabase
        .from('transaction_lines')
        .insert(transactionLines);

      if (linesError) {
        // If line insertion fails, we should clean up the transaction
        await supabase
          .from('transactions')
          .delete()
          .eq('transaction_id', purchaseReturn.transaction_id);

        throw new Error(`Failed to create return lines: ${linesError.message}`);
      }



      return {
        purchaseReturn,
        returnLines: context.lines.map((line: any, index: number) => ({
          transaction_line_id: `line_${index + 1}`, // This would be the actual ID from the database
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
        })),
        message: `Successfully created purchase return ${documentNumber} for ${vendorName} with ${context.lines.length} line items${linesWithProducts.length > 0 ? ` (${linesWithProducts.length} with product/service details)` : ''}. Original transaction: ${originalTransactionNumber}.`,
        processingTimeMs: Date.now() - startTime
      };

    } catch (error: any) {
      throw error;
    }
  },
});
