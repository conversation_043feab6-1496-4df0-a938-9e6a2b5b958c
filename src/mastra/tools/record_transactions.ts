/**
 * UPDATED RECORD TRANSACTIONS TOOL - Manual Accounting Lines Architecture
 *
 * This tool implements the new manual accounting lines architecture where:
 * - AI agents must provide all accounting lines manually
 * - Full control over debits, credits, and account assignments
 * - Minimum 2 lines required for proper double-entry bookkeeping
 * - Optional product/service details can be included in lines
 * - Strict validation with zero tolerance for balance violations
 * - Automatic floating-point precision handling
 */

import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';



// Define transaction type enum for validation
const transactionTypeEnum = z.enum([
  'sales_invoice',
  'sales_receipt',
  'customer_payment',
  'sales_return',           // ✅ Replaces customer_refund
  'customer_cash_refund',   // ✅ New - Cash refund to customer
  'bill',
  'expense',
  'vendor_payment',
  'purchase_return',        // ✅ Replaces vendor_refund
  'vendor_cash_refund',     // ✅ New - Cash refund from vendor
  'journal_entry'
]);

// Define transaction status enum for validation
const transactionStatusEnum = z.enum([
  'for_review',
  'approved'
]).optional();

// Define payment terms enum for validation
const paymentTermsEnum = z.enum([
  'due_on_receipt',
  'net_15',
  'net_30',
  'net_45',
  'net_60',
  'custom'
]).optional();

// Helper function to validate date format (YYYY-MM-DD)
const isValidDateFormat = (dateString: string): boolean => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;

  const date = new Date(dateString);
  const timestamp = date.getTime();

  if (isNaN(timestamp)) return false;

  return date.toISOString().slice(0, 10) === dateString;
};

// Helper function to calculate due date by adding days to a given date
const calculateDueDate = (transactionDate: string, daysToAdd: number): string => {
  const date = new Date(transactionDate);
  date.setDate(date.getDate() + daysToAdd);
  return date.toISOString().slice(0, 10);
};

// Custom Zod transform for currency amounts - rounds to 2 decimal places
const currencyAmount = z.number()
  .transform(val => Math.round(val * 100) / 100)
  .refine(val => val >= 0, { message: 'Amount must be non-negative' });

// Helper function to check if an entity exists in the database
const entityExists = async (
  table: string,
  idField: string,
  id: string,
  organizationId: string
): Promise<boolean> => {
  const { data, error } = await supabase
    .from(table)
    .select(idField)
    .eq(idField, id)
    .eq('organization_id', organizationId)
    .single();

  if (error || !data) {
    return false;
  }

  return true;
};

// Helper function to check for duplicate transactions
const isDuplicateTransaction = async (
  referenceNumber: string,
  transactionDate: string,
  organizationId: string,
  entityId?: string
): Promise<boolean> => {
  if (!referenceNumber) return false; // Can't check for duplicates without a reference number

  let query = supabase
    .from('transactions')
    .select('transaction_id')
    .eq('reference_number', referenceNumber)
    .eq('transaction_date', transactionDate)
    .eq('organization_id', organizationId);

  // Check for either customer_id or vendor_id match if entity provided
  if (entityId) {
    query = query.or(`customer_id.eq.${entityId},vendor_id.eq.${entityId}`);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error checking for duplicate transaction:', error);
    return false; // If we can't check, assume it's not a duplicate
  }

  return data && data.length > 0;
};

// Enhanced accounting line schema with precision handling and product/service support
const enhancedAccountingLineSchema = z.object({
  // Required accounting fields
  accountId: z.string().uuid().describe('Account ID'),
  description: z.string().describe('Line description'),
  debitAmount: currencyAmount.default(0).describe('Debit amount (automatically rounded to 2 decimal places)'),
  creditAmount: currencyAmount.default(0).describe('Credit amount (automatically rounded to 2 decimal places)'),

  // Optional classification
  classId: z.string().uuid().optional().describe('Class ID'),
  projectId: z.string().uuid().optional().describe('Project ID'),

  // Optional product/service fields
  productServiceId: z.string().uuid().optional().describe('Product/Service ID'),
  quantity: z.number().positive().optional().describe('Quantity'),
  unitPrice: currencyAmount.optional().describe('Unit price (automatically rounded to 2 decimal places)'),
  discountPercentage: z.number().min(0).max(100).default(0).describe('Discount percentage'),
  discountAmount: currencyAmount.default(0).describe('Discount amount (automatically rounded to 2 decimal places)'),
  taxRateId: z.string().uuid().optional().describe('Tax rate ID'),
  taxRate: z.number().min(0).max(100).default(0).describe('Tax rate percentage')
});

// Define payment application schema for linking payments to invoices/bills
const paymentApplicationSchema = z.object({
  transactionId: z.string().uuid().describe('Invoice or Bill transaction ID being paid'),
  amount: z.number().positive().describe('Amount being applied to this invoice/bill'),
  paymentMethod: z.string().optional().describe('Payment method (cash, check, credit_card, bank_transfer, etc.)'),
  reference: z.string().optional().describe('Payment reference number or check number'),
});

// REMOVED: Auto-generation logic - Manual accounting lines architecture requires
// AI agents to provide all accounting lines manually for full control

// Define transaction schema for output
const transactionSchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.string(),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  document_number: z.string().nullable(),
  description: z.string(),
  customer_id: z.string().uuid().nullable(),
  vendor_id: z.string().uuid().nullable(),
  due_date: z.string().nullable(),
  status: z.string(),
  payment_terms: z.string().nullable(),
  subtotal_amount: z.number().nullable(),
  tax_amount: z.number().nullable(),
  total_amount: z.number(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Tool to record a transaction with manual accounting lines
export const recordTransaction = createTool({
  id: 'Record Transaction',
  description: 'Record a financial transaction with manual double-entry accounting lines. This tool implements the new manual accounting lines architecture where AI agents must provide all accounting lines manually for full control. Supports all transaction types with optional product/service details in lines. Includes automatic floating-point precision handling and strict validation.',
  inputSchema: z.object({
    // Required context fields for self-contained pattern
    organizationId: z.string().uuid().describe('Organization ID for the transaction'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),

    // Basic transaction info
    transactionType: transactionTypeEnum.describe('Type of transaction'),
    transactionDate: z.string()
      .refine(val => isValidDateFormat(val), {
        message: 'Transaction date must be in YYYY-MM-DD format',
      })
      .describe('Transaction date (YYYY-MM-DD)'),
    description: z.string()
      .min(3, { message: 'Description must be at least 3 characters' })
      .describe('Transaction description'),
    totalAmount: currencyAmount
      .describe('Total transaction amount (automatically rounded to 2 decimal places)'),

    // Entity references (customer/vendor)
    customerId: z.string().uuid().optional()
      .describe('Customer ID (required for sales transactions)'),
    vendorId: z.string().uuid().optional()
      .describe('Vendor ID (required for purchase transactions)'),

    // Manual accounting lines (REQUIRED)
    lines: z.array(enhancedAccountingLineSchema)
      .min(2, { message: 'At least 2 line items are required for double-entry accounting' })
      .describe('Accounting line items (must have at least 2 for double-entry). Can optionally include product/service details for revenue/expense lines. Tax can be specified using taxRate (percentage) or taxRateId (reference).'),

    // Optional transaction fields
    referenceNumber: z.string().optional()
      .describe('Reference or document number'),
    dueDate: z.string().optional()
      .refine(val => val === undefined || isValidDateFormat(val), {
        message: 'Due date must be in YYYY-MM-DD format',
      })
      .describe('Due date (YYYY-MM-DD). If not provided, defaults to 30 days from transaction date.'),
    status: z.enum(['approved', 'for_review']).default('approved')
      .describe('Transaction status (for_review or approved)'),
    paymentTerms: paymentTermsEnum
      .describe('Payment terms for the transaction'),

    // Tax and amounts
    subtotalAmount: currencyAmount.optional()
      .describe('Subtotal amount before tax (automatically rounded to 2 decimal places)'),
    taxAmount: currencyAmount.optional()
      .describe('Tax amount (automatically rounded to 2 decimal places)'),
    taxAccountId: z.string().uuid().optional()
      .describe('Tax account ID (required when lines have tax rates)'),

    // Payment applications for payment transactions
    paymentApplications: z.array(paymentApplicationSchema).optional()
      .describe('For payment transactions: specify which invoices/bills are being paid'),

    // Processing options
    skipDuplicateCheck: z.boolean().optional().default(false)
      .describe('Skip checking for duplicate transactions'),
    maxRetries: z.number().optional().default(1)
      .describe('Maximum number of retries for database operations'),
  }),
  outputSchema: z.object({
    transaction: transactionSchema,
    transactionLines: z.array(z.object({
      transaction_line_id: z.string().uuid(),
      account_id: z.string().uuid(),
      description: z.string(),
      debit_amount: z.number(),
      credit_amount: z.number(),
      class_id: z.string().uuid().nullable(),
      project_id: z.string().uuid().nullable(),
    })),
    message: z.string().describe('Success message with transaction details'),
    processingTimeMs: z.number().describe('Time taken to process the transaction'),
  }),
  execute: async ({ context }) => {
    // Extract organization ID from context (self-contained pattern)
    const organizationId = context.organizationId;
    // Start timing the transaction processing
    const startTime = Date.now();
    let maxRetryCount = 0;

    try {
      // Validate transaction type-specific requirements
      const customerRequiredTypes = ['sales_invoice', 'sales_receipt', 'customer_payment', 'sales_return', 'customer_cash_refund'];
      const vendorRequiredTypes = ['bill', 'expense', 'vendor_payment', 'purchase_return', 'vendor_cash_refund'];

      if (customerRequiredTypes.includes(context.transactionType) && !context.customerId) {
        throw new Error(`Customer ID is required for transaction type: ${context.transactionType}`);
      }

      if (vendorRequiredTypes.includes(context.transactionType) && !context.vendorId) {
        throw new Error(`Vendor ID is required for transaction type: ${context.transactionType}`);
      }

      // Auto-calculate due date for sales invoices and bills if not provided (default to 30 days)
      if (['sales_invoice', 'bill'].includes(context.transactionType) && !context.dueDate) {
        context.dueDate = calculateDueDate(context.transactionDate, 30);
      }

      // STRICT VALIDATION: Manual accounting lines are required
      if (!context.lines || context.lines.length < 2) {
        throw new Error('At least 2 accounting line items are required for double-entry accounting. Manual lines must be provided - no auto-generation.');
      }

      // STRICT VALIDATION: Each line must have either debit OR credit (not both)
      for (const line of context.lines) {
        if (line.debitAmount > 0 && line.creditAmount > 0) {
          throw new Error('Each line must have either debit OR credit amount, not both');
        }
        if (line.debitAmount === 0 && line.creditAmount === 0) {
          throw new Error('Each line must have either a debit or credit amount');
        }
      }

      // ZERO TOLERANCE: Debits must exactly equal credits (with floating-point precision handling)
      const totalDebits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.debitAmount, 0) * 100) / 100;
      const totalCredits = Math.round(context.lines.reduce((sum: number, line: any) => sum + line.creditAmount, 0) * 100) / 100;

      if (totalDebits !== totalCredits) {
        throw new Error(`Debits (${totalDebits}) must exactly equal credits (${totalCredits}). No tolerance allowed.`);
      }

      // Validate all accounts exist
      for (const line of context.lines) {
        const accountExists = await entityExists('accounts', 'account_id', line.accountId, organizationId);
        if (!accountExists) {
          throw new Error(`Account with ID ${line.accountId} not found`);
        }

        // Validate optional classification fields
        if (line.classId) {
          const classExists = await entityExists('classes', 'class_id', line.classId, organizationId);
          if (!classExists) {
            throw new Error(`Class with ID ${line.classId} not found`);
          }
        }

        if (line.projectId) {
          const projectExists = await entityExists('projects', 'project_id', line.projectId, organizationId);
          if (!projectExists) {
            throw new Error(`Project with ID ${line.projectId} not found`);
          }
        }
      }

      // Enhanced product/service validation
      const linesWithProducts = context.lines.filter(line => line.productServiceId);
      if (linesWithProducts.length > 0) {
        // Check if any lines have tax rates and ensure tax account is provided
        const hasLinesWithTax = linesWithProducts.some(line => line.taxRateId);
        if (hasLinesWithTax && !context.taxAccountId) {
          throw new Error('Tax account ID is required when lines have tax rates. Please provide taxAccountId parameter.');
        }

        // Validate each product/service and related entities
        for (const line of linesWithProducts) {
          const productExists = await entityExists('products_and_services', 'product_service_id', line.productServiceId, organizationId);
          if (!productExists) {
            throw new Error(`Product/Service with ID ${line.productServiceId} not found`);
          }

          // Validate tax rate if provided
          if (line.taxRateId) {
            const taxRateExists = await entityExists('tax_rates', 'tax_rate_id', line.taxRateId, organizationId);
            if (!taxRateExists) {
              throw new Error(`Tax rate with ID ${line.taxRateId} not found`);
            }
          }
        }
      }

      // Validate entity exists (customer/vendor)
      if (context.customerId) {
        const customerExists = await entityExists('customers', 'customer_id', context.customerId, organizationId);
        if (!customerExists) {
          throw new Error(`Customer with ID ${context.customerId} not found`);
        }
      }

      if (context.vendorId) {
        const vendorExists = await entityExists('vendors', 'vendor_id', context.vendorId, organizationId);
        if (!vendorExists) {
          throw new Error(`Vendor with ID ${context.vendorId} not found`);
        }
      }

      // Validate tax account if provided
      if (context.taxAccountId) {
        const taxAccountExists = await entityExists('accounts', 'account_id', context.taxAccountId, organizationId);
        if (!taxAccountExists) {
          throw new Error(`Tax account with ID ${context.taxAccountId} not found`);
        }
      }

      // Check for duplicate transactions (if reference number provided)
      if (!context.skipDuplicateCheck && context.referenceNumber) {
        const isDuplicate = await isDuplicateTransaction(
          context.referenceNumber,
          context.transactionDate,
          organizationId,
          context.customerId || context.vendorId
        );

        if (isDuplicate) {
          throw new Error(`Duplicate transaction detected with reference number ${context.referenceNumber}`);
        }
      }

      // REMOVED: itemLines validation - Manual accounting lines architecture
      // Product/service details are now included directly in the lines array

      // Validate payment applications if provided
      if (context.paymentApplications && context.paymentApplications.length > 0) {
        // Check if transaction type is a payment type
        const paymentTypes = ['customer_payment', 'vendor_payment', 'customer_cash_refund', 'vendor_cash_refund'];
        if (!paymentTypes.includes(context.transactionType)) {
          throw new Error(`Payment applications are only applicable for payment and cash refund transaction types`);
        }

        // Validate each payment application
        for (const application of context.paymentApplications) {
          // Check if the transaction being paid exists
          const { data: targetTransaction, error: targetError } = await supabase
            .from('transactions')
            .select('*')
            .eq('transaction_id', application.transactionId)
            .eq('organization_id', organizationId)
            .single();

          if (targetError || !targetTransaction) {
            throw new Error(`Transaction with ID ${application.transactionId} not found`);
          }

          // Validate transaction type compatibility
          if (context.transactionType === 'customer_payment' || context.transactionType === 'customer_cash_refund') {
            if (!['sales_invoice', 'sales_receipt', 'sales_return'].includes(targetTransaction.transaction_type)) {
              throw new Error(`Customer payments and refunds can only be applied to sales invoices, sales receipts, or sales returns`);
            }
            // Ensure customer matches
            if (targetTransaction.customer_id !== context.customerId) {
              throw new Error(`Payment customer ID must match the invoice/receipt/return customer ID`);
            }
          } else if (context.transactionType === 'vendor_payment' || context.transactionType === 'vendor_cash_refund') {
            if (!['bill', 'expense', 'purchase_return'].includes(targetTransaction.transaction_type)) {
              throw new Error(`Vendor payments and refunds can only be applied to bills, expenses, or purchase returns`);
            }
            // Ensure vendor matches
            if (targetTransaction.vendor_id !== context.vendorId) {
              throw new Error(`Payment vendor ID must match the bill/expense/return vendor ID`);
            }
          }
        }

        // Validate total payment applications don't exceed payment amount
        const totalApplications = context.paymentApplications.reduce((sum, app) => sum + app.amount, 0);
        if (Math.abs(totalApplications - context.totalAmount) > 0.001) {
          throw new Error(`Total payment applications (${totalApplications}) must equal payment amount (${context.totalAmount})`);
        }
      }

      // Generate document number using the database function
      const { data: documentNumberResult, error: documentNumberError } = await supabase
        .rpc('generate_document_number', {
          p_transaction_type: context.transactionType,
          p_organization_id: organizationId
        });

      if (documentNumberError) {
        throw new Error(`Failed to generate document number: ${documentNumberError.message}`);
      }

      const documentNumber = documentNumberResult;

      // Start a Supabase transaction with retry logic
      let transaction: any;
      let transactionError;
      let retryCount = 0;
      const maxRetries = context.maxRetries || 1;

      while (retryCount < maxRetries) {
        const transactionData = {
          transaction_type: context.transactionType,
          transaction_date: context.transactionDate,
          reference_number: context.referenceNumber,
          document_number: documentNumber,
          description: context.description,
          customer_id: context.customerId,
          vendor_id: context.vendorId,
          due_date: context.dueDate,
          status: context.status || 'approved',
          payment_terms: context.paymentTerms,
          subtotal_amount: context.subtotalAmount || context.totalAmount,
          tax_amount: context.taxAmount || 0,
          total_amount: context.totalAmount,
          organization_id: context.organizationId,
          created_by: context.userId,
          updated_by: context.userId,
        };

        const result = await supabase
          .from('transactions')
          .insert(transactionData)
          .select()
          .single();

        transaction = result.data;
        transactionError = result.error;

        if (!transactionError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (transactionError) {
        throw new Error(`Failed to create transaction after ${retryCount} attempts: ${transactionError.message}`);
      }

      // Create transaction lines with optional product/service details
      const transactionLines = context.lines.map((line: any) => {
        const lineData: any = {
          transaction_id: transaction.transaction_id,
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
          organization_id: context.organizationId,
          created_by: context.userId,
          updated_by: context.userId,
        };

        // Add product/service fields if provided
        if (line.productServiceId) {
          lineData.product_service_id = line.productServiceId;
          lineData.quantity = line.quantity || null;
          lineData.unit_price = line.unitPrice || null;
          lineData.discount_percentage = line.discountPercentage || 0;
          lineData.discount_amount = line.discountAmount || 0;
          lineData.tax_rate_id = line.taxRateId || null;
          lineData.tax_rate = line.taxRate || 0;
        }

        return lineData;
      });

      retryCount = 0;
      let linesError;

      while (retryCount < maxRetries) {
        const result = await supabase
          .from('transaction_lines')
          .insert(transactionLines);

        linesError = result.error;

        if (!linesError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (linesError) {
        throw new Error(`Failed to create transaction lines after ${retryCount} attempts: ${linesError.message}`);
      }

      // REMOVED: itemLines processing - Manual accounting lines architecture
      // Inventory tracking is handled automatically by database triggers
      // The products_and_services table tracks quantities automatically when transaction_lines
      // with product_service_id are inserted/updated/deleted via database triggers

      // Process payment applications if provided
      if (context.paymentApplications && context.paymentApplications.length > 0) {
        const paymentRecords = context.paymentApplications.map((application: any) => ({
          payment_transaction_id: transaction.transaction_id,
          transaction_id: application.transactionId,
          amount: application.amount,
          payment_date: context.transactionDate,
          payment_method: application.paymentMethod || null,
          reference: application.reference || context.referenceNumber || null,
          organization_id: context.organizationId,
          created_by: context.userId,
          updated_by: context.userId,
        }));

        // Determine which table to use based on transaction type
        const customerTransactionTypes = ['customer_payment', 'customer_cash_refund'];
        const tableName = customerTransactionTypes.includes(context.transactionType) ? 'invoice_payments' : 'bill_payments';

        // Insert payment records with retry logic
        retryCount = 0;
        let paymentError;

        while (retryCount < maxRetries) {
          const result = await supabase
            .from(tableName)
            .insert(paymentRecords);

          paymentError = result.error;

          if (!paymentError) {
            break; // Success, exit the retry loop
          }

          retryCount++;
          // Update max retry count for logging
          maxRetryCount = Math.max(maxRetryCount, retryCount);

          if (retryCount < maxRetries) {
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
          }
        }

        if (paymentError) {
          throw new Error(`Failed to create payment records after ${retryCount} attempts: ${paymentError.message}`);
        }
      }

      // Update max retry count
      maxRetryCount = Math.max(maxRetryCount, context.maxRetries || 1);



      return {
        transaction,
        transactionLines: context.lines.map((line: any, index: number) => ({
          transaction_line_id: `line_${index + 1}`, // This would be the actual ID from the database
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
        })),
        message: `Successfully recorded ${context.transactionType} transaction ${documentNumber} with ${context.lines.length} accounting line items${context.paymentApplications ? ` and ${context.paymentApplications.length} payment applications` : ''}.`,
        processingTimeMs: Date.now() - startTime
      };
    } catch (error: any) {
      // Provide detailed error information - throw error to match expected output schema
      throw new Error(`Transaction failed: ${error.message}`);
    }
  },
});

