# DeepLedger AI Tools Documentation

This directory contains specialized accounting tools for the DeepLedger AI agent, built using the Mastra AI framework. These tools enable AI-powered double-entry bookkeeping and financial management through natural language interactions.

## 🏗️ Architecture Overview

All tools follow the Mastra framework pattern:
- **Input Schema**: Zod validation for type safety
- **Output Schema**: Structured response format
- **Execute Function**: Core business logic with error handling
- **Automatic Rounding**: All monetary amounts rounded to 2 decimal places
- **Organization Context**: Multi-tenant support with organization isolation

## 📊 Tool Categories

### 1. Master Data Management

#### Account Management
- **`getAccounts`** - Retrieve chart of accounts with filtering
- **`createAccount`** - Create new accounting accounts
- **`updateAccount`** - Modify existing account details
- **`getAccountByCode`** - Find account by unique code
- **`searchAccountsByName`** - Search accounts by name patterns

#### Customer Management
- **`getCustomers`** - List all customers with pagination
- **`createCustomer`** - Add new customer records
- **`updateCustomer`** - Update customer information
- **`searchCustomersByName`** - Find customers by name patterns

#### Vendor Management
- **`getVendors`** - List all vendors with filtering
- **`createVendor`** - Create new vendor records
- **`updateVendor`** - Modify vendor details
- **`searchVendorsByName`** - Search vendors by name patterns

#### Product & Service Management
- **`getProductsAndServices`** - List inventory items and services
- **`createProductOrService`** - Add new products/services
- **`updateProductOrService`** - Update item details
- **`searchProductsAndServices`** - Find items by name/description
- **`getProductServiceAnalytics`** - Get sales/purchase analytics
- **`getInventoryStatus`** - Check current inventory levels

#### Project & Class Management
- **`getProjects`** - List all projects
- **`createProject`** - Create new project tracking
- **`updateProject`** - Update project details
- **`searchProjects`** - Find projects by criteria
- **`getHierarchicalProjects`** - Get project hierarchy
- **`getClasses`** - List business segments/departments
- **`createClass`** - Create new class/department
- **`updateClass`** - Modify class details
- **`searchClassesByName`** - Find classes by name

#### Tax Management
- **`getTaxRates`** - List all tax rates
- **`createTaxRate`** - Create new tax rates
- **`updateTaxRate`** - Modify tax rate details
- **`searchTaxRatesByName`** - Find tax rates by name
- **`getTaxGroups`** - List tax groups
- **`createTaxGroup`** - Create tax group combinations
- **`updateTaxGroup`** - Update tax group details
- **`searchTaxGroupsByName`** - Find tax groups by name
- **`getTaxGroupWithRates`** - Get group with associated rates
- **`getTaxGroupItems`** - List items in tax groups
- **`addTaxRateToGroup`** - Add rate to tax group
- **`removeTaxRateFromGroup`** - Remove rate from group
- **`getTaxRatesNotInGroup`** - Find unassigned tax rates

### 2. Transaction Recording

#### Sales Transactions
- **`createSalesInvoice`** - Create customer invoices (A/R)
- **`createSalesReceipt`** - Record immediate sales (Cash/Bank)
- **`createSalesReturn`** - Process sales returns/refunds
- **`recordCustomerCashRefund`** - Issue cash refunds to customers

#### Purchase Transactions
- **`createBill`** - Record vendor bills (A/P)
- **`createExpense`** - Record direct expenses
- **`createPurchaseReturn`** - Process purchase returns
- **`recordVendorCashRefund`** - Record vendor refunds

#### Payment Processing
- **`recordCustomerPayment`** - Apply customer payments to invoices
- **`recordVendorPayment`** - Pay vendor bills

#### General Transactions
- **`recordTransaction`** - Manual journal entries with full control
- **`createJournalEntry`** - Standard journal entry creation

### 3. Smart Workflows

#### Intelligent Account Creation
- **`createNewAccountWorkflowTool`** - Advanced account creation with:
  - Duplicate detection (exact name, code, similar names)
  - Smart code generation based on account type
  - Comprehensive validation and conflict resolution
  - Detailed workflow execution tracking

### 4. Financial Reporting & Analysis

#### Balance Inquiries
- **`getAccountBalance`** - Basic account balance lookup
- **`getAccountBalanceOptimized`** - High-performance balance calculation
- **`getMultipleAccountBalances`** - Batch balance retrieval
- **`checkRealTimeDataStatus`** - Verify data freshness

#### Financial Statements
- **`getTrialBalance`** - Basic trial balance report
- **`getTrialBalanceOptimized`** - Optimized trial balance with performance metrics
- **`getTrialBalanceSummary`** - Condensed trial balance view
- **`compareTrialBalances`** - Period-over-period comparison
- **`getBalanceSheet`** - Statement of financial position
- **`getProfitAndLossAccount`** - Income statement/P&L

#### Transaction Analysis
- **`getTransactions`** - Transaction history with filtering
- **`getOutstandingInvoices`** - Unpaid customer invoices (A/R aging)
- **`getOutstandingBills`** - Unpaid vendor bills (A/P aging)

## 🔧 Key Features

### Automatic Precision Handling
All monetary amounts are automatically rounded to exactly 2 decimal places using:
```typescript
const currencyAmount = z.number()
  .transform(val => Math.round(val * 100) / 100)
  .refine(val => val >= 0, { message: 'Amount must be non-negative' });
```

### Double-Entry Validation
Transaction tools automatically validate:
- Debits equal credits (zero tolerance)
- Minimum 2 lines per transaction
- Each line has either debit OR credit (not both)

### Enhanced Product/Service Integration
Revenue and expense lines can include:
- **Product/Service ID**: Links to inventory items
- **Quantity & Unit Price**: Automatic calculations
- **Tax Rates**: Per-line tax handling
- **Discounts**: Percentage or fixed amount
- **Project/Class Tracking**: Detailed cost allocation

### Intelligent Search & Validation
- Fuzzy name matching for entities
- Existence validation before transactions
- Duplicate detection and prevention
- Smart code generation for accounts

### Performance Optimization
- Optimized database functions for balance calculations
- Batch operations for multiple records
- Real-time views for instant data freshness
- Retry logic with exponential backoff

## 🚀 Usage Patterns

### Creating Sales Invoices
```typescript
// 1. Search for entities first
const customer = await searchCustomersByName({ nameContains: "ABC Corp" });
const revenueAccount = await searchAccountsByName({ nameContains: "Sales Revenue" });
const arAccount = await searchAccountsByName({ nameContains: "Accounts Receivable" });

// 2. Create invoice with found IDs
const invoice = await createSalesInvoice({
  customerId: customer.customer_id,
  lines: [
    {
      accountId: revenueAccount.account_id,
      description: "Sales Revenue",
      creditAmount: 1000.00
    },
    {
      accountId: arAccount.account_id,
      description: "Accounts Receivable",
      debitAmount: 1000.00
    }
  ]
});
```

### Smart Account Creation
```typescript
// Intelligent workflow with duplicate detection
const result = await createNewAccountWorkflowTool({
  accountName: "Professional Services Revenue",
  accountType: "revenue",
  accountTypeDetail: "revenue"
});
// Returns: created/exists/error with detailed analysis
```

### Optimized Reporting
```typescript
// High-performance balance calculation
const balance = await getAccountBalanceOptimized({
  accountId: "uuid-here",
  organizationId: "org-uuid"
});
// Includes: balance, interpretation, performance metrics
```

## 🔒 Security & Validation

- **Organization Isolation**: All queries filtered by organization_id
- **Entity Validation**: Existence checks before transactions
- **Input Sanitization**: Zod schema validation
- **Duplicate Prevention**: Smart duplicate detection
- **Audit Trail**: Created/updated by tracking
- **Error Handling**: Comprehensive error messages

## 📈 Performance Features

- **Optimized Queries**: Database functions for complex calculations
- **Batch Operations**: Multiple records in single calls
- **Real-time Views**: Instant data without refresh
- **Retry Logic**: Automatic retry with backoff
- **Performance Metrics**: Query timing and optimization status

## 🎯 Best Practices

1. **Always search for entity IDs first** before creating transactions
2. **Use optimized tools** for better performance (e.g., `getAccountBalanceOptimized`)
3. **Include product/service details** in sales lines for inventory tracking
4. **Validate entities exist** before referencing in transactions
5. **Use workflow tools** for complex operations with built-in intelligence
6. **Check for duplicates** when creating new records
7. **Provide meaningful descriptions** for all transaction lines

## 🔄 Integration with Mastra AI

These tools are designed to work seamlessly with the Mastra AI agent framework:
- **Natural Language Processing**: Convert plain English to structured transactions
- **Context Awareness**: Remember user preferences and business context
- **Error Recovery**: Intelligent error handling and user guidance
- **Educational Mode**: Explain accounting concepts to non-accountants
- **Memory Integration**: Learn from user patterns and preferences

## 📝 Tool Registration

All tools are exported from `index.ts` and automatically registered with the Mastra agent:

```typescript
export const tools = {
  // Master data tools
  getAccounts, createAccount, searchAccountsByName,
  // Transaction tools
  createSalesInvoice, recordCustomerPayment,
  // Workflow tools
  createNewAccountWorkflowTool,
  // Reporting tools
  getAccountBalanceOptimized, getTrialBalanceOptimized,
  // ... all other tools
};
```

This comprehensive tool suite enables the DeepLedger AI agent to handle complex accounting workflows while maintaining accuracy, performance, and user-friendly interactions.

## 🛠️ Tool Implementation Details

### Enhanced Sales Invoice Tool (`createSalesInvoice`)
**Key Features:**
- **Product/Service Integration**: Links to inventory for automatic tracking
- **Multi-line Tax Support**: Per-line tax rates and calculations
- **Discount Handling**: Both percentage and fixed amount discounts
- **Project/Class Allocation**: Detailed cost center tracking
- **Duplicate Detection**: Prevents duplicate invoices by reference number
- **Automatic Document Numbering**: Sequential invoice numbering
- **Retry Logic**: Handles database conflicts with exponential backoff

**Enhanced Line Schema:**
```typescript
{
  accountId: string,           // Required: Account for this line
  description: string,         // Required: Line description
  debitAmount: number,         // Either debit OR credit required
  creditAmount: number,        // Either debit OR credit required
  productServiceId?: string,   // Optional: Links to inventory
  quantity?: number,           // Optional: Quantity sold
  unitPrice?: number,          // Optional: Price per unit
  taxRateId?: string,          // Optional: Tax rate reference
  taxRate?: number,            // Optional: Tax percentage (0-100)
  discountPercentage?: number, // Optional: Discount percentage
  discountAmount?: number,     // Optional: Fixed discount amount
  classId?: string,            // Optional: Business segment
  projectId?: string           // Optional: Project tracking
}
```

### Smart Account Workflow (`createNewAccountWorkflowTool`)
**Workflow Steps:**
1. **Search Phase**: Comprehensive duplicate detection
   - Exact name matches
   - Exact code matches
   - Similar name matches (fuzzy matching)
2. **Analysis Phase**: Conflict resolution
   - Detailed duplicate analysis
   - Smart code generation if needed
   - Account type validation
3. **Creation Phase**: Safe account creation
   - Only creates if no conflicts
   - Provides detailed feedback
   - Returns workflow execution details

**Duplicate Detection Types:**
- `exact_name`: Account with identical name exists
- `exact_code`: Account with identical code exists
- `similar_name`: Account with similar name exists
- `none`: No duplicates found, safe to create

### Optimized Balance Tools
**Performance Enhancements:**
- **Database Functions**: Uses PostgreSQL functions for calculations
- **Real-time Views**: No caching delays, always current data
- **Batch Processing**: Multiple accounts in single query
- **Performance Metrics**: Query timing and optimization status

**Balance Interpretation:**
Each balance includes contextual interpretation:
- **Assets**: Positive = value owned, Negative = unusual credit balance
- **Liabilities**: Positive = amount owed, Negative = overpayment
- **Equity**: Positive = owner's equity, Negative = deficit
- **Revenue**: Positive = income earned, Negative = reversals
- **Expenses**: Positive = costs incurred, Negative = credits

## 🔍 Search and Validation Features

### Intelligent Entity Search
All search tools support:
- **Partial Name Matching**: Find entities by partial names
- **Case-Insensitive Search**: Flexible text matching
- **Active/Inactive Filtering**: Control visibility of disabled records
- **Pagination Support**: Handle large datasets efficiently

### Comprehensive Validation
Before transaction creation:
- **Entity Existence**: Verify all referenced entities exist
- **Organization Isolation**: Ensure entities belong to correct organization
- **Account Type Validation**: Verify appropriate account types for transactions
- **Balance Validation**: Ensure debits equal credits exactly
- **Date Validation**: Proper date format and business rules

## 📊 Reporting and Analytics

### Trial Balance Features
- **Optimized Performance**: Fast calculation using database functions
- **Period Comparison**: Compare different date ranges
- **Account Filtering**: Filter by account type or specific accounts
- **Summary Views**: Condensed reports for quick overview
- **Performance Metrics**: Query execution timing

### Outstanding Items Tracking
- **Aging Analysis**: Group by age buckets (30, 60, 90+ days)
- **Customer/Vendor Breakdown**: Detailed aging by entity
- **Payment Status**: Track partial payments and credits
- **Due Date Monitoring**: Identify overdue items

### Transaction Analysis
- **Flexible Filtering**: By date, account, entity, project, class
- **Pagination Support**: Handle large transaction volumes
- **Detailed Line Items**: Full transaction line details
- **Audit Trail**: Complete transaction history

## 🔧 Error Handling and Recovery

### Comprehensive Error Messages
- **Validation Errors**: Clear field-level validation messages
- **Business Rule Violations**: Accounting-specific error explanations
- **Entity Not Found**: Specific entity identification in errors
- **Duplicate Detection**: Detailed duplicate analysis

### Retry Mechanisms
- **Exponential Backoff**: Intelligent retry timing
- **Configurable Retries**: Adjustable retry counts
- **Transaction Safety**: Atomic operations with rollback
- **Performance Monitoring**: Track retry patterns

## 🎯 Advanced Features

### Multi-Currency Support (Future)
Tools are designed to support:
- Currency-specific rounding rules
- Exchange rate handling
- Multi-currency reporting

### Audit Trail Integration
All tools include:
- Created/Updated by tracking
- Timestamp recording
- Change history support
- Compliance reporting

### Performance Monitoring
Built-in metrics for:
- Query execution times
- Optimization status
- Retry patterns
- Error rates

## 🚀 Getting Started

### Basic Transaction Flow
1. **Search Entities**: Use search tools to find required IDs
2. **Validate Data**: Ensure all entities exist and are valid
3. **Create Transaction**: Use appropriate transaction tool
4. **Verify Results**: Check created transaction and balances

### Example: Complete Sales Invoice Workflow
```typescript
// Step 1: Find customer
const customers = await searchCustomersByName({
  nameContains: "ABC Corporation"
});
const customerId = customers[0].customer_id;

// Step 2: Find accounts
const revenueAccounts = await searchAccountsByName({
  nameContains: "Sales Revenue"
});
const arAccounts = await searchAccountsByName({
  nameContains: "Accounts Receivable"
});

// Step 3: Find product (if applicable)
const products = await searchProductsAndServices({
  nameContains: "Consulting Services"
});

// Step 4: Create invoice
const invoice = await createSalesInvoice({
  organizationId: "org-uuid",
  userId: "user-uuid",
  invoiceDate: "2024-01-15",
  customerId: customerId,
  description: "Consulting services for January",
  totalAmount: 1000.00,
  lines: [
    {
      accountId: revenueAccounts[0].account_id,
      description: "Consulting Revenue",
      creditAmount: 1000.00,
      productServiceId: products[0].product_service_id,
      quantity: 10,
      unitPrice: 100.00
    },
    {
      accountId: arAccounts[0].account_id,
      description: "Accounts Receivable - ABC Corp",
      debitAmount: 1000.00
    }
  ]
});
```

This documentation provides a comprehensive guide to the specialized accounting tools available in the DeepLedger AI system, enabling developers and users to understand and effectively utilize the full range of capabilities.
