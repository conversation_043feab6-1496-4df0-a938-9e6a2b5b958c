import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { getUserContext } from '../../utils/supabase.js';

// Enhanced tool that executes the create new account workflow
export const createNewAccountWorkflowTool = createTool({
  id: 'Create New Account Workflow',
  description: `Execute an intelligent workflow to create new accounting accounts with comprehensive duplicate detection.

  This workflow:
  1. Searches for existing accounts by name and code to prevent duplicates
  2. Provides detailed duplicate analysis (exact name, exact code, similar name matches)
  3. Generates smart account codes based on account type if not provided
  4. Creates new accounts only when no conflicts exist
  5. Provides clear feedback about existing accounts when duplicates are found

  Use this when the user wants to create a new account and you want to ensure data integrity through intelligent duplicate checking and code generation.`,
  inputSchema: z.object({
    accountName: z.string().describe('The name of the account to search for or create'),
    accountCode: z.string().optional().describe('Optional account code (4-digit format). If not provided, will be auto-generated based on account type'),
    accountType: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']).describe('The type of account to create'),
    accountTypeDetail: z.enum([
      // Asset details
      'accounts_receivable', 'other_current_assets', 'bank', 'fixed_assets', 'other_assets',
      // Liability details
      'accounts_payable', 'credit_card', 'other_current_liabilities', 'long_term_liabilities',
      // Equity details
      'equity',
      // Revenue details
      'revenue', 'other_revenue',
      // Expense details
      'cost_of_goods_sold', 'expenditures', 'other_expenditure'
    ]).describe('The specific classification within the account type'),
    description: z.string().optional().describe('Optional description for the account'),
  }),
  outputSchema: z.object({
    success: z.boolean(),
    action: z.enum(['created', 'exists', 'error']),
    message: z.string(),
    account: z.object({
      account_id: z.string().uuid(),
      account_name: z.string(),
      code: z.string(),
      account_type: z.string(),
      account_type_detail: z.string(),
      description: z.string().nullable(),
      is_active: z.boolean(),
    }).optional(),
    existingAccounts: z.array(z.object({
      account_id: z.string().uuid(),
      account_name: z.string(),
      code: z.string(),
      account_type: z.string(),
      account_type_detail: z.string(),
      description: z.string().nullable(),
      is_active: z.boolean(),
    })).optional(),
    workflowDetails: z.object({
      runId: z.string(),
      status: z.string(),
      searchSummary: z.string(),
      duplicateType: z.enum(['exact_name', 'exact_code', 'similar_name', 'none']).optional(),
      stepsExecuted: z.array(z.string()).optional(),
    }),
  }),
  execute: async ({ context, mastra, runtimeContext }) => {
    if (!mastra) {
      throw new Error('Mastra instance not available');
    }

    const { organizationId, userId } = getUserContext(runtimeContext);
    const { accountName, accountCode, accountType, accountTypeDetail, description } = context;

    try {
      // Get the workflow from Mastra
      const workflow = mastra.getWorkflow('createNewAccountWorkflow');
      if (!workflow) {
        throw new Error('Create new account workflow not found');
      }

      // Create a run and execute the workflow
      const run = workflow.createRun();
      const result = await run.start({
        inputData: {
          accountName,
          accountCode,
          accountType,
          accountTypeDetail,
          description,
          organizationId,
          userId,
        },
      });

      if (result.status === 'failed') {
        throw new Error(`Workflow failed: ${result.error.message}`);
      }

      if (result.status === 'suspended') {
        throw new Error('Workflow was suspended - this should not happen in this tool');
      }

      // Extract results from workflow steps
      const searchStep = result.steps.searchExistingAccounts;
      const createStep = result.steps.createOrInform;

      if (!searchStep || searchStep.status !== 'success') {
        throw new Error('Account search step failed');
      }

      if (!createStep || createStep.status !== 'success') {
        throw new Error('Account creation/information step failed');
      }

      const searchOutput = searchStep.output;
      const createOutput = createStep.output;

      return {
        success: createOutput.action !== 'error',
        action: createOutput.action,
        message: createOutput.message,
        account: createOutput.account,
        existingAccounts: createOutput.existingAccounts,
        workflowDetails: {
          runId: `workflow-${Date.now()}`, // Generate a unique run ID
          status: result.status,
          searchSummary: searchOutput.searchSummary,
          duplicateType: searchOutput.duplicateType,
          stepsExecuted: Object.keys(result.steps).filter(step => result.steps[step].status === 'success'),
        },
      };
    } catch (error) {
      return {
        success: false,
        action: 'error' as const,
        message: `Workflow execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        workflowDetails: {
          runId: 'failed',
          status: 'error',
          searchSummary: 'Workflow execution failed before search could complete',
        },
      };
    }
  },
});
