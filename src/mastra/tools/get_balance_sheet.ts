import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define balance sheet item schema
const balanceSheetItemSchema = z.object({
  account_id: z.string(),
  code: z.string(),
  name: z.string(),
  type: z.enum(['asset', 'liability', 'equity']),
  currentAmount: z.number(),
  previousAmount: z.number().nullable(),
  percentChange: z.number().nullable(),
});

// Define grouped balance sheet item schema
const groupedBalanceSheetItemSchema = z.object({
  category: z.string(),
  isCategory: z.boolean(),
  currentAmount: z.number(),
  previousAmount: z.number().nullable(),
  percentChange: z.number().nullable(),
  items: z.array(balanceSheetItemSchema),
});

// Tool to generate a balance sheet report
export const getBalanceSheet = createTool({
  id: 'Get Balance Sheet',
  description: 'Generate a comprehensive balance sheet report showing assets, liabilities, and equity as of a specific date. Use this when the user wants to see the financial position of the business, analyze asset and liability balances, or when they ask for a balance sheet. Supports period comparison and account grouping for detailed analysis.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the balance sheet'),
    asOfDate: z.string().describe('Generate balance sheet as of this date (YYYY-MM-DD)'),
    compareWithPreviousPeriod: z.boolean().default(false).describe('Include comparison with previous period'),
    previousDate: z.string().optional().describe('Previous date for comparison (YYYY-MM-DD). If not provided, defaults to 1 year before asOfDate'),
    includeZeroBalances: z.boolean().default(false).describe('Include accounts with zero balances'),
    groupByCategory: z.boolean().default(true).describe('Group accounts by category'),
  }),
  outputSchema: z.object({
    reportTitle: z.string(),
    asOfDate: z.string(),
    previousDate: z.string().nullable(),
    assets: z.array(z.union([balanceSheetItemSchema, groupedBalanceSheetItemSchema])),
    liabilities: z.array(z.union([balanceSheetItemSchema, groupedBalanceSheetItemSchema])),
    equity: z.array(z.union([balanceSheetItemSchema, groupedBalanceSheetItemSchema])),
    summary: z.object({
      totalAssets: z.number(),
      totalLiabilities: z.number(),
      totalEquity: z.number(),
      totalLiabilitiesAndEquity: z.number(),
      previousTotalAssets: z.number().nullable(),
      previousTotalLiabilities: z.number().nullable(),
      previousTotalEquity: z.number().nullable(),
      previousTotalLiabilitiesAndEquity: z.number().nullable(),
      assetsPercentChange: z.number().nullable(),
      liabilitiesPercentChange: z.number().nullable(),
      equityPercentChange: z.number().nullable(),
    }),
    isBalanced: z.boolean(),
    message: z.string().describe('Summary message about the balance sheet analysis and financial position'),
  }),
  execute: async ({ context }) => {
    const organizationId = context.organizationId;
    // Calculate previous date if comparison is requested but no specific date is provided
    let previousDate = null;
    if (context.compareWithPreviousPeriod) {
      if (context.previousDate) {
        previousDate = context.previousDate;
      } else {
        // Default to 1 year before asOfDate
        const asOfDateObj = new Date(context.asOfDate);
        asOfDateObj.setFullYear(asOfDateObj.getFullYear() - 1);
        previousDate = asOfDateObj.toISOString().split('T')[0];
      }
    }

    // Get all balance sheet accounts (assets, liabilities, equity)
    const { data: accounts, error: accountsError } = await supabase
      .from('accounts')
      .select('*')
      .in('type', ['asset', 'liability', 'equity'])
      .eq('is_active', true)
      .eq('organization_id', organizationId)
      .order('code');

    if (accountsError) {
      throw new Error(`Failed to get accounts: ${accountsError.message}`);
    }

    // Separate accounts by type
    const assetAccounts = accounts.filter(account => account.type === 'asset');
    const liabilityAccounts = accounts.filter(account => account.type === 'liability');
    const equityAccounts = accounts.filter(account => account.type === 'equity');

    // Process each account type
    const assetItems = await processAccounts(assetAccounts, context.asOfDate, previousDate, context.compareWithPreviousPeriod, context.includeZeroBalances, organizationId);
    const liabilityItems = await processAccounts(liabilityAccounts, context.asOfDate, previousDate, context.compareWithPreviousPeriod, context.includeZeroBalances, organizationId);
    const equityItems = await processAccounts(equityAccounts, context.asOfDate, previousDate, context.compareWithPreviousPeriod, context.includeZeroBalances, organizationId);

    // Calculate retained earnings (if not already included in equity accounts)
    // This requires calculating net income from all time up to the asOfDate
    const retainedEarnings = await calculateRetainedEarnings(context.asOfDate, previousDate, context.compareWithPreviousPeriod, organizationId);

    // Add retained earnings to equity items if it's not zero or includeZeroBalances is true
    if (retainedEarnings.currentAmount !== 0 || context.includeZeroBalances) {
      equityItems.push(retainedEarnings);
    }

    // Calculate totals
    const totalAssets = assetItems.reduce((sum, item) => sum + item.currentAmount, 0);
    const totalLiabilities = liabilityItems.reduce((sum, item) => sum + item.currentAmount, 0);
    const totalEquity = equityItems.reduce((sum, item) => sum + item.currentAmount, 0);

    // Calculate previous period totals if comparison is requested
    let previousTotalAssets = 0;
    let previousTotalLiabilities = 0;
    let previousTotalEquity = 0;

    if (context.compareWithPreviousPeriod) {
      previousTotalAssets = assetItems.reduce((sum, item) => sum + (item.previousAmount || 0), 0);
      previousTotalLiabilities = liabilityItems.reduce((sum, item) => sum + (item.previousAmount || 0), 0);
      previousTotalEquity = equityItems.reduce((sum, item) => sum + (item.previousAmount || 0), 0);
    }

    // Group accounts by category if requested
    let groupedAssetItems: any = assetItems;
    let groupedLiabilityItems: any = liabilityItems;
    let groupedEquityItems: any = equityItems;

    if (context.groupByCategory) {
      // Group asset items
      const assetCategories = {
        'Current Assets': assetItems.filter(item => item.code.startsWith('1') && parseInt(item.code) < 1500),
        'Fixed Assets': assetItems.filter(item => item.code.startsWith('15')),
        'Other Assets': assetItems.filter(item => !item.code.startsWith('1') || parseInt(item.code) >= 1900),
      };

      // Group liability items
      const liabilityCategories = {
        'Current Liabilities': liabilityItems.filter(item => item.code.startsWith('2') && parseInt(item.code) < 2500),
        'Long-term Liabilities': liabilityItems.filter(item => item.code.startsWith('25')),
        'Other Liabilities': liabilityItems.filter(item => !item.code.startsWith('2') || parseInt(item.code) >= 2900),
      };

      // Group equity items
      const equityCategories = {
        'Owner Equity': equityItems.filter(item => item.code.startsWith('3') && parseInt(item.code) < 3900),
        'Retained Earnings': equityItems.filter(item => item.name === 'Retained Earnings' || item.code === '3900'),
      };

      // Convert grouped items to a format suitable for the response
      groupedAssetItems = createGroupedItems(assetCategories, context.compareWithPreviousPeriod).filter(Boolean) as any;
      groupedLiabilityItems = createGroupedItems(liabilityCategories, context.compareWithPreviousPeriod).filter(Boolean) as any;
      groupedEquityItems = createGroupedItems(equityCategories, context.compareWithPreviousPeriod).filter(Boolean) as any;
    }

    // Check if the balance sheet balances (Assets = Liabilities + Equity)
    const isBalanced = Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01; // Allow for small rounding differences

    // Generate informative message about the balance sheet
    const balanceDifference = totalAssets - (totalLiabilities + totalEquity);

    let message = `Balance Sheet as of ${context.asOfDate}: Total Assets: $${totalAssets.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}, Total Liabilities: $${totalLiabilities.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}, Total Equity: $${totalEquity.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}. `;

    if (isBalanced) {
      message += 'The balance sheet is properly balanced (Assets = Liabilities + Equity).';
    } else {
      message += `WARNING: Balance sheet is NOT balanced. Difference: $${Math.abs(balanceDifference).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ${balanceDifference > 0 ? '(Assets exceed Liabilities + Equity)' : '(Liabilities + Equity exceed Assets)'}.`;
    }

    if (context.compareWithPreviousPeriod) {
      const assetChange = previousTotalAssets ? ((totalAssets - previousTotalAssets) / previousTotalAssets * 100) : 0;
      const equityChange = previousTotalEquity ? ((totalEquity - previousTotalEquity) / previousTotalEquity * 100) : 0;
      message += ` Compared to ${previousDate}: Assets changed by ${assetChange.toFixed(1)}%, Equity changed by ${equityChange.toFixed(1)}%.`;
    }

    // Prepare the response
    const response = {
      reportTitle: 'Balance Sheet',
      asOfDate: context.asOfDate,
      previousDate: context.compareWithPreviousPeriod ? previousDate : null,
      assets: context.groupByCategory ? groupedAssetItems : assetItems,
      liabilities: context.groupByCategory ? groupedLiabilityItems : liabilityItems,
      equity: context.groupByCategory ? groupedEquityItems : equityItems,
      summary: {
        totalAssets,
        totalLiabilities,
        totalEquity,
        totalLiabilitiesAndEquity: totalLiabilities + totalEquity,
        previousTotalAssets: context.compareWithPreviousPeriod ? previousTotalAssets : null,
        previousTotalLiabilities: context.compareWithPreviousPeriod ? previousTotalLiabilities : null,
        previousTotalEquity: context.compareWithPreviousPeriod ? previousTotalEquity : null,
        previousTotalLiabilitiesAndEquity: context.compareWithPreviousPeriod ? previousTotalLiabilities + previousTotalEquity : null,
        assetsPercentChange: context.compareWithPreviousPeriod && previousTotalAssets
          ? ((totalAssets - previousTotalAssets) / previousTotalAssets) * 100
          : null,
        liabilitiesPercentChange: context.compareWithPreviousPeriod && previousTotalLiabilities
          ? ((totalLiabilities - previousTotalLiabilities) / previousTotalLiabilities) * 100
          : null,
        equityPercentChange: context.compareWithPreviousPeriod && previousTotalEquity
          ? ((totalEquity - previousTotalEquity) / previousTotalEquity) * 100
          : null,
      },
      isBalanced,
      message
    };

    return response;
  },
});

// Helper function to process accounts and calculate balances
async function processAccounts(
  accounts: any[],
  asOfDate: string,
  previousDate: string | null,
  compareWithPreviousPeriod: boolean,
  includeZeroBalances: boolean,
  organizationId: string
) {
  const results = [];

  for (const account of accounts) {
    // Get current period transaction lines for this account
    const { data: currentLines, error: currentLinesError } = await supabase
      .from('transaction_lines')
      .select(`
        *,
        transactions:transaction_id (transaction_date, organization_id)
      `)
      .eq('account_id', account.account_id)
      .eq('transactions.organization_id', organizationId)
      .lte('transactions.transaction_date', asOfDate);

    if (currentLinesError) {
      throw new Error(`Failed to get transaction lines for account ${account.code}: ${currentLinesError.message}`);
    }

    // Calculate current balance
    let currentAmount = 0;
    for (const line of currentLines) {
      if (account.type === 'asset') {
        currentAmount += line.debit_amount - line.credit_amount;
      } else if (account.type === 'liability' || account.type === 'equity') {
        currentAmount += line.credit_amount - line.debit_amount;
      }
    }

    // Skip accounts with zero balance unless includeZeroBalances is true
    if (currentAmount === 0 && !includeZeroBalances && !compareWithPreviousPeriod) {
      continue;
    }

    // Get previous period transaction lines if comparison is requested
    let previousAmount = null;
    if (compareWithPreviousPeriod) {
      const { data: previousLines, error: previousLinesError } = await supabase
        .from('transaction_lines')
        .select(`
          *,
          transactions:transaction_id (transaction_date, organization_id)
        `)
        .eq('account_id', account.account_id)
        .eq('transactions.organization_id', organizationId)
        .lte('transactions.transaction_date', previousDate);

      if (previousLinesError) {
        throw new Error(`Failed to get previous period transaction lines for account ${account.code}: ${previousLinesError.message}`);
      }

      // Calculate previous balance
      previousAmount = 0;
      for (const line of previousLines) {
        if (account.type === 'asset') {
          previousAmount += line.debit_amount - line.credit_amount;
        } else if (account.type === 'liability' || account.type === 'equity') {
          previousAmount += line.credit_amount - line.debit_amount;
        }
      }

      // Skip accounts with zero balance in both periods unless includeZeroBalances is true
      if (currentAmount === 0 && previousAmount === 0 && !includeZeroBalances) {
        continue;
      }
    }

    // Calculate percent change if previous amount exists and is not zero
    const percentChange = previousAmount && previousAmount !== 0
      ? ((currentAmount - previousAmount) / previousAmount) * 100
      : null;

    results.push({
      account_id: account.account_id,
      code: account.code,
      name: account.account_name,
      type: account.type,
      currentAmount,
      previousAmount,
      percentChange
    });
  }

  return results;
}

// Helper function to calculate retained earnings
async function calculateRetainedEarnings(
  asOfDate: string,
  previousDate: string | null,
  compareWithPreviousPeriod: boolean,
  organizationId: string
) {
  // Get all revenue and expense accounts
  const { data: incomeAccounts, error: accountsError } = await supabase
    .from('accounts')
    .select('*')
    .in('type', ['revenue', 'expense'])
    .eq('is_active', true)
    .eq('organization_id', organizationId);

  if (accountsError) {
    throw new Error(`Failed to get income accounts: ${accountsError.message}`);
  }

  // Calculate current retained earnings
  let currentRetainedEarnings = 0;

  for (const account of incomeAccounts) {
    // Get all transaction lines for this account up to asOfDate
    const { data: lines, error: linesError } = await supabase
      .from('transaction_lines')
      .select(`
        *,
        transactions:transaction_id (transaction_date, organization_id)
      `)
      .eq('account_id', account.account_id)
      .eq('transactions.organization_id', organizationId)
      .lte('transactions.transaction_date', asOfDate);

    if (linesError) {
      throw new Error(`Failed to get transaction lines for account ${account.code}: ${linesError.message}`);
    }

    // Calculate the contribution to retained earnings
    for (const line of lines) {
      if (account.type === 'revenue') {
        currentRetainedEarnings += line.credit_amount - line.debit_amount;
      } else if (account.type === 'expense') {
        currentRetainedEarnings -= line.debit_amount - line.credit_amount;
      }
    }
  }

  // Calculate previous retained earnings if comparison is requested
  let previousRetainedEarnings = null;
  if (compareWithPreviousPeriod && previousDate) {
    previousRetainedEarnings = 0;

    for (const account of incomeAccounts) {
      // Get all transaction lines for this account up to previousDate
      const { data: lines, error: linesError } = await supabase
        .from('transaction_lines')
        .select(`
          *,
          transactions:transaction_id (transaction_date, organization_id)
        `)
        .eq('account_id', account.account_id)
        .eq('transactions.organization_id', organizationId)
        .lte('transactions.transaction_date', previousDate);

      if (linesError) {
        throw new Error(`Failed to get previous transaction lines for account ${account.code}: ${linesError.message}`);
      }

      // Calculate the contribution to previous retained earnings
      for (const line of lines) {
        if (account.type === 'revenue') {
          previousRetainedEarnings += line.credit_amount - line.debit_amount;
        } else if (account.type === 'expense') {
          previousRetainedEarnings -= line.debit_amount - line.credit_amount;
        }
      }
    }
  }

  // Calculate percent change if previous amount exists and is not zero
  const percentChange = previousRetainedEarnings && previousRetainedEarnings !== 0
    ? ((currentRetainedEarnings - previousRetainedEarnings) / previousRetainedEarnings) * 100
    : null;

  return {
    account_id: 'retained-earnings',
    code: '3900',
    name: 'Retained Earnings',
    type: 'equity',
    currentAmount: currentRetainedEarnings,
    previousAmount: previousRetainedEarnings,
    percentChange
  };
}

// Helper function to create grouped items
function createGroupedItems(categories: Record<string, any[]>, compareWithPreviousPeriod: boolean) {
  return Object.entries(categories).map(([category, items]) => {
    if (items.length === 0) return null;

    const totalCurrentAmount = items.reduce((sum, item) => sum + item.currentAmount, 0);
    const totalPreviousAmount = compareWithPreviousPeriod
      ? items.reduce((sum, item) => sum + (item.previousAmount || 0), 0)
      : null;

    return {
      category,
      isCategory: true,
      currentAmount: totalCurrentAmount,
      previousAmount: totalPreviousAmount,
      percentChange: totalPreviousAmount ? ((totalCurrentAmount - totalPreviousAmount) / totalPreviousAmount) * 100 : null,
      items
    };
  }).filter(Boolean);
}
