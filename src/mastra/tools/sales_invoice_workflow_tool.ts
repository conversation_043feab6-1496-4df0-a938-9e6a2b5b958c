import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { getUserContext } from '../../utils/supabase.js';

// Helper function to validate date format (YYYY-MM-DD)
const isValidDateFormat = (dateString: string): boolean => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;

  const date = new Date(dateString);
  const timestamp = date.getTime();

  if (isNaN(timestamp)) return false;

  return date.toISOString().slice(0, 10) === dateString;
};

// Custom Zod transform for currency amounts - rounds to 2 decimal places
const currencyAmount = z.number()
  .transform(val => Math.round(val * 100) / 100)
  .refine(val => val >= 0, { message: 'Amount must be non-negative' });

// Define transaction status enum for validation
const invoiceStatusEnum = z.enum([
  'for_review',
  'approved'
]).optional();

// Define payment terms enum for validation
const paymentTermsEnum = z.enum([
  'due_on_receipt',
  'net_15',
  'net_30',
  'net_45',
  'net_60',
  'custom'
]).optional();

// Enhanced accounting line schema for workflow input
const workflowAccountingLineSchema = z.object({
  // Required fields
  accountName: z.string().describe('Account name to search for (e.g., "Sales Revenue", "Accounts Receivable")'),
  description: z.string().describe('Line item description'),
  debitAmount: currencyAmount.default(0).describe('Debit amount (automatically rounded to 2 decimal places)'),
  creditAmount: currencyAmount.default(0).describe('Credit amount (automatically rounded to 2 decimal places)'),
  
  // Optional classification
  className: z.string().optional().describe('Class name for tracking business segments'),
  projectName: z.string().optional().describe('Project name for tracking project-based work'),

  // Optional product/service fields for revenue/expense lines
  productServiceName: z.string().optional().describe('Product/Service name (for revenue/expense lines)'),
  quantity: z.number().positive().optional().describe('Quantity (if product/service line)'),
  unitPrice: currencyAmount.optional().describe('Unit price (automatically rounded to 2 decimal places)'),
  discountPercentage: z.number().min(0).max(100).default(0).describe('Discount percentage'),
  discountAmount: currencyAmount.default(0).describe('Discount amount (automatically rounded to 2 decimal places)'),
  taxRateName: z.string().optional().describe('Tax rate name'),
  taxRate: z.number().min(0).max(100).default(0).describe('Tax rate percentage (0-100)'),
});

// Enhanced tool that executes the sales invoice workflow
export const salesInvoiceWorkflowTool = createTool({
  id: 'Sales Invoice Workflow',
  description: `PRIMARY TOOL for creating sales invoices. Always use this first instead of searching for master data individually.

  This workflow automatically:
  1. Searches for all required master data (customer, accounts, products, tax rates) in parallel
  2. Validates data completeness and resolves names to IDs
  3. Identifies missing data and provides clear feedback
  4. Records the sales invoice transaction when all data is available

  EXECUTION APPROACH:
  - Use immediately when user requests sales invoice
  - Include all available information in single call
  - If missing data is returned, ask user how to handle it
  - Retry with createMissingData=true if user approves auto-creation
  - Never use individual search tools before this workflow

  Benefits: Single-step execution, comprehensive validation, clear missing data feedback, user choice for data creation.`,
  inputSchema: z.object({
    // Invoice details
    invoiceDate: z.string()
      .refine(val => isValidDateFormat(val), {
        message: 'Invoice date must be in YYYY-MM-DD format',
      })
      .describe('Date of the invoice (YYYY-MM-DD)'),
    referenceNumber: z.string().optional().describe('Reference or invoice number'),
    description: z.string()
      .min(3, { message: 'Description must be at least 3 characters' })
      .describe('Description of the invoice'),
    
    // Customer information
    customerName: z.string().describe('Customer name to search for'),
    
    // Invoice terms
    dueDate: z.string().optional()
      .refine(val => val === undefined || isValidDateFormat(val), {
        message: 'Due date must be in YYYY-MM-DD format',
      })
      .describe('Due date for the invoice (YYYY-MM-DD). If not provided, defaults to 30 days from invoice date.'),
    status: invoiceStatusEnum.default('approved').describe('Invoice status (for_review or approved)'),
    paymentTerms: paymentTermsEnum.describe('Payment terms for the invoice'),
    
    // Financial totals
    subtotalAmount: currencyAmount.optional()
      .describe('Subtotal amount before tax (automatically rounded to 2 decimal places)'),
    taxAmount: currencyAmount.optional()
      .describe('Tax amount (automatically rounded to 2 decimal places)'),
    totalAmount: currencyAmount
      .describe('Total invoice amount (automatically rounded to 2 decimal places)'),
    
    // Tax account information
    taxAccountName: z.string().optional()
      .describe('Tax account name (required if there are taxes on the invoice)'),

    // Accounting lines with names instead of IDs
    lines: z.array(workflowAccountingLineSchema)
      .min(2, { message: 'At least 2 line items are required for double-entry accounting' })
      .describe('Accounting line items with names that will be resolved to IDs'),
      
    // Workflow options
    skipDuplicateCheck: z.boolean().optional().default(false).describe('Skip checking for duplicate invoices'),
    createMissingData: z.boolean().optional().default(false).describe('Automatically create missing master data. Set to true only when user explicitly approves auto-creation.'),
    maxRetries: z.number().optional().default(1).describe('Maximum number of retries for database operations'),
  }),
  outputSchema: z.object({
    success: z.boolean().describe('Whether the workflow completed successfully'),
    action: z.enum(['completed', 'suspended', 'failed']),
    message: z.string().describe('Summary message about the workflow result'),
    
    // Invoice data if completed
    invoice: z.any().optional().describe('Created invoice data'),
    invoiceLines: z.array(z.any()).optional().describe('Created invoice lines'),
    
    // Missing data information if suspended
    missingData: z.array(z.any()).optional().describe('Missing master data items'),
    createdItems: z.array(z.any()).optional().describe('Items that were automatically created'),
    
    // Workflow metadata
    workflowDetails: z.object({
      runId: z.string().describe('Unique workflow run ID'),
      status: z.string().describe('Final workflow status'),
      stepsExecuted: z.array(z.string()).describe('List of steps that were executed'),
      totalProcessingTimeMs: z.number().describe('Total time taken for the workflow'),
    }),
  }),
  execute: async ({ context, mastra, runtimeContext }) => {
    if (!mastra) {
      throw new Error('Mastra instance not available');
    }

    const { organizationId, userId } = getUserContext(runtimeContext);
    const startTime = Date.now();

    try {
      // Get the workflow from Mastra
      const workflow = mastra.getWorkflow('salesInvoiceWorkflow');
      if (!workflow) {
        throw new Error('Sales invoice workflow not found');
      }

      // Create a run and execute the workflow
      const run = workflow.createRun();
      const result = await run.start({
        inputData: {
          organizationId,
          userId,
          ...context,
        },
      });

      const totalProcessingTime = Date.now() - startTime;

      if (result.status === 'failed') {
        return {
          success: false,
          action: 'failed',
          message: `Workflow failed: ${result.error?.message || 'Unknown error'}`,
          workflowDetails: {
            runId: result.runId,
            status: result.status,
            stepsExecuted: Object.keys(result.steps || {}).filter(step => 
              result.steps?.[step]?.status === 'success'
            ),
            totalProcessingTimeMs: totalProcessingTime,
          },
        };
      }

      if (result.status === 'suspended') {
        const handleMissingDataResult = result.steps?.handleMissingData;
        const validationResult = result.steps?.validateData;

        return {
          success: false,
          action: 'suspended',
          message: 'Workflow suspended due to missing master data',
          missingData: validationResult?.output?.missingData || [],
          workflowDetails: {
            runId: result.runId,
            status: result.status,
            stepsExecuted: Object.keys(result.steps || {}).filter(step => 
              result.steps?.[step]?.status === 'success'
            ),
            totalProcessingTimeMs: totalProcessingTime,
          },
        };
      }

      // Workflow completed successfully
      const recordInvoiceResult = result.steps?.recordInvoice;
      const handleMissingDataResult = result.steps?.handleMissingData;

      return {
        success: true,
        action: 'completed',
        message: recordInvoiceResult?.output?.message || 'Sales invoice created successfully',
        invoice: recordInvoiceResult?.output?.invoice,
        invoiceLines: recordInvoiceResult?.output?.invoiceLines,
        createdItems: handleMissingDataResult?.output?.createdItems || [],
        workflowDetails: {
          runId: result.runId,
          status: result.status,
          stepsExecuted: Object.keys(result.steps || {}).filter(step => 
            result.steps?.[step]?.status === 'success'
          ),
          totalProcessingTimeMs: totalProcessingTime,
        },
      };

    } catch (error) {
      return {
        success: false,
        action: 'failed',
        message: `Workflow execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        workflowDetails: {
          runId: `error-${Date.now()}`,
          status: 'failed',
          stepsExecuted: [],
          totalProcessingTimeMs: Date.now() - startTime,
        },
      };
    }
  },
});
