import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';



// Define payment method enum
const paymentMethodEnum = z.enum([
  'cash',
  'check',
  'credit_card',
  'debit_card',
  'bank_transfer',
  'ach',
  'wire_transfer',
  'paypal',
  'stripe',
  'other'
]);

// Define payment status enum for validation
const paymentStatusEnum = z.enum([
  'for_review',
  'approved'
]).optional();

// Helper function to validate date format (YYYY-MM-DD)
const isValidDateFormat = (dateString: string): boolean => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;

  const date = new Date(dateString);
  const timestamp = date.getTime();

  if (isNaN(timestamp)) return false;

  return date.toISOString().slice(0, 10) === dateString;
};

// Custom Zod transform for currency amounts - rounds to 2 decimal places
const currencyAmount = z.number()
  .transform(val => Math.round(val * 100) / 100)
  .refine(val => val >= 0, { message: 'Amount must be non-negative' });

// Helper function to check if an entity exists in the database
const entityExists = async (
  table: string,
  idField: string,
  id: string,
  organizationId: string
): Promise<boolean> => {
  const { data, error } = await supabase
    .from(table)
    .select(idField)
    .eq(idField, id)
    .eq('organization_id', organizationId)
    .single();

  if (error || !data) {
    return false;
  }

  return true;
};

// Helper function to check for duplicate transactions
const isDuplicateTransaction = async (
  referenceNumber: string,
  transactionDate: string,
  organizationId: string,
  customerId?: string
): Promise<boolean> => {
  let query = supabase
    .from('transactions')
    .select('transaction_id')
    .eq('reference_number', referenceNumber)
    .eq('transaction_date', transactionDate)
    .eq('organization_id', organizationId)
    .eq('transaction_type', 'customer_payment');

  if (customerId) {
    query = query.eq('customer_id', customerId);
  }

  const { data, error } = await query;

  if (error) {
    return false;
  }

  return data && data.length > 0;
};

// Define enhanced payment application schema for linking payments to invoices
const paymentApplicationSchema = z.object({
  transactionId: z.string().uuid().describe('Invoice transaction ID being paid'),
  amount: currencyAmount.describe('Amount being applied to this invoice (automatically rounded to 2 decimal places)'),
  paymentMethod: z.string().optional().describe('Payment method for this specific application (cash, check, credit_card, bank_transfer, etc.). If not provided, uses the global payment method.'),
  reference: z.string().optional().describe('Payment reference number or check number for this specific application. If not provided, uses the global reference number.'),
});

// Enhanced accounting line schema
const enhancedAccountingLineSchema = z.object({
  accountId: z.string().uuid().describe('Account ID for this line'),
  description: z.string().describe('Line item description'),
  debitAmount: currencyAmount.default(0).describe('Debit amount (automatically rounded to 2 decimal places)'),
  creditAmount: currencyAmount.default(0).describe('Credit amount (automatically rounded to 2 decimal places)'),
  classId: z.string().uuid().optional().describe('Class ID for tracking business segments'),
  projectId: z.string().uuid().optional().describe('Project ID for tracking project-based work'),
});

// Define customer payment schema for output
const customerPaymentSchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.string(),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  document_number: z.string().nullable(),
  description: z.string(),
  customer_id: z.string().uuid(),
  status: z.string(),
  payment_method: z.string(),
  total_amount: z.number(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Tool to record customer payment
export const recordCustomerPayment = createTool({
  id: 'Record Customer Payment',
  description: 'Record a payment received from a customer against one or more invoices. Use this when a customer pays their outstanding invoices. The payment will be applied to accounts receivable and the specified bank/cash account. Supports enhanced payment applications with per-application payment methods and references for sophisticated payment tracking. Follows manual accounting lines architecture with strict double-entry validation and automatic floating-point precision handling.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the payment'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    paymentDate: z.string()
      .refine(val => isValidDateFormat(val), {
        message: 'Payment date must be in YYYY-MM-DD format',
      })
      .describe('Date the payment was received (YYYY-MM-DD)'),
    referenceNumber: z.string().optional().describe('Check number, transaction ID, or other reference'),
    description: z.string()
      .min(3, { message: 'Description must be at least 3 characters' })
      .describe('Payment description'),
    customerId: z.string().uuid()
      .describe('Customer ID who made the payment'),
    status: paymentStatusEnum.default('approved').describe('Payment status (for_review or approved)'),
    paymentMethod: paymentMethodEnum.describe('Method of payment'),
    totalAmount: currencyAmount
      .describe('Total payment amount (automatically rounded to 2 decimal places)'),

    lines: z.array(enhancedAccountingLineSchema)
    .min(2, { message: 'At least 2 line items are required for double-entry accounting' })
    .describe('Accounting line items (must have at least 2 for double-entry). First line should be the bank/cash account (debit), second line should be accounts receivable (credit).'),

    paymentApplications: z.array(paymentApplicationSchema)
      .min(1, { message: 'At least one invoice must be specified for payment' })
      .describe('Invoices being paid with amounts and optional per-application payment methods and references. Supports different payment methods for different invoices in a single payment.'),

    skipDuplicateCheck: z.boolean().optional().default(false).describe('Skip checking for duplicate payments'),
    maxRetries: z.number().optional().default(1).describe('Maximum number of retries for database operations'),
  }),
  outputSchema: z.object({
    payment: customerPaymentSchema,
    paymentLines: z.array(z.object({
      transaction_line_id: z.string().uuid(),
      account_id: z.string().uuid(),
      description: z.string(),
      debit_amount: z.number(),
      credit_amount: z.number(),
      class_id: z.string().uuid().nullable(),
      project_id: z.string().uuid().nullable(),
    })),
    paymentApplications: z.array(z.object({
      invoice_payment_id: z.string().uuid(),
      transaction_id: z.string().uuid(),
      payment_transaction_id: z.string().uuid(),
      amount: z.number(),
      payment_date: z.string(),
      payment_method: z.string(),
      reference: z.string().nullable(),
      application_payment_method: z.string().nullable().describe('Payment method specific to this application'),
      application_reference: z.string().nullable().describe('Reference specific to this application'),
    })),
    message: z.string().describe('Success message with payment details'),
    processingTimeMs: z.number().describe('Time taken to process the payment'),
  }),
  execute: async ({ context }) => {
    // Extract organization ID from context
    const organizationId = context.organizationId;
    // Start timing the payment processing
    const startTime = Date.now();
    let maxRetryCount = 0;
    let customerName = '';

    try {
      // Validate that lines are provided
      if (!context.lines || context.lines.length < 2) {
        throw new Error('At least 2 accounting line items are required for double-entry accounting.');
      }

      // Validate that each line has either debit OR credit (not both)
      for (const line of context.lines) {
        if (line.debitAmount > 0 && line.creditAmount > 0) {
          throw new Error('Each line must have either debit OR credit amount, not both');
        }
        if (line.debitAmount === 0 && line.creditAmount === 0) {
          throw new Error('Each line must have either a debit or credit amount');
        }
      }

      // Validate debits = credits (ZERO TOLERANCE with floating-point precision handling)
      const totalDebits = Math.round(context.lines.reduce((sum, line) => sum + line.debitAmount, 0) * 100) / 100;
      const totalCredits = Math.round(context.lines.reduce((sum, line) => sum + line.creditAmount, 0) * 100) / 100;
      if (totalDebits !== totalCredits) {
        throw new Error(`Debits (${totalDebits}) must exactly equal credits (${totalCredits}). No tolerance allowed.`);
      }

      // Validate total amount matches accounting lines
      if (Math.round(context.totalAmount * 100) / 100 !== totalDebits) {
        throw new Error(`Total amount (${context.totalAmount}) must equal total debits/credits (${totalDebits})`);
      }

      // Validate all accounts exist
      for (const line of context.lines) {
        const accountExists = await entityExists('accounts', 'account_id', line.accountId, organizationId);
        if (!accountExists) {
          throw new Error(`Account with ID ${line.accountId} not found`);
        }

        // Validate optional classification fields
        if (line.classId) {
          const classExists = await entityExists('classes', 'class_id', line.classId, organizationId);
          if (!classExists) {
            throw new Error(`Class with ID ${line.classId} not found`);
          }
        }

        if (line.projectId) {
          const projectExists = await entityExists('projects', 'project_id', line.projectId, organizationId);
          if (!projectExists) {
            throw new Error(`Project with ID ${line.projectId} not found`);
          }
        }
      }

      // Validate customer exists
      const { data: customer, error: customerError } = await supabase
        .from('customers')
        .select('customer_name')
        .eq('customer_id', context.customerId)
        .eq('organization_id', organizationId)
        .single();

      if (customerError || !customer) {
        throw new Error(`Customer with ID ${context.customerId} not found`);
      }

      customerName = customer.customer_name;

      // Validate payment applications total matches payment amount
      const totalApplicationAmount = Math.round(context.paymentApplications.reduce((sum, app) => sum + app.amount, 0) * 100) / 100;
      if (totalApplicationAmount !== Math.round(context.totalAmount * 100) / 100) {
        throw new Error(`Payment applications total (${totalApplicationAmount}) must equal payment amount (${context.totalAmount})`);
      }

      // Validate each invoice being paid
      for (const application of context.paymentApplications) {
        const { data: invoice, error: invoiceError } = await supabase
          .from('transactions')
          .select('*')
          .eq('transaction_id', application.transactionId)
          .eq('customer_id', context.customerId)
          .eq('organization_id', organizationId)
          .in('transaction_type', ['sales_invoice', 'sales_receipt'])
          .single();

        if (invoiceError || !invoice) {
          throw new Error(`Invoice with ID ${application.transactionId} not found for this customer`);
        }
      }

      // Check for duplicate transactions (if reference number provided)
      if (!context.skipDuplicateCheck && context.referenceNumber) {
        const isDuplicate = await isDuplicateTransaction(
          context.referenceNumber,
          context.paymentDate,
          organizationId,
          context.customerId
        );

        if (isDuplicate) {
          throw new Error(`Duplicate customer payment detected with reference number ${context.referenceNumber}`);
        }
      }

      // Generate document number using the database function
      const { data: documentNumberResult, error: documentNumberError } = await supabase
        .rpc('generate_document_number', {
          p_transaction_type: 'customer_payment',
          p_organization_id: organizationId
        });

      if (documentNumberError) {
        throw new Error(`Failed to generate payment number: ${documentNumberError.message}`);
      }

      const documentNumber = documentNumberResult;

      // Start a Supabase transaction with retry logic
      let payment: any;
      let paymentError;
      let retryCount = 0;
      const maxRetries = context.maxRetries || 1;

      while (retryCount < maxRetries) {
        const paymentData = {
          transaction_type: 'customer_payment',
          transaction_date: context.paymentDate,
          reference_number: context.referenceNumber,
          document_number: documentNumber,
          description: context.description,
          customer_id: context.customerId,
          status: context.status || 'approved',
          payment_method: context.paymentMethod,
          total_amount: context.totalAmount,
          organization_id: context.organizationId,
          created_by: context.userId,
          updated_by: context.userId,
        };

        const result = await supabase
          .from('transactions')
          .insert(paymentData)
          .select()
          .single();

        payment = result.data;
        paymentError = result.error;

        if (!paymentError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (paymentError) {
        throw new Error(`Failed to create customer payment after ${retryCount} attempts: ${paymentError.message}`);
      }

      // Create transaction lines
      const transactionLines = context.lines.map((line) => {
        return {
          transaction_id: payment.transaction_id,
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
          organization_id: context.organizationId,
          created_by: context.userId,
          updated_by: context.userId,
        };
      });

      // Insert transaction lines with retry logic
      retryCount = 0;
      let linesError;

      while (retryCount < maxRetries) {
        const result = await supabase
          .from('transaction_lines')
          .insert(transactionLines);

        linesError = result.error;

        if (!linesError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (linesError) {
        throw new Error(`Failed to create payment lines after ${retryCount} attempts: ${linesError.message}`);
      }

      // Create enhanced payment applications (link payment to invoices)
      const invoicePayments = context.paymentApplications.map((application) => {
        return {
          transaction_id: application.transactionId,
          payment_transaction_id: payment.transaction_id,
          amount: application.amount,
          payment_date: context.paymentDate,
          payment_method: application.paymentMethod || context.paymentMethod, // Use application-specific or global payment method
          reference: application.reference || context.referenceNumber || null, // Use application-specific or global reference
          organization_id: context.organizationId,
          created_by: context.userId,
          updated_by: context.userId,
        };
      });

      // Insert payment applications with retry logic
      retryCount = 0;
      let applicationsError;

      while (retryCount < maxRetries) {
        const result = await supabase
          .from('invoice_payments')
          .insert(invoicePayments);

        applicationsError = result.error;

        if (!applicationsError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        // Update max retry count for logging
        maxRetryCount = Math.max(maxRetryCount, retryCount);

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (applicationsError) {
        throw new Error(`Failed to create payment applications after ${retryCount} attempts: ${applicationsError.message}`);
      }

      // Update max retry count
      maxRetryCount = Math.max(maxRetryCount, context.maxRetries || 1);



      return {
        payment,
        paymentLines: context.lines.map((line: any, index: number) => ({
          transaction_line_id: `line_${index + 1}`, // This would be the actual ID from the database
          account_id: line.accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: line.classId || null,
          project_id: line.projectId || null,
        })),
        paymentApplications: context.paymentApplications.map((app: any, index: number) => ({
          invoice_payment_id: `payment_${index + 1}`, // This would be the actual ID from the database
          transaction_id: app.transactionId,
          payment_transaction_id: payment.transaction_id,
          amount: app.amount,
          payment_date: context.paymentDate,
          payment_method: app.paymentMethod || context.paymentMethod, // Use application-specific or global payment method
          reference: app.reference || context.referenceNumber || null, // Use application-specific or global reference
          application_payment_method: app.paymentMethod || null, // Track application-specific payment method
          application_reference: app.reference || null, // Track application-specific reference
        })),
        message: `Successfully recorded customer payment ${documentNumber} from ${customerName} for $${context.totalAmount} applied to ${context.paymentApplications.length} invoice(s).`,
        processingTimeMs: Date.now() - startTime
      };

    } catch (error: any) {
      throw error;
    }
  },
});


