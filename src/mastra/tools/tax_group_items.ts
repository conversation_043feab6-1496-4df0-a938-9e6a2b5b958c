import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define comprehensive schemas for tax group items
const taxGroupSchema = z.object({
  tax_group_id: z.string().uuid(),
  tax_group_name: z.string(),
});

const taxRateSchema = z.object({
  tax_rate_id: z.string().uuid(),
  tax_rate_name: z.string(),
  tax_rate_percentage: z.number(),
  tax_rate_type: z.string(),
});

const taxGroupItemSchema = z.object({
  tax_group_item_id: z.string().uuid(),
  tax_group_id: z.string().uuid(),
  tax_rate_id: z.string().uuid(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
  created_by: z.string().uuid().nullable(),
  updated_by: z.string().uuid().nullable(),
  tax_groups: taxGroupSchema.optional(),
  tax_rates: taxRateSchema.optional(),
});

// Define TaxGroupItem interface for type safety
interface TaxGroupItem {
  tax_group_item_id?: string;
  tax_group_id?: string;
  tax_rate_id?: string;
  organization_id?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string | null;
  updated_by?: string | null;
}

// Tool to get tax group items
export const getTaxGroupItems = createTool({
  id: 'Get Tax Group Items',
  description: 'Retrieve tax group items (associations between tax groups and tax rates) for the current organization. Use this when you need to see which tax rates are assigned to specific tax groups, manage tax group compositions, or when the user asks about tax group relationships. Essential for tax management workflows and understanding how taxes are grouped together. This tool helps you understand the structure of tax groups and which rates apply together.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter tax group items'),
    taxGroupId: z.string().uuid().optional().describe('Filter by tax group ID to see only items for a specific tax group'),
    taxRateId: z.string().uuid().optional().describe('Filter by tax rate ID to see which tax groups contain a specific tax rate'),
  }),
  outputSchema: z.object({
    success: z.boolean().describe('Whether the operation was successful'),
    taxGroupItems: z.array(taxGroupItemSchema),
    totalItems: z.number().describe('Total number of tax group items found'),
    message: z.string().describe('Information about the tax group items and their relationships'),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('tax_group_items')
      .select(`
        *,
        tax_groups:tax_group_id (tax_group_id, tax_group_name),
        tax_rates:tax_rate_id (tax_rate_id, tax_rate_name, tax_rate_percentage, tax_rate_type)
      `)
      .eq('organization_id', context.organizationId);

    if (context.taxGroupId) {
      query = query.eq('tax_group_id', context.taxGroupId);
    }

    if (context.taxRateId) {
      query = query.eq('tax_rate_id', context.taxRateId);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to get tax group items: ${error.message}`);
    }

    const count = data?.length || 0;
    let message = '';

    if (context.taxGroupId && context.taxRateId) {
      message = count > 0
        ? `Found association between the specified tax group and tax rate`
        : `No association found between the specified tax group and tax rate`;
    } else if (context.taxGroupId) {
      message = count > 0
        ? `Found ${count} tax rate(s) assigned to this tax group`
        : `No tax rates are currently assigned to this tax group`;
    } else if (context.taxRateId) {
      message = count > 0
        ? `This tax rate is assigned to ${count} tax group(s)`
        : `This tax rate is not assigned to any tax groups`;
    } else {
      message = count > 0
        ? `Found ${count} tax group item associations across all tax groups`
        : `No tax group items found - no tax rates are assigned to any tax groups`;
    }

    return {
      success: true,
      taxGroupItems: data || [],
      totalItems: count,
      message
    };
  },
});

// Tool to add a tax rate to a tax group
export const addTaxRateToGroup = createTool({
  id: 'Add Tax Rate To Group',
  description: 'Add a tax rate to a tax group for the current organization. Use this when you need to associate a tax rate with a tax group, build composite tax structures, or when the user wants to organize tax rates into logical groups. Essential for setting up complex tax scenarios where multiple tax rates need to be grouped together.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the tax group item'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    taxGroupId: z.string().uuid().describe('Tax group ID to add the tax rate to'),
    taxRateId: z.string().uuid().describe('Tax rate ID to add to the group'),
  }),
  outputSchema: z.object({
    taxGroupItem: taxGroupItemSchema,
    success: z.boolean().describe('Whether the operation was successful'),
    message: z.string().describe('Success message with details about the tax group association'),
  }),
  execute: async ({ context }) => {
    // Check if tax group exists
    const { data: taxGroup, error: taxGroupError } = await supabase
      .from('tax_groups')
      .select('tax_group_id, tax_group_name')
      .eq('tax_group_id', context.taxGroupId)
      .eq('organization_id', context.organizationId)
      .single();

    if (taxGroupError || !taxGroup) {
      throw new Error(`Tax group with ID ${context.taxGroupId} not found`);
    }

    // Check if tax rate exists
    const { data: taxRate, error: taxRateError } = await supabase
      .from('tax_rates')
      .select('tax_rate_id, name, rate')
      .eq('tax_rate_id', context.taxRateId)
      .eq('organization_id', context.organizationId)
      .single();

    if (taxRateError || !taxRate) {
      throw new Error(`Tax rate with ID ${context.taxRateId} not found`);
    }

    // Check if this association already exists
    const { data: existingItem, error: existingError } = await supabase
      .from('tax_group_items')
      .select('tax_group_item_id')
      .eq('tax_group_id', context.taxGroupId)
      .eq('tax_rate_id', context.taxRateId)
      .eq('organization_id', context.organizationId)
      .maybeSingle();

    if (existingItem) {
      throw new Error(`This tax rate is already in the tax group`);
    }

    // Create the new tax group item
    const { data, error } = await supabase
      .from('tax_group_items')
      .insert({
        tax_group_id: context.taxGroupId,
        tax_rate_id: context.taxRateId,
        organization_id: context.organizationId,
        created_by: context.userId,
        updated_by: context.userId,
      })
      .select(`
        *,
        tax_groups:tax_group_id (tax_group_id, tax_group_name),
        tax_rates:tax_rate_id (tax_rate_id, name, rate, type)
      `)
      .single();

    if (error) {
      throw new Error(`Failed to add tax rate to group: ${error.message}`);
    }

    return {
      taxGroupItem: data,
      success: true,
      message: `Successfully added tax rate "${taxRate.name}" (${taxRate.rate}%) to tax group "${taxGroup.tax_group_name}"`
    };
  },
});

// Tool to remove a tax rate from a tax group
export const removeTaxRateFromGroup = createTool({
  id: 'Remove Tax Rate From Group',
  description: 'Remove a tax rate from a tax group for the current organization. Use this when you need to disassociate a tax rate from a tax group, restructure tax group compositions, or when the user wants to remove tax rates from logical groups. Essential for maintaining accurate tax group structures.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the tax group item'),
    taxGroupId: z.string().uuid().describe('Tax group ID to remove the tax rate from'),
    taxRateId: z.string().uuid().describe('Tax rate ID to remove from the group'),
  }),
  outputSchema: z.object({
    success: z.boolean().describe('Whether the operation was successful'),
    message: z.string().describe('Success message with details about the tax group disassociation'),
    removedAssociation: z.object({
      taxGroupId: z.string().uuid(),
      taxRateId: z.string().uuid(),
    }).describe('Details of the removed association'),
  }),
  execute: async ({ context }) => {
    // Get tax group and tax rate details for better messaging
    const { data: taxGroup, error: taxGroupError } = await supabase
      .from('tax_groups')
      .select('tax_group_name')
      .eq('tax_group_id', context.taxGroupId)
      .eq('organization_id', context.organizationId)
      .single();

    const { data: taxRate, error: taxRateError } = await supabase
      .from('tax_rates')
      .select('name, rate')
      .eq('tax_rate_id', context.taxRateId)
      .eq('organization_id', context.organizationId)
      .single();

    // Check if this association exists
    const { data: existingItem, error: existingError } = await supabase
      .from('tax_group_items')
      .select('tax_group_item_id')
      .eq('tax_group_id', context.taxGroupId)
      .eq('tax_rate_id', context.taxRateId)
      .eq('organization_id', context.organizationId)
      .maybeSingle();

    if (existingError) {
      throw new Error(`Failed to check for existing tax group item: ${existingError.message}`);
    }

    if (!existingItem) {
      throw new Error(`This tax rate is not in the tax group`);
    }

    // Delete the tax group item
    const { error } = await supabase
      .from('tax_group_items')
      .delete()
      .eq('tax_group_id', context.taxGroupId)
      .eq('tax_rate_id', context.taxRateId)
      .eq('organization_id', context.organizationId);

    if (error) {
      throw new Error(`Failed to remove tax rate from group: ${error.message}`);
    }

    const message = taxGroup && taxRate
      ? `Successfully removed tax rate "${taxRate.name}" (${taxRate.rate}%) from tax group "${taxGroup.tax_group_name}"`
      : 'Tax rate successfully removed from tax group';

    return {
      success: true,
      message,
      removedAssociation: {
        taxGroupId: context.taxGroupId,
        taxRateId: context.taxRateId,
      }
    };
  },
});

// Tool to get tax rates not in a specific tax group
export const getTaxRatesNotInGroup = createTool({
  id: 'Get Tax Rates Not In Group',
  description: 'Get all tax rates that are not currently in a specific tax group for the current organization. Use this when you need to find available tax rates to add to a tax group, manage tax group compositions, or when the user wants to see which tax rates are available for assignment. Essential for building and maintaining tax group structures.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter tax rates'),
    taxGroupId: z.string().uuid().describe('Tax group ID to check against'),
    isActive: z.boolean().optional().default(true).describe('Filter tax rates by active status (defaults to true for active rates only)'),
  }),
  outputSchema: z.object({
    taxRates: z.array(z.object({
      tax_rate_id: z.string().uuid(),
      name: z.string(),
      rate: z.number(),
      type: z.string(),
      description: z.string().nullable(),
      is_active: z.boolean(),
      organization_id: z.string().uuid(),
      created_at: z.string(),
      updated_at: z.string(),
    })),
    count: z.number().describe('Number of available tax rates not in the group'),
    message: z.string().describe('Information about available tax rates for the tax group'),
  }),
  execute: async ({ context }) => {
    // Get tax group details for better messaging
    const { data: taxGroup, error: taxGroupError } = await supabase
      .from('tax_groups')
      .select('tax_group_name')
      .eq('tax_group_id', context.taxGroupId)
      .eq('organization_id', context.organizationId)
      .single();

    // First get all tax rates in the group
    const { data: taxGroupItems, error: itemsError } = await supabase
      .from('tax_group_items')
      .select('tax_rate_id')
      .eq('tax_group_id', context.taxGroupId)
      .eq('organization_id', context.organizationId);

    if (itemsError) {
      throw new Error(`Failed to get tax group items: ${itemsError.message}`);
    }

    // Extract tax rate IDs
    const taxRateIds = taxGroupItems.map(item => item.tax_rate_id);

    // Get all tax rates not in the group
    let query = supabase
      .from('tax_rates')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    // If there are tax rates in the group, exclude them
    if (taxRateIds.length > 0) {
      query = query.not('tax_rate_id', 'in', `(${taxRateIds.join(',')})`);
    }

    const { data, error } = await query.order('name');

    if (error) {
      throw new Error(`Failed to get tax rates not in group: ${error.message}`);
    }

    const count = data?.length || 0;
    const groupName = taxGroup?.tax_group_name || 'the specified tax group';
    const activeFilter = context.isActive ? ' active' : '';

    let message = '';
    if (count === 0) {
      message = `No${activeFilter} tax rates are available to add to ${groupName}. All${activeFilter} tax rates are already assigned to this group.`;
    } else {
      message = `Found ${count}${activeFilter} tax rate(s) available to add to ${groupName}`;
    }

    return {
      taxRates: data || [],
      count,
      message
    };
  },
});
