import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define tax rate type enum for validation
const taxRateTypeEnum = z.enum(['percentage', 'fixed']);

// Define tax rate schema for reuse
const taxRateSchema = z.object({
  tax_rate_id: z.string().uuid(),
  tax_rate_name: z.string(),
  tax_rate_percentage: z.number(),
  tax_rate_type: z.enum(['percentage', 'fixed']),
  is_compound: z.boolean(),
  is_recoverable: z.boolean(),
  tax_account_id: z.string().uuid().nullable(),
  is_active: z.boolean(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Define TaxRate interface for type safety
interface TaxRate {
  tax_rate_id?: string;
  tax_rate_name?: string;
  tax_rate_percentage?: number;
  tax_rate_type?: string;
  is_compound?: boolean;
  is_recoverable?: boolean;
  tax_account_id?: string | null;
  is_active?: boolean;
  organization_id?: string;
  created_at?: string;
  updated_at?: string;
}

// Tool to get tax rates
export const getTaxRates = createTool({
  id: 'Get Tax Rates',
  description: 'Retrieve tax rates for calculating taxes on transactions. Use this when you need to display available tax rates, apply taxes to invoices/bills, or when the user asks about tax rates. Tax rates can be percentage-based (like VAT/GST) or fixed amounts.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter tax rates'),
    taxRateId: z.string().uuid().optional().describe('Specific tax rate ID to retrieve'),
    type: taxRateTypeEnum.optional().describe('Filter by tax rate type (percentage, fixed)'),
    isActive: z.boolean().optional().describe('Filter by active status'),
  }),
  outputSchema: z.object({
    taxRates: z.array(taxRateSchema),
    message: z.string().describe('Information about tax rates and their usage'),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('tax_rates')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.taxRateId) {
      query = query.eq('tax_rate_id', context.taxRateId);
    }

    if (context.type) {
      query = query.eq('tax_rate_type', context.type);
    }

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query.order('tax_rate_name');

    if (error) {
      throw new Error(`Failed to get tax rates: ${error.message}`);
    }

    const message = `Tax rates are used to calculate taxes on transactions. Percentage rates (like VAT/GST) are applied as a percentage of the transaction amount, while fixed rates apply a specific dollar amount. Compound taxes are calculated on top of other taxes, and recoverable taxes can be claimed back from tax authorities.`;

    return {
      taxRates: data,
      message
    };
  },
});

// Tool to create a tax rate
export const createTaxRate = createTool({
  id: 'Create Tax Rate',
  description: 'Create a new tax rate for calculating taxes on transactions. Use this when the user wants to add a new tax rate like VAT, GST, sales tax, or other taxes. Tax rates can be percentage-based or fixed amounts and can be configured as compound or recoverable.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the tax rate'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    name: z.string().describe('Tax rate name'),
    rate: z.number().describe('Tax rate value (percentage or fixed amount)'),
    type: taxRateTypeEnum.describe('Tax rate type (percentage or fixed)'),
    isCompound: z.boolean().optional().default(false).describe('Whether this tax is applied on top of other taxes'),
    isRecoverable: z.boolean().optional().default(true).describe('Whether this tax is recoverable (e.g., VAT/GST)'),
    taxAccountId: z.string().uuid().optional().describe('Account ID for tracking this tax'),
    isActive: z.boolean().optional().default(true).describe('Whether the tax rate is active'),
  }),
  outputSchema: z.object({
    taxRate: taxRateSchema,
  }),
  execute: async ({ context }) => {
    // Validate rate for percentage type (should be between 0 and 100)
    if (context.type === 'percentage' && (context.rate < 0 || context.rate > 100)) {
      throw new Error('Percentage tax rates must be between 0 and 100');
    }

    // Check if tax account exists if provided
    if (context.taxAccountId) {
      const { data: taxAccount, error: taxAccountError } = await supabase
        .from('accounts')
        .select('account_id')
        .eq('account_id', context.taxAccountId)
        .eq('organization_id', context.organizationId)
        .single();

      if (taxAccountError || !taxAccount) {
        throw new Error(`Tax account with ID ${context.taxAccountId} not found`);
      }
    }

    // Create the new tax rate
    const { data, error } = await supabase
      .from('tax_rates')
      .insert({
        tax_rate_name: context.name,
        tax_rate_percentage: context.rate,
        tax_rate_type: context.type,
        is_compound: context.isCompound,
        is_recoverable: context.isRecoverable,
        tax_account_id: context.taxAccountId || null,
        is_active: context.isActive,
        organization_id: context.organizationId,
        created_by: context.userId,
        updated_by: context.userId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create tax rate: ${error.message}`);
    }

    return { taxRate: data };
  },
});

// Tool to update a tax rate
export const updateTaxRate = createTool({
  id: 'Update Tax Rate',
  description: 'Update an existing tax rate used for calculating taxes. Use this when the user wants to modify tax rate details like rate percentage, name, or configuration. Be careful when updating rates as this affects tax calculations on future transactions.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the tax rate'),
    userId: z.string().uuid().describe('User ID for updated_by field'),
    taxRateId: z.string().uuid().describe('Tax rate ID to update'),
    name: z.string().optional().describe('Updated tax rate name'),
    rate: z.number().optional().describe('Updated tax rate value'),
    type: taxRateTypeEnum.optional().describe('Updated tax rate type'),
    isCompound: z.boolean().optional().describe('Updated compound status'),
    isRecoverable: z.boolean().optional().describe('Updated recoverable status'),
    taxAccountId: z.string().uuid().optional().describe('Updated tax account ID'),
    isActive: z.boolean().optional().describe('Updated active status'),
  }),
  outputSchema: z.object({
    taxRate: taxRateSchema,
  }),
  execute: async ({ context }) => {
    // Check if tax rate exists
    const { data: existingTaxRate, error: existingError } = await supabase
      .from('tax_rates')
      .select('*')
      .eq('tax_rate_id', context.taxRateId)
      .eq('organization_id', context.organizationId)
      .single();

    if (existingError || !existingTaxRate) {
      throw new Error(`Tax rate with ID ${context.taxRateId} not found`);
    }

    // Validate rate for percentage type
    if ((context.type === 'percentage' || (existingTaxRate.tax_rate_type === 'percentage' && context.type === undefined)) &&
        context.rate !== undefined && (context.rate < 0 || context.rate > 100)) {
      throw new Error('Percentage tax rates must be between 0 and 100');
    }

    // Check if tax account exists if provided
    if (context.taxAccountId) {
      const { data: taxAccount, error: taxAccountError } = await supabase
        .from('accounts')
        .select('account_id')
        .eq('account_id', context.taxAccountId)
        .eq('organization_id', context.organizationId)
        .single();

      if (taxAccountError || !taxAccount) {
        throw new Error(`Tax account with ID ${context.taxAccountId} not found`);
      }
    }

    // Update the tax rate
    const updateData: any = {
      updated_by: context.userId,
    };
    if (context.name !== undefined) updateData.tax_rate_name = context.name;
    if (context.rate !== undefined) updateData.tax_rate_percentage = context.rate;
    if (context.type !== undefined) updateData.tax_rate_type = context.type;
    if (context.isCompound !== undefined) updateData.is_compound = context.isCompound;
    if (context.isRecoverable !== undefined) updateData.is_recoverable = context.isRecoverable;
    if (context.taxAccountId !== undefined) updateData.tax_account_id = context.taxAccountId;
    if (context.isActive !== undefined) updateData.is_active = context.isActive;

    const { data, error } = await supabase
      .from('tax_rates')
      .update(updateData)
      .eq('tax_rate_id', context.taxRateId)
      .eq('organization_id', context.organizationId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update tax rate: ${error.message}`);
    }

    return { taxRate: data };
  },
});

// Tool to search for tax rates by name
export const searchTaxRatesByName = createTool({
  id: 'Search Tax Rates By Name',
  description: 'Search for tax rates by name when you need to find specific tax rates for transaction calculations or when the user asks for tax rates containing certain text. Use this when you know part of a tax rate name but need to find the exact rate.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter tax rates'),
    nameContains: z.string().describe('Text to search for in tax rate names'),
    type: taxRateTypeEnum.optional().describe('Filter by tax rate type'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    limit: z.number().min(1).max(100).default(10).describe('Maximum number of tax rates to return'),
  }),
  outputSchema: z.object({
    taxRates: z.array(taxRateSchema),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('tax_rates')
      .select('*')
      .ilike('tax_rate_name', `%${context.nameContains}%`)
      .eq('organization_id', context.organizationId);

    if (context.type) {
      query = query.eq('tax_rate_type', context.type);
    }

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query
      .order('tax_rate_name')
      .limit(context.limit);

    if (error) {
      throw new Error(`Failed to search for tax rates: ${error.message}`);
    }

    return { taxRates: data };
  },
});
