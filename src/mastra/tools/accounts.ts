import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Account types enum for reuse
const accountTypeEnum = z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']);

// Account type detail enums based on account type
const accountTypeDetailEnum = z.enum([
  // Asset details
  'accounts_receivable', 'other_current_assets', 'bank', 'fixed_assets', 'other_assets',
  // Liability details
  'accounts_payable', 'credit_card', 'other_current_liabilities', 'long_term_liabilities',
  // Equity details
  'equity',
  // Revenue details
  'revenue', 'other_revenue',
  // Expense details
  'cost_of_goods_sold', 'expenditures', 'other_expenditure'
]);

// Helper function to get valid account type details for a given account type
const getValidAccountTypeDetails = (accountType: string): string[] => {
  switch (accountType) {
    case 'asset':
      return ['accounts_receivable', 'other_current_assets', 'bank', 'fixed_assets', 'other_assets'];
    case 'liability':
      return ['accounts_payable', 'credit_card', 'other_current_liabilities', 'long_term_liabilities'];
    case 'equity':
      return ['equity'];
    case 'revenue':
      return ['revenue', 'other_revenue'];
    case 'expense':
      return ['cost_of_goods_sold', 'expenditures', 'other_expenditure'];
    default:
      return [];
  }
};

// Helper function to validate account type detail
const validateAccountTypeDetail = (accountType: string, accountTypeDetail: string): { isValid: boolean; message: string } => {
  const validDetails = getValidAccountTypeDetails(accountType);

  if (!validDetails.includes(accountTypeDetail)) {
    return {
      isValid: false,
      message: `Invalid account_type_detail '${accountTypeDetail}' for account_type '${accountType}'. Valid options: ${validDetails.join(', ')}`
    };
  }

  return { isValid: true, message: 'Valid account type detail' };
};

// Helper function to validate account code format and series
const validateAccountCode = (code: string, type: string): { isValid: boolean; message: string } => {
  // Check if code is exactly 4 digits
  if (!/^\d{4}$/.test(code)) {
    return { isValid: false, message: 'Account code must be exactly 4 digits' };
  }

  const codeNum = parseInt(code, 10);

  // Validate code series based on account type
  switch (type) {
    case 'asset':
      if (codeNum < 1000 || codeNum > 1999) {
        return { isValid: false, message: 'Asset accounts must use codes between 1000-1999' };
      }
      break;
    case 'liability':
      if (codeNum < 2000 || codeNum > 2999) {
        return { isValid: false, message: 'Liability accounts must use codes between 2000-2999' };
      }
      break;
    case 'equity':
      if (codeNum < 3000 || codeNum > 3999) {
        return { isValid: false, message: 'Equity accounts must use codes between 3000-3999' };
      }
      break;
    case 'revenue':
      if (codeNum < 4000 || codeNum > 4999) {
        return { isValid: false, message: 'Revenue accounts must use codes between 4000-4999' };
      }
      break;
    case 'expense':
      if (codeNum < 5000 || codeNum > 5999) {
        return { isValid: false, message: 'Expense accounts must use codes between 5000-5999' };
      }
      break;
    default:
      return { isValid: false, message: 'Invalid account type' };
  }

  return { isValid: true, message: 'Valid account code' };
};

// Define account schema for reuse
const accountSchema = z.object({
  account_id: z.string().uuid(),
  account_code: z.number(),
  account_name: z.string(),
  account_type: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']),
  account_type_detail: accountTypeDetailEnum,
  description: z.string().nullable(),
  parent_id: z.string().uuid().nullable(),
  account_level: z.number(),
  is_summary: z.boolean(),
  is_active: z.boolean(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Tool to get all accounts
export const getAccounts = createTool({
  id: 'Get Accounts',
  description: 'Retrieve all accounts from the chart of accounts. Use this when you need to display the complete chart of accounts, find available accounts for transaction recording, or when the user asks to see all accounts. You can filter by account type (asset, liability, equity, revenue, expense) or active status.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter accounts'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    accountType: accountTypeEnum.optional().describe('Filter by account type'),
    accountTypeDetail: accountTypeDetailEnum.optional().describe('Filter by account type detail'),
  }),
  outputSchema: z.object({
    accounts: z.array(accountSchema),
    accountCodeStandard: z.string().describe('Information about account code standards and hierarchy'),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('accounts')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    if (context.accountType) {
      query = query.eq('account_type', context.accountType);
    }

    if (context.accountTypeDetail) {
      query = query.eq('account_type_detail', context.accountTypeDetail);
    }

    const { data, error } = await query.order('account_code');

    if (error) {
      throw new Error(`Failed to get accounts: ${error.message}`);
    }

    // Add information about account code standardization
    const accountCodeInfo = `
Account codes follow a standardized format:
- 4-digit codes only
- Asset accounts: 1000-1999
- Liability accounts: 2000-2999
- Equity accounts: 3000-3999
- Revenue accounts: 4000-4999
- Expense accounts: 5000-5999

Account codes can be auto-generated when creating a new account. The system will automatically assign the next available code in the appropriate range based on the account type.

Accounts can be organized in a hierarchical structure:
- Parent-child relationships allow for grouping related accounts
- Summary accounts can be used to create account groups
- Account levels indicate the depth in the hierarchy (1 for top-level)
`;

    return {
      accounts: data,
      accountCodeStandard: accountCodeInfo
    };
  },
});

// Tool to create a new account
export const createAccount = createTool({
  id: 'Create Account',
  description: 'Create a new account in the chart of accounts. Use this when the user wants to add a new account for tracking assets, liabilities, equity, revenue, or expenses. The system will auto-generate account codes following standard accounting practices if not provided.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the account'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    accountCode: z.string().optional().describe('Account code (optional - will be auto-generated if not provided). If provided, must be 4 digits and follow the series: 1000-1999 for assets, 2000-2999 for liabilities, 3000-3999 for equity, 4000-4999 for revenue, 5000-5999 for expenses)'),
    name: z.string().describe('Account name'),
    accountType: accountTypeEnum.describe('Account type'),
    accountTypeDetail: accountTypeDetailEnum.describe('Account type detail - specific classification within the account type'),
    description: z.string().optional().describe('Optional account description'),
    parentId: z.string().uuid().optional().describe('Parent account ID for hierarchical chart of accounts'),
    isSummary: z.boolean().default(false).describe('Whether this is a summary account (used for grouping)'),
    accountLevel: z.number().min(1).max(10).optional().describe('Account level in the hierarchy (1 for top-level, automatically calculated if not provided)'),
    isActive: z.boolean().default(true).describe('Whether the account is active'),
  }),
  outputSchema: z.object({
    account: accountSchema,
    message: z.string().describe('Success message with details about the created account'),
  }),
  execute: async ({ context }) => {
    let accountCode: number;

    // Validate account type detail
    const typeDetailValidation = validateAccountTypeDetail(context.accountType, context.accountTypeDetail);
    if (!typeDetailValidation.isValid) {
      throw new Error(typeDetailValidation.message);
    }

    // If no code provided, generate one automatically
    if (!context.accountCode) {
      // Determine the starting code based on account type
      let startingCode;
      let maxCode;
      switch (context.accountType) {
        case 'asset':
          startingCode = 1000;
          maxCode = 1999;
          break;
        case 'liability':
          startingCode = 2000;
          maxCode = 2999;
          break;
        case 'equity':
          startingCode = 3000;
          maxCode = 3999;
          break;
        case 'revenue':
          startingCode = 4000;
          maxCode = 4999;
          break;
        case 'expense':
          startingCode = 5000;
          maxCode = 5999;
          break;
        default:
          throw new Error(`Invalid account type: ${context.accountType}`);
      }

      // Get the highest existing code for this account type and organization
      const { data: accounts, error: queryError } = await supabase
        .from('accounts')
        .select('account_code')
        .eq('account_type', context.accountType)
        .eq('organization_id', context.organizationId)
        .gte('account_code', startingCode.toString())
        .lte('account_code', maxCode.toString())
        .order('account_code', { ascending: false });

      if (queryError) {
        throw new Error(`Failed to generate account code: ${queryError.message}`);
      }

      // Find the highest existing code or use the starting code
      let nextCode = startingCode;
      if (accounts && accounts.length > 0) {
        // Find the highest numeric code
        const highestCode = accounts.reduce((max, account) => {
          const code = account.account_code;
          return code > max ? code : max;
        }, startingCode - 1);

        nextCode = highestCode + 1;
      }

      // Ensure the code stays within the valid range
      if (nextCode > maxCode) {
        throw new Error(`Cannot generate a new code for ${context.accountType} accounts: maximum code ${maxCode} reached`);
      }

      accountCode = nextCode;
    } else {
      // Convert string account code to number and validate
      accountCode = parseInt(context.accountCode);
      if (isNaN(accountCode)) {
        throw new Error(`Invalid account code: ${context.accountCode}. Account code must be a valid number.`);
      }

      // Validate manually provided code
      const validation = validateAccountCode(context.accountCode, context.accountType);
      if (!validation.isValid) {
        throw new Error(validation.message);
      }
    }

    // Check if an account with the same code already exists in this organization
    const { data: existingAccount, error: checkError } = await supabase
      .from('accounts')
      .select('*')
      .eq('account_code', accountCode)
      .eq('organization_id', context.organizationId)
      .maybeSingle();

    if (checkError) {
      throw new Error(`Failed to check for existing account: ${checkError.message}`);
    }

    if (existingAccount) {
      throw new Error(`An account with code ${accountCode} already exists in this organization`);
    }

    // If parent ID is provided, validate it and determine account level
    let accountLevel = context.accountLevel || 1; // Default to top level

    if (context.parentId) {
      // Check if parent account exists and belongs to the same organization
      const { data: parentAccount, error: parentError } = await supabase
        .from('accounts')
        .select('*')
        .eq('account_id', context.parentId)
        .eq('organization_id', context.organizationId)
        .single();

      if (parentError) {
        throw new Error(`Failed to find parent account: ${parentError.message}`);
      }

      if (!parentAccount) {
        throw new Error(`Parent account with ID ${context.parentId} not found in this organization`);
      }

      // Check if parent account is of the same type
      if (parentAccount.account_type !== context.accountType) {
        throw new Error(`Parent account type (${parentAccount.account_type}) does not match child account type (${context.accountType})`);
      }

      // If account level not explicitly provided, calculate it based on parent
      if (!context.accountLevel) {
        accountLevel = parentAccount.account_level + 1;
      }
    }

    // Create the new account
    const { data: newAccount, error: createError } = await supabase
      .from('accounts')
      .insert({
        account_code: accountCode,
        account_name: context.name,
        account_type: context.accountType,
        account_type_detail: context.accountTypeDetail,
        description: context.description,
        parent_id: context.parentId || null,
        account_level: accountLevel,
        is_summary: context.isSummary || false,
        is_active: context.isActive,
        organization_id: context.organizationId,
        created_by: context.userId,
        updated_by: context.userId,
      })
      .select()
      .single();

    if (createError) {
      throw new Error(`Failed to create account: ${createError.message}`);
    }

    return {
      account: newAccount,
      message: `Successfully created ${context.accountType} account: ${context.name} (${accountCode})${!context.accountCode ? ' with auto-generated code' : ''}`
    };
  },
});

// Tool to search for accounts by name
export const searchAccountsByName = createTool({
  id: 'Search Accounts By Name',
  description: 'Search for accounts by name when you need to find specific accounts for transaction recording or when the user asks for accounts containing certain text. Use this when you know part of an account name but need to find the exact account.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter accounts'),
    nameContains: z.string().describe('Text to search for in account names'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    accountType: accountTypeEnum.optional().describe('Filter by account type'),
    accountTypeDetail: accountTypeDetailEnum.optional().describe('Filter by account type detail'),
    limit: z.number().min(1).max(100).default(10).describe('Maximum number of accounts to return'),
  }),
  outputSchema: z.object({
    found: z.boolean().describe('Whether any accounts were found'),
    accounts: z.array(accountSchema),
    count: z.number().optional().describe('Number of accounts found'),
    accountCodeStandard: z.string().describe('Information about account code standards'),
    message: z.string().describe('Search result message'),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('accounts')
      .select('*')
      .ilike('account_name', `%${context.nameContains}%`)
      .eq('organization_id', context.organizationId);

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    if (context.accountType) {
      query = query.eq('account_type', context.accountType);
    }

    if (context.accountTypeDetail) {
      query = query.eq('account_type_detail', context.accountTypeDetail);
    }

    const { data, error } = await query
      .order('account_name')
      .limit(context.limit);

    if (error) {
      throw new Error(`Failed to search for accounts: ${error.message}`);
    }

    // Add information about account code standardization
    const accountCodeInfo = `
Account codes follow a standardized format:
- 4-digit codes only
- Asset accounts: 1000-1999
- Liability accounts: 2000-2999
- Equity accounts: 3000-3999
- Revenue accounts: 4000-4999
- Expense accounts: 5000-5999

Account codes can be auto-generated when creating a new account. The system will automatically assign the next available code in the appropriate range based on the account type.

Accounts can be organized in a hierarchical structure:
- Parent-child relationships allow for grouping related accounts
- Summary accounts can be used to create account groups
- Account levels indicate the depth in the hierarchy (1 for top-level)
`;

    if (!data || data.length === 0) {
      return {
        found: false,
        accounts: [],
        accountCodeStandard: accountCodeInfo,
        message: `No accounts found with name containing "${context.nameContains}"`
      };
    }

    return {
      found: true,
      accounts: data,
      count: data.length,
      accountCodeStandard: accountCodeInfo,
      message: `Found ${data.length} account(s) with name containing "${context.nameContains}"`
    };
  },
});

// Tool to get an account by its code
export const getAccountByCode = createTool({
  id: 'Get Account By Code',
  description: 'Find a specific account by its 4-digit account code. Use this when you have an account code and need to verify the account details or when recording transactions with specific account codes.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter accounts'),
    accountCode: z.string().describe('Account code to search for (4-digit code)'),
  }),
  outputSchema: z.object({
    found: z.boolean().describe('Whether the account was found'),
    account: accountSchema.optional().describe('Account details if found'),
    message: z.string().describe('Result message with account details or error information'),
  }),
  execute: async ({ context }) => {
    // Check if code is in the correct format (4 digits)
    if (!/^\d{4}$/.test(context.accountCode)) {
      return {
        found: false,
        message: `Invalid account code format. Account codes must be exactly 4 digits.`
      };
    }

    const numericAccountCode = parseInt(context.accountCode);
    const { data, error } = await supabase
      .from('accounts')
      .select('*')
      .eq('account_code', numericAccountCode)
      .eq('organization_id', context.organizationId)
      .maybeSingle();

    if (error) {
      throw new Error(`Failed to get account by code: ${error.message}`);
    }

    if (!data) {
      // Provide a hint about the account code series
      const codeNum = parseInt(context.accountCode, 10);
      let seriesHint = '';

      if (codeNum >= 1000 && codeNum <= 1999) {
        seriesHint = ' This code is in the asset account range (1000-1999).';
      } else if (codeNum >= 2000 && codeNum <= 2999) {
        seriesHint = ' This code is in the liability account range (2000-2999).';
      } else if (codeNum >= 3000 && codeNum <= 3999) {
        seriesHint = ' This code is in the equity account range (3000-3999).';
      } else if (codeNum >= 4000 && codeNum <= 4999) {
        seriesHint = ' This code is in the revenue account range (4000-4999).';
      } else if (codeNum >= 5000 && codeNum <= 5999) {
        seriesHint = ' This code is in the expense account range (5000-5999).';
      }

      return {
        found: false,
        message: `No account found with code "${context.accountCode}".${seriesHint}`
      };
    }

    return {
      found: true,
      account: data,
      message: `Found account: ${data.account_name} (${data.account_code})`
    };
  },
});

// Tool to update an existing account
export const updateAccount = createTool({
  id: 'Update Account',
  description: 'Update an existing account in the chart of accounts. Use this when the user wants to modify account details like name, description, or hierarchy. Be careful when updating account codes as this affects transaction history.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the account'),
    userId: z.string().uuid().describe('User ID for updated_by field'),
    accountId: z.string().uuid().describe('ID of the account to update'),
    accountCode: z.string().optional().describe('Updated account code (must be 4 digits and follow the series: 1000-1999 for assets, 2000-2999 for liabilities, 3000-3999 for equity, 4000-4999 for revenue, 5000-5999 for expenses)'),
    name: z.string().optional().describe('Updated account name'),
    accountTypeDetail: accountTypeDetailEnum.optional().describe('Updated account type detail - specific classification within the account type'),
    description: z.string().optional().describe('Updated account description'),
    parentId: z.string().uuid().optional().describe('Updated parent account ID for hierarchical chart of accounts'),
    isSummary: z.boolean().optional().describe('Update whether this is a summary account (used for grouping)'),
    accountLevel: z.number().min(1).max(10).optional().describe('Updated account level in the hierarchy (1 for top-level)'),
    isActive: z.boolean().optional().describe('Update active status'),
  }),
  outputSchema: z.object({
    account: accountSchema,
    message: z.string().describe('Success message with details about the updated account'),
  }),
  execute: async ({ context }) => {
    // First check if the account exists in this organization
    const { data: existingAccount, error: checkError } = await supabase
      .from('accounts')
      .select('*')
      .eq('account_id', context.accountId)
      .eq('organization_id', context.organizationId)
      .single();

    if (checkError) {
      throw new Error(`Failed to find account: ${checkError.message}`);
    }

    if (!existingAccount) {
      throw new Error(`Account with ID ${context.accountId} not found in this organization`);
    }

    // If updating account type detail, validate it
    if (context.accountTypeDetail && context.accountTypeDetail !== existingAccount.account_type_detail) {
      const typeDetailValidation = validateAccountTypeDetail(existingAccount.account_type, context.accountTypeDetail);
      if (!typeDetailValidation.isValid) {
        throw new Error(typeDetailValidation.message);
      }
    }

    // If updating the code, validate it and check for duplicates
    if (context.accountCode && parseInt(context.accountCode) !== existingAccount.account_code) {
      // Validate account code format and series
      const validation = validateAccountCode(context.accountCode, existingAccount.account_type);
      if (!validation.isValid) {
        throw new Error(validation.message);
      }

      const numericAccountCode = parseInt(context.accountCode);
      // Check if the new code already exists on a different account in this organization
      const { data: codeExists, error: codeCheckError } = await supabase
        .from('accounts')
        .select('account_id')
        .eq('account_code', numericAccountCode)
        .eq('organization_id', context.organizationId)
        .neq('account_id', context.accountId)
        .maybeSingle();

      if (codeCheckError) {
        throw new Error(`Failed to check for duplicate account code: ${codeCheckError.message}`);
      }

      if (codeExists) {
        throw new Error(`Another account with code ${context.accountCode} already exists in this organization`);
      }
    }

    // If updating parent ID, validate it and determine account level
    let accountLevel = context.accountLevel;

    if (context.parentId !== undefined) {
      // Check for circular references
      if (context.parentId === context.accountId) {
        throw new Error('An account cannot be its own parent');
      }

      // If setting a parent (not removing it)
      if (context.parentId) {
        // Check if parent account exists and belongs to the same organization
        const { data: parentAccount, error: parentError } = await supabase
          .from('accounts')
          .select('*')
          .eq('account_id', context.parentId)
          .eq('organization_id', context.organizationId)
          .single();

        if (parentError) {
          throw new Error(`Failed to find parent account: ${parentError.message}`);
        }

        if (!parentAccount) {
          throw new Error(`Parent account with ID ${context.parentId} not found in this organization`);
        }

        // Check if parent account is of the same type
        if (parentAccount.account_type !== existingAccount.account_type) {
          throw new Error(`Parent account type (${parentAccount.account_type}) does not match this account type (${existingAccount.account_type})`);
        }

        // Check for circular references in the hierarchy
        let currentParentId = parentAccount.parent_id;
        while (currentParentId) {
          if (currentParentId === context.accountId) {
            throw new Error('Circular reference detected in account hierarchy');
          }

          const { data: ancestorAccount, error: ancestorError } = await supabase
            .from('accounts')
            .select('parent_id')
            .eq('account_id', currentParentId)
            .single();

          if (ancestorError || !ancestorAccount) {
            break;
          }

          currentParentId = ancestorAccount.parent_id;
        }

        // If account level not explicitly provided, calculate it based on parent
        if (accountLevel === undefined) {
          accountLevel = parentAccount.account_level + 1;
        }
      } else {
        // If removing parent, set to top level if not specified
        if (accountLevel === undefined) {
          accountLevel = 1;
        }
      }
    }

    // Check if this account has children (if changing to non-summary)
    if (context.isSummary === false) {
      const { data: children, error: childrenError } = await supabase
        .from('accounts')
        .select('account_id')
        .eq('parent_id', context.accountId)
        .limit(1);

      if (childrenError) {
        throw new Error(`Failed to check for child accounts: ${childrenError.message}`);
      }

      if (children && children.length > 0) {
        throw new Error('Cannot change a parent account to non-summary while it has child accounts');
      }
    }

    // Prepare update data (only include fields that were provided)
    const updateData: Record<string, any> = {};

    if (context.accountCode !== undefined) updateData.account_code = parseInt(context.accountCode);
    if (context.name !== undefined) updateData.account_name = context.name;
    if (context.accountTypeDetail !== undefined) updateData.account_type_detail = context.accountTypeDetail;
    if (context.description !== undefined) updateData.description = context.description;
    if (context.parentId !== undefined) updateData.parent_id = context.parentId;
    if (accountLevel !== undefined) updateData.account_level = accountLevel;
    if (context.isSummary !== undefined) updateData.is_summary = context.isSummary;
    if (context.isActive !== undefined) updateData.is_active = context.isActive;

    // Only update if there are changes
    if (Object.keys(updateData).length === 0) {
      return {
        account: existingAccount,
        message: 'No changes provided for update'
      };
    }

    // Update the account
    const { data: updatedAccount, error: updateError } = await supabase
      .from('accounts')
      .update({
        ...updateData,
        updated_by: context.userId,
        updated_at: new Date().toISOString(),
      })
      .eq('account_id', context.accountId)
      .eq('organization_id', context.organizationId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update account: ${updateError.message}`);
    }

    return {
      account: updatedAccount,
      message: `Successfully updated account ${updatedAccount.account_name} (${updatedAccount.account_code})`
    };
  },
});