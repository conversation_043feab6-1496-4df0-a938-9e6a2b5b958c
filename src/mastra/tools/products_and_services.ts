import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define product/service type enum
const productServiceTypeEnum = z.enum(['product', 'service']);

// Define product/service schema
const productServiceSchema = z.object({
  product_service_id: z.string().uuid(),
  product_service_name: z.string(),
  description: z.string().nullable(),
  product_service_type: productServiceTypeEnum,
  sku: z.string().nullable(),
  barcode: z.string().nullable(),
  sales_price: z.number().nullable(),
  purchase_price: z.number().nullable(),
  revenue_account_id: z.string().uuid(),
  expense_account_id: z.string().uuid().nullable(),
  purchase_qty: z.number(),
  sale_qty: z.number(),
  current_qty: z.number(),
  total_revenue: z.number(),
  total_cost: z.number(),
  total_profit: z.number(),
  last_sale_date: z.string().nullable(),
  last_purchase_date: z.string().nullable(),
  last_sale_price: z.number().nullable(),
  last_purchase_price: z.number().nullable(),
  is_active: z.boolean(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Define ProductService interface for type safety
interface ProductService {
  product_service_id?: string;
  product_service_name?: string;
  description?: string | null;
  product_service_type?: 'product' | 'service';
  sku?: string | null;
  barcode?: string | null;
  sales_price?: number | null;
  purchase_price?: number | null;
  revenue_account_id?: string;
  expense_account_id?: string | null;
  purchase_qty?: number;
  sale_qty?: number;
  current_qty?: number;
  total_revenue?: number;
  total_cost?: number;
  total_profit?: number;
  last_sale_date?: string | null;
  last_purchase_date?: string | null;
  last_sale_price?: number | null;
  last_purchase_price?: number | null;
  is_active?: boolean;
  organization_id?: string;
  created_at?: string;
  updated_at?: string;
}

// Tool to get products and services
export const getProductsAndServices = createTool({
  id: 'Get Products and Services',
  description: 'Retrieve products and services from the system. Use this when you need to display available products/services, find items for transaction recording, or when the user asks about products/services. You can filter by type (product/service) or active status.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter products and services'),
    productServiceId: z.string().uuid().optional().describe('Specific product/service ID to retrieve'),
    type: productServiceTypeEnum.optional().describe('Filter by type (product or service)'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    includeStats: z.boolean().optional().default(false).describe('Include sales and purchase statistics'),
  }),
  outputSchema: z.object({
    productsAndServices: z.array(productServiceSchema),
    message: z.string().optional(),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('products_and_services')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.productServiceId) {
      query = query.eq('product_service_id', context.productServiceId);
    }

    if (context.type) {
      query = query.eq('product_service_type', context.type);
    }

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query.order('product_service_name');

    if (error) {
      throw new Error(`Failed to get products and services: ${error.message}`);
    }

    const message = data.length === 0
      ? 'No products or services found matching the criteria.'
      : `Found ${data.length} ${context.type ? context.type + 's' : 'products and services'}.`;

    return {
      productsAndServices: data,
      message
    };
  },
});

// Tool to create a product or service
export const createProductOrService = createTool({
  id: 'Create Product or Service',
  description: 'Create a new product or service. Products are physical goods you sell, services are intangible offerings. Both require a revenue account. Products should also have an expense account for COGS tracking.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the product/service'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    name: z.string().describe('Product or service name'),
    description: z.string().optional().describe('Description'),
    type: productServiceTypeEnum.describe('Type: product (physical goods) or service (intangible)'),
    sku: z.string().optional().describe('Stock Keeping Unit (SKU)'),
    barcode: z.string().optional().describe('Barcode or UPC'),
    salesPrice: z.number().optional().describe('Default sales price'),
    purchasePrice: z.number().optional().describe('Default purchase price (for products)'),
    revenueAccountId: z.string().uuid().describe('Revenue account ID (where sales are recorded)'),
    expenseAccountId: z.string().uuid().optional().describe('Expense account ID (for COGS - required for products)'),
    isActive: z.boolean().optional().default(true).describe('Whether the item is active'),
  }),
  outputSchema: z.object({
    productService: productServiceSchema,
    message: z.string(),
  }),
  execute: async ({ context }) => {
    // Validate that products have expense accounts for COGS
    if (context.type === 'product' && !context.expenseAccountId) {
      throw new Error('Products require an expense account for Cost of Goods Sold (COGS) tracking');
    }

    // Validate revenue account exists
    const { data: revenueAccount, error: revenueError } = await supabase
      .from('accounts')
      .select('account_id, account_name, account_type')
      .eq('account_id', context.revenueAccountId)
      .eq('organization_id', context.organizationId)
      .single();

    if (revenueError || !revenueAccount) {
      throw new Error(`Revenue account with ID ${context.revenueAccountId} not found`);
    }

    // Validate expense account if provided
    if (context.expenseAccountId) {
      const { data: expenseAccount, error: expenseError } = await supabase
        .from('accounts')
        .select('account_id, account_name, account_type')
        .eq('account_id', context.expenseAccountId)
        .eq('organization_id', context.organizationId)
        .single();

      if (expenseError || !expenseAccount) {
        throw new Error(`Expense account with ID ${context.expenseAccountId} not found`);
      }
    }

    // Create the new product/service
    const { data, error } = await supabase
      .from('products_and_services')
      .insert({
        product_service_name: context.name,
        description: context.description || null,
        product_service_type: context.type,
        sku: context.sku || null,
        barcode: context.barcode || null,
        sales_price: context.salesPrice || null,
        purchase_price: context.purchasePrice || null,
        revenue_account_id: context.revenueAccountId,
        expense_account_id: context.expenseAccountId || null,
        is_active: context.isActive,
        organization_id: context.organizationId,
        created_by: context.userId,
        updated_by: context.userId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create ${context.type}: ${error.message}`);
    }

    return { 
      productService: data,
      message: `Successfully created ${context.type} "${context.name}". ${context.type === 'product' ? 'Sales will automatically update inventory quantities and calculate COGS.' : 'Service sales will be tracked for revenue analysis.'}`
    };
  },
});

// Tool to get product/service performance analytics
export const getProductServiceAnalytics = createTool({
  id: 'Get Product Service Analytics',
  description: 'Get detailed analytics for products and services including sales performance, profit margins, and inventory status. Perfect for business intelligence and decision making.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter analytics'),
    productServiceId: z.string().uuid().optional().describe('Specific product/service ID for detailed analysis'),
    type: productServiceTypeEnum.optional().describe('Filter by type (product or service)'),
    sortBy: z.enum(['revenue', 'profit', 'quantity', 'margin']).optional().default('revenue').describe('Sort results by metric'),
    limit: z.number().optional().default(50).describe('Maximum number of results'),
  }),
  outputSchema: z.object({
    analytics: z.array(z.object({
      product_service_id: z.string().uuid(),
      product_service_name: z.string(),
      product_service_type: z.string(),
      sku: z.string().nullable(),
      current_qty: z.number(),
      total_revenue: z.number(),
      total_cost: z.number(),
      total_profit: z.number(),
      profit_margin_percentage: z.number(),
      avg_sale_price: z.number(),
      total_sales_transactions: z.number(),
      total_quantity_sold: z.number(),
      last_sale_date: z.string().nullable(),
      stock_status: z.string().optional(),
    })),
    summary: z.object({
      total_products: z.number(),
      total_services: z.number(),
      total_revenue: z.number(),
      total_profit: z.number(),
      average_margin: z.number(),
    }),
  }),
  execute: async ({ context }) => {
    // Build the query for performance view
    let query = supabase
      .from('product_service_performance')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.productServiceId) {
      query = query.eq('product_service_id', context.productServiceId);
    }

    if (context.type) {
      query = query.eq('product_service_type', context.type);
    }

    // Apply sorting
    const sortColumn = context.sortBy === 'revenue' ? 'total_revenue' :
                      context.sortBy === 'profit' ? 'total_profit' :
                      context.sortBy === 'quantity' ? 'sale_qty' :
                      'profit_margin_percentage';

    query = query.order(sortColumn, { ascending: false }).limit(context.limit);

    const { data: performanceData, error: performanceError } = await query;

    if (performanceError) {
      throw new Error(`Failed to get analytics: ${performanceError.message}`);
    }

    // Get sales data for additional metrics
    const { data: salesData, error: salesError } = await supabase
      .from('sales_by_product_service')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (salesError) {
      throw new Error(`Failed to get sales data: ${salesError.message}`);
    }

    // Combine performance and sales data
    const analytics = performanceData.map(perf => {
      const sales = salesData.find(s => s.product_service_id === perf.product_service_id);
      return {
        ...perf,
        total_sales_transactions: sales?.total_sales_transactions || 0,
        total_quantity_sold: sales?.total_quantity_sold || 0,
        avg_sale_price: sales?.average_sale_price || 0,
        stock_status: perf.product_service_type === 'product' ?
          (perf.current_qty <= 0 ? 'Out of Stock' :
           perf.current_qty <= 5 ? 'Low Stock' : 'In Stock') : 'Service'
      };
    });

    // Calculate summary statistics
    const summary = {
      total_products: analytics.filter(a => a.product_service_type === 'product').length,
      total_services: analytics.filter(a => a.product_service_type === 'service').length,
      total_revenue: analytics.reduce((sum, a) => sum + (a.total_revenue || 0), 0),
      total_profit: analytics.reduce((sum, a) => sum + (a.total_profit || 0), 0),
      average_margin: analytics.length > 0 ? 
        analytics.reduce((sum, a) => sum + (a.profit_margin_percentage || 0), 0) / analytics.length : 0
    };

    return { analytics, summary };
  },
});

// Tool to update product/service
export const updateProductOrService = createTool({
  id: 'Update Product or Service',
  description: 'Update an existing product or service. You can modify pricing, accounts, descriptions, and other details.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the product/service'),
    userId: z.string().uuid().describe('User ID for updated_by field'),
    productServiceId: z.string().uuid().describe('Product/service ID to update'),
    name: z.string().optional().describe('New name'),
    description: z.string().optional().describe('New description'),
    sku: z.string().optional().describe('New SKU'),
    salesPrice: z.number().optional().describe('New sales price'),
    purchasePrice: z.number().optional().describe('New purchase price'),
    revenueAccountId: z.string().uuid().optional().describe('New revenue account ID'),
    expenseAccountId: z.string().uuid().optional().describe('New expense account ID'),
    isActive: z.boolean().optional().describe('New active status'),
  }),
  outputSchema: z.object({
    productService: productServiceSchema,
    message: z.string(),
  }),
  execute: async ({ context }) => {
    // Check if product/service exists
    const { data: existing, error: existingError } = await supabase
      .from('products_and_services')
      .select('*')
      .eq('product_service_id', context.productServiceId)
      .eq('organization_id', context.organizationId)
      .single();

    if (existingError || !existing) {
      throw new Error(`Product/service with ID ${context.productServiceId} not found`);
    }

    // Build update data
    const updateData: any = {
      updated_by: context.userId,
    };
    if (context.name !== undefined) updateData.product_service_name = context.name;
    if (context.description !== undefined) updateData.description = context.description;
    if (context.sku !== undefined) updateData.sku = context.sku;
    if (context.salesPrice !== undefined) updateData.sales_price = context.salesPrice;
    if (context.purchasePrice !== undefined) updateData.purchase_price = context.purchasePrice;
    if (context.revenueAccountId !== undefined) updateData.revenue_account_id = context.revenueAccountId;
    if (context.expenseAccountId !== undefined) updateData.expense_account_id = context.expenseAccountId;
    if (context.isActive !== undefined) updateData.is_active = context.isActive;

    const { data, error } = await supabase
      .from('products_and_services')
      .update(updateData)
      .eq('product_service_id', context.productServiceId)
      .eq('organization_id', context.organizationId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update ${existing.product_service_type}: ${error.message}`);
    }

    return {
      productService: data,
      message: `Successfully updated ${existing.product_service_type} "${existing.product_service_name}"`
    };
  },
});

// Tool to search products and services by name
export const searchProductsAndServices = createTool({
  id: 'Search Products and Services',
  description: 'Search for products and services by name when you need to find specific items for transactions or when the user asks for items containing certain text. Use this when you know part of a product/service name but need to find the exact item.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter products and services'),
    nameContains: z.string().describe('Text to search for in product/service names'),
    type: productServiceTypeEnum.optional().describe('Filter by type (product or service)'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    limit: z.number().min(1).max(100).default(10).describe('Maximum number of results to return'),
  }),
  outputSchema: z.object({
    productsAndServices: z.array(productServiceSchema),
    message: z.string(),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('products_and_services')
      .select('*')
      .ilike('product_service_name', `%${context.nameContains}%`)
      .eq('organization_id', context.organizationId);

    if (context.type) {
      query = query.eq('product_service_type', context.type);
    }

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query
      .order('product_service_name')
      .limit(context.limit);

    if (error) {
      throw new Error(`Failed to search for products and services: ${error.message}`);
    }

    const message = data.length === 0
      ? `No ${context.type || 'products or services'} found containing "${context.nameContains}"`
      : `Found ${data.length} ${context.type || 'products/services'} containing "${context.nameContains}"`;

    return {
      productsAndServices: data,
      message
    };
  },
});

// Tool to get inventory status
export const getInventoryStatus = createTool({
  id: 'Get Inventory Status',
  description: 'Get current inventory status for products including stock levels, values, and alerts. Use this to check what products are in stock, low stock, or out of stock.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter inventory'),
    productServiceId: z.string().uuid().optional().describe('Specific product ID to check'),
    stockStatus: z.enum(['In Stock', 'Low Stock', 'Out of Stock', 'Service']).optional().describe('Filter by stock status'),
    includeServices: z.boolean().optional().default(false).describe('Include services in the results'),
  }),
  outputSchema: z.object({
    inventory: z.array(z.object({
      product_service_id: z.string().uuid(),
      product_service_name: z.string(),
      product_service_type: z.string(),
      sku: z.string().nullable(),
      current_qty: z.number(),
      sales_price: z.number().nullable(),
      purchase_price: z.number().nullable(),
      inventory_value: z.number(),
      stock_status: z.string(),
    })),
    summary: z.object({
      total_products: z.number(),
      in_stock: z.number(),
      low_stock: z.number(),
      out_of_stock: z.number(),
      total_inventory_value: z.number(),
    }),
    message: z.string(),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('inventory_status')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.productServiceId) {
      query = query.eq('product_service_id', context.productServiceId);
    }

    if (context.stockStatus) {
      query = query.eq('stock_status', context.stockStatus);
    }

    if (!context.includeServices) {
      query = query.eq('product_service_type', 'product');
    }

    const { data, error } = await query.order('stock_status').order('product_service_name');

    if (error) {
      throw new Error(`Failed to get inventory status: ${error.message}`);
    }

    // Calculate summary
    const products = data.filter(item => item.product_service_type === 'product');
    const summary = {
      total_products: products.length,
      in_stock: products.filter(p => p.stock_status === 'In Stock').length,
      low_stock: products.filter(p => p.stock_status === 'Low Stock').length,
      out_of_stock: products.filter(p => p.stock_status === 'Out of Stock').length,
      total_inventory_value: products.reduce((sum, p) => sum + (p.inventory_value || 0), 0),
    };

    const message = context.productServiceId
      ? `Inventory status for specific ${data[0]?.product_service_type || 'item'}`
      : `Inventory overview: ${summary.in_stock} in stock, ${summary.low_stock} low stock, ${summary.out_of_stock} out of stock`;

    return {
      inventory: data,
      summary,
      message
    };
  },
});
