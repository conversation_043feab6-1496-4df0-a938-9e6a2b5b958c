import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define class schema for reuse
const classSchema = z.object({
  class_id: z.string().uuid(),
  class_name: z.string(),
  description: z.string().nullable(),
  parent_id: z.string().uuid().nullable(),
  is_active: z.boolean(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Define Class interface for type safety
interface Class {
  class_id?: string;
  class_name?: string;
  description?: string | null;
  parent_id?: string | null;
  is_active?: boolean;
  organization_id?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
}

// Tool to get classes
export const getClasses = createTool({
  id: 'Get Classes',
  description: 'Retrieve classes for tracking business segments, departments, locations, or projects. Use this when you need to display available classes for transaction categorization or when the user asks about business segments. Classes help organize transactions for better reporting.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter classes'),
    classId: z.string().uuid().optional().describe('Specific class ID to retrieve'),
    parentId: z.string().uuid().optional().describe('Filter by parent class ID'),
    isActive: z.boolean().optional().describe('Filter by active status'),
  }),
  outputSchema: z.object({
    classes: z.array(classSchema),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('classes')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.classId) {
      query = query.eq('class_id', context.classId);
    }

    if (context.parentId) {
      query = query.eq('parent_id', context.parentId);
    } else if (context.parentId === null) {
      // If explicitly looking for top-level classes
      query = query.is('parent_id', null);
    }

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query.order('class_name');

    if (error) {
      throw new Error(`Failed to get classes: ${error.message}`);
    }

    return { classes: data };
  },
});

// Tool to create a class
export const createClass = createTool({
  id: 'Create Class',
  description: 'Create a new class for tracking business segments, departments, locations, or projects. Use this when the user wants to add a new way to categorize transactions for better reporting and analysis. Classes can be hierarchical for detailed organization.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the class'),
    userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
    name: z.string().describe('Class name'),
    description: z.string().optional().describe('Class description'),
    parentId: z.string().uuid().optional().describe('Parent class ID for hierarchical classes'),
    isActive: z.boolean().optional().default(true).describe('Whether the class is active'),
  }),
  outputSchema: z.object({
    class: classSchema,
  }),
  execute: async ({ context }) => {
    // Check if parent class exists if provided
    if (context.parentId) {
      const { data: parentClass, error: parentError } = await supabase
        .from('classes')
        .select('class_id')
        .eq('class_id', context.parentId)
        .eq('organization_id', context.organizationId)
        .single();

      if (parentError || !parentClass) {
        throw new Error(`Parent class with ID ${context.parentId} not found`);
      }
    }

    // Create the new class
    const { data, error } = await supabase
      .from('classes')
      .insert({
        class_name: context.name,
        description: context.description || null,
        parent_id: context.parentId || null,
        is_active: context.isActive,
        organization_id: context.organizationId,
        created_by: context.userId,
        updated_by: context.userId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create class: ${error.message}`);
    }

    return { class: data };
  },
});

// Tool to update a class
export const updateClass = createTool({
  id: 'Update Class',
  description: 'Update an existing class used for tracking business segments. Use this when the user wants to modify class details like name, description, hierarchy, or active status. Be careful when changing hierarchy as this affects transaction categorization.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the class'),
    userId: z.string().uuid().describe('User ID for updated_by field'),
    classId: z.string().uuid().describe('Class ID to update'),
    name: z.string().optional().describe('Updated class name'),
    description: z.string().optional().describe('Updated class description'),
    parentId: z.string().uuid().optional().describe('Updated parent class ID'),
    isActive: z.boolean().optional().describe('Updated active status'),
  }),
  outputSchema: z.object({
    class: classSchema,
  }),
  execute: async ({ context }) => {

    // Check if class exists
    const { data: existingClass, error: existingError } = await supabase
      .from('classes')
      .select('*')
      .eq('class_id', context.classId)
      .eq('organization_id', context.organizationId)
      .single();

    if (existingError || !existingClass) {
      throw new Error(`Class with ID ${context.classId} not found`);
    }

    // Check if parent class exists if provided
    if (context.parentId) {
      // Prevent circular references
      if (context.parentId === context.classId) {
        throw new Error('A class cannot be its own parent');
      }

      const { data: parentClass, error: parentError } = await supabase
        .from('classes')
        .select('class_id')
        .eq('class_id', context.parentId)
        .eq('organization_id', context.organizationId)
        .single();

      if (parentError || !parentClass) {
        throw new Error(`Parent class with ID ${context.parentId} not found`);
      }
    }

    // Update the class
    const updateData: Partial<Class> = {};
    if (context.name !== undefined) updateData.class_name = context.name;
    if (context.description !== undefined) updateData.description = context.description;
    if (context.parentId !== undefined) updateData.parent_id = context.parentId;
    if (context.isActive !== undefined) updateData.is_active = context.isActive;

    // Add updated_by field
    updateData.updated_by = context.userId;

    const { data, error } = await supabase
      .from('classes')
      .update(updateData)
      .eq('class_id', context.classId)
      .eq('organization_id', context.organizationId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update class: ${error.message}`);
    }

    return { class: data };
  },
});

// Tool to search for classes by name
export const searchClassesByName = createTool({
  id: 'Search Classes By Name',
  description: 'Search for classes by name when you need to find specific classes for transaction categorization or when the user asks for classes containing certain text. Use this when you know part of a class name but need to find the exact class.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter classes'),
    nameContains: z.string().describe('Text to search for in class names'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    limit: z.number().min(1).max(100).default(10).describe('Maximum number of classes to return'),
  }),
  outputSchema: z.object({
    classes: z.array(classSchema),
  }),
  execute: async ({ context }) => {
    let query = supabase
      .from('classes')
      .select('*')
      .ilike('class_name', `%${context.nameContains}%`)
      .eq('organization_id', context.organizationId);

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data, error } = await query
      .order('class_name')
      .limit(context.limit);

    if (error) {
      throw new Error(`Failed to search for classes: ${error.message}`);
    }

    return { classes: data };
  },
});

// Define hierarchical class schema
const hierarchicalClassSchema: z.ZodType<any> = z.lazy(() =>
  classSchema.extend({
    children: z.array(hierarchicalClassSchema),
  })
);

// Tool to get class hierarchy
export const getClassHierarchy = createTool({
  id: 'Get Class Hierarchy',
  description: 'Retrieve the full hierarchy of classes organized in a tree structure. Use this when you need to display the complete class organization or when the user wants to understand the business segment structure. This shows parent-child relationships between classes.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter classes'),
    rootClassId: z.string().uuid().optional().describe('Optional root class ID to start the hierarchy from'),
    isActive: z.boolean().optional().describe('Filter by active status'),
  }),
  outputSchema: z.object({
    hierarchy: z.array(hierarchicalClassSchema),
  }),
  execute: async ({ context }) => {
    // First, get all classes for the organization
    let query = supabase
      .from('classes')
      .select('*')
      .eq('organization_id', context.organizationId);

    if (context.isActive !== undefined) {
      query = query.eq('is_active', context.isActive);
    }

    const { data: allClasses, error } = await query.order('class_name');

    if (error) {
      throw new Error(`Failed to get classes: ${error.message}`);
    }

    // Function to build a hierarchical tree from flat data
    const buildHierarchy = (items: any[], parentId: string | null = null): any[] => {
      return items
        .filter(item => item.parent_id === parentId)
        .map(item => ({
          ...item,
          children: buildHierarchy(items, item.class_id)
        }));
    };

    // If a root class ID is provided, verify it exists
    if (context.rootClassId) {
      const rootClass = allClasses.find(c => c.class_id === context.rootClassId);
      if (!rootClass) {
        throw new Error(`Root class with ID ${context.rootClassId} not found`);
      }

      // Return hierarchy starting from the specified root
      return {
        hierarchy: [
          {
            ...rootClass,
            children: buildHierarchy(allClasses, context.rootClassId)
          }
        ]
      };
    }

    // Otherwise, return the full hierarchy starting from top-level classes
    return {
      hierarchy: buildHierarchy(allClasses)
    };
  },
});
