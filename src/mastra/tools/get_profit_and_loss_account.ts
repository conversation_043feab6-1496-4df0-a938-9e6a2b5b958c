import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define schemas for P&L report
const accountItemSchema = z.object({
  account_id: z.string().uuid(),
  code: z.string(),
  name: z.string(),
  type: z.enum(['revenue', 'expense']),
  currentAmount: z.number(),
  previousAmount: z.number().nullable(),
  percentChange: z.number().nullable(),
});

const categoryItemSchema = z.object({
  category: z.string(),
  isCategory: z.boolean(),
  currentAmount: z.number(),
  previousAmount: z.number().nullable(),
  percentChange: z.number().nullable(),
  items: z.array(accountItemSchema),
});

// Tool to generate a profit and loss (income statement) report
export const getProfitAndLossAccount = createTool({
  id: 'Get Profit and Loss Account',
  description: 'Generate a profit and loss (income statement) report showing revenue, expenses, and net income for a specified period. Use this for financial reporting, performance analysis, or when the user needs to see business profitability. The P&L statement is one of the three core financial statements that shows the company\'s financial performance over time.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID for the profit and loss report'),
    startDate: z.string().describe('Start date for the report period (YYYY-MM-DD)'),
    endDate: z.string().describe('End date for the report period (YYYY-MM-DD)'),
    compareWithPreviousPeriod: z.boolean().default(false).describe('Include comparison with previous period'),
    groupByCategory: z.boolean().default(true).describe('Group accounts by category (e.g., operating revenue, operating expenses)'),
  }),
  outputSchema: z.object({
    reportTitle: z.string(),
    period: z.object({
      startDate: z.string(),
      endDate: z.string(),
    }),
    previousPeriod: z.object({
      startDate: z.string(),
      endDate: z.string(),
    }).nullable(),
    revenue: z.array(z.union([accountItemSchema, categoryItemSchema])),
    expenses: z.array(z.union([accountItemSchema, categoryItemSchema])),
    summary: z.object({
      totalRevenue: z.number(),
      totalExpenses: z.number(),
      netIncome: z.number(),
      previousTotalRevenue: z.number().nullable(),
      previousTotalExpenses: z.number().nullable(),
      previousNetIncome: z.number().nullable(),
      revenuePercentChange: z.number().nullable(),
      expensesPercentChange: z.number().nullable(),
      netIncomePercentChange: z.number().nullable(),
    }),
    message: z.string().describe('Information about the P&L report and business performance'),
  }),
  execute: async ({ context }) => {
    const organizationId = context.organizationId;
    // Calculate the length of the current period in days
    const currentStartDate = new Date(context.startDate);
    const currentEndDate = new Date(context.endDate);
    const currentPeriodDays = Math.ceil((currentEndDate.getTime() - currentStartDate.getTime()) / (1000 * 60 * 60 * 24));

    // Calculate previous period dates if comparison is requested
    let previousStartDate, previousEndDate;
    if (context.compareWithPreviousPeriod) {
      previousEndDate = new Date(currentStartDate);
      previousEndDate.setDate(previousEndDate.getDate() - 1);
      previousStartDate = new Date(previousEndDate);
      previousStartDate.setDate(previousStartDate.getDate() - currentPeriodDays);
    }

    // Get all revenue and expense accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('accounts')
      .select('*')
      .in('type', ['revenue', 'expense'])
      .eq('is_active', true)
      .eq('organization_id', organizationId)
      .order('code');

    if (accountsError) {
      throw new Error(`Failed to get accounts: ${accountsError.message}`);
    }

    // Separate revenue and expense accounts
    const revenueAccounts = accounts.filter(account => account.type === 'revenue');
    const expenseAccounts = accounts.filter(account => account.type === 'expense');

    // Process revenue accounts
    const revenueItems = await processAccounts(
      revenueAccounts,
      context.startDate,
      context.endDate,
      previousStartDate?.toISOString().split('T')[0],
      previousEndDate?.toISOString().split('T')[0],
      context.compareWithPreviousPeriod,
      organizationId
    );

    // Process expense accounts
    const expenseItems = await processAccounts(
      expenseAccounts,
      context.startDate,
      context.endDate,
      previousStartDate?.toISOString().split('T')[0],
      previousEndDate?.toISOString().split('T')[0],
      context.compareWithPreviousPeriod,
      organizationId
    );

    // Calculate totals
    const totalRevenue = revenueItems.reduce((sum, item) => sum + item.currentAmount, 0);
    const totalExpenses = expenseItems.reduce((sum, item) => sum + item.currentAmount, 0);
    const netIncome = totalRevenue - totalExpenses;

    // Calculate previous period totals if comparison is requested
    let previousTotalRevenue = 0;
    let previousTotalExpenses = 0;
    let previousNetIncome = 0;

    if (context.compareWithPreviousPeriod) {
      previousTotalRevenue = revenueItems.reduce((sum, item) => sum + (item.previousAmount || 0), 0);
      previousTotalExpenses = expenseItems.reduce((sum, item) => sum + (item.previousAmount || 0), 0);
      previousNetIncome = previousTotalRevenue - previousTotalExpenses;
    }

    // Group accounts by category if requested
    let groupedRevenueItems: any = revenueItems;
    let groupedExpenseItems: any = expenseItems;

    if (context.groupByCategory) {
      // Group revenue items
      const revenueCategories = {
        'Operating Revenue': revenueItems.filter(item => item.code.startsWith('4')),
        'Other Revenue': revenueItems.filter(item => !item.code.startsWith('4')),
      };

      // Group expense items
      const expenseCategories = {
        'Cost of Goods Sold': expenseItems.filter(item => item.code.startsWith('5')),
        'Operating Expenses': expenseItems.filter(item => item.code.startsWith('6')),
        'Other Expenses': expenseItems.filter(item => !item.code.startsWith('5') && !item.code.startsWith('6')),
      };

      // Convert grouped items to a format suitable for the response
      groupedRevenueItems = Object.entries(revenueCategories).map(([category, items]) => {
        if (items.length === 0) return null;

        const totalCurrentAmount = items.reduce((sum, item) => sum + item.currentAmount, 0);
        const totalPreviousAmount = context.compareWithPreviousPeriod
          ? items.reduce((sum, item) => sum + (item.previousAmount || 0), 0)
          : null;

        return {
          category,
          isCategory: true,
          currentAmount: totalCurrentAmount,
          previousAmount: totalPreviousAmount,
          percentChange: totalPreviousAmount ? ((totalCurrentAmount - totalPreviousAmount) / totalPreviousAmount) * 100 : null,
          items
        };
      }).filter(Boolean) as any;

      groupedExpenseItems = Object.entries(expenseCategories).map(([category, items]) => {
        if (items.length === 0) return null;

        const totalCurrentAmount = items.reduce((sum, item) => sum + item.currentAmount, 0);
        const totalPreviousAmount = context.compareWithPreviousPeriod
          ? items.reduce((sum, item) => sum + (item.previousAmount || 0), 0)
          : null;

        return {
          category,
          isCategory: true,
          currentAmount: totalCurrentAmount,
          previousAmount: totalPreviousAmount,
          percentChange: totalPreviousAmount ? ((totalCurrentAmount - totalPreviousAmount) / totalPreviousAmount) * 100 : null,
          items
        };
      }).filter(Boolean) as any;
    }

    // Generate performance message
    const profitMargin = totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0;
    const performanceIndicator = netIncome > 0 ? 'profitable' : netIncome < 0 ? 'showing a loss' : 'breaking even';

    const message = `Profit & Loss Statement for ${context.startDate} to ${context.endDate}: Total Revenue: $${totalRevenue.toFixed(2)}, Total Expenses: $${totalExpenses.toFixed(2)}, Net Income: $${netIncome.toFixed(2)} (${profitMargin.toFixed(1)}% margin). The business is ${performanceIndicator} for this period.${context.compareWithPreviousPeriod ? ` Compared to the previous period, revenue changed by ${((totalRevenue - previousTotalRevenue) / previousTotalRevenue * 100).toFixed(1)}% and net income changed by ${previousNetIncome !== 0 ? ((netIncome - previousNetIncome) / previousNetIncome * 100).toFixed(1) : 'N/A'}%.` : ''}`;

    // Prepare the response
    const response = {
      reportTitle: 'Profit and Loss Account',
      period: {
        startDate: context.startDate,
        endDate: context.endDate,
      },
      previousPeriod: context.compareWithPreviousPeriod && previousStartDate && previousEndDate ? {
        startDate: previousStartDate.toISOString().split('T')[0],
        endDate: previousEndDate.toISOString().split('T')[0],
      } : null,
      revenue: context.groupByCategory ? groupedRevenueItems : revenueItems,
      expenses: context.groupByCategory ? groupedExpenseItems : expenseItems,
      summary: {
        totalRevenue,
        totalExpenses,
        netIncome,
        previousTotalRevenue: context.compareWithPreviousPeriod ? previousTotalRevenue : null,
        previousTotalExpenses: context.compareWithPreviousPeriod ? previousTotalExpenses : null,
        previousNetIncome: context.compareWithPreviousPeriod ? previousNetIncome : null,
        revenuePercentChange: context.compareWithPreviousPeriod && previousTotalRevenue
          ? ((totalRevenue - previousTotalRevenue) / previousTotalRevenue) * 100
          : null,
        expensesPercentChange: context.compareWithPreviousPeriod && previousTotalExpenses
          ? ((totalExpenses - previousTotalExpenses) / previousTotalExpenses) * 100
          : null,
        netIncomePercentChange: context.compareWithPreviousPeriod && previousNetIncome
          ? ((netIncome - previousNetIncome) / previousNetIncome) * 100
          : null,
      },
      message
    };

    return response;
  },
});

// Helper function to process accounts and calculate balances
async function processAccounts(
  accounts: any[],
  startDate: string,
  endDate: string,
  previousStartDate: string | undefined,
  previousEndDate: string | undefined,
  compareWithPreviousPeriod: boolean,
  organizationId: string
) {
  const results = [];

  for (const account of accounts) {
    // Get current period transaction lines for this account
    const { data: currentLines, error: currentLinesError } = await supabase
      .from('transaction_lines')
      .select(`
        *,
        transactions:transaction_id (transaction_date, organization_id)
      `)
      .eq('account_id', account.account_id)
      .eq('transactions.organization_id', organizationId)
      .gte('transactions.transaction_date', startDate)
      .lte('transactions.transaction_date', endDate);

    if (currentLinesError) {
      throw new Error(`Failed to get transaction lines for account ${account.code}: ${currentLinesError.message}`);
    }

    // Calculate current period balance
    let currentAmount = 0;
    for (const line of currentLines) {
      if (account.type === 'revenue') {
        currentAmount += line.credit_amount - line.debit_amount;
      } else if (account.type === 'expense') {
        currentAmount += line.debit_amount - line.credit_amount;
      }
    }

    // Skip accounts with zero balance
    if (currentAmount === 0 && !compareWithPreviousPeriod) {
      continue;
    }

    // Get previous period transaction lines if comparison is requested
    let previousAmount = null;
    if (compareWithPreviousPeriod) {
      const { data: previousLines, error: previousLinesError } = await supabase
        .from('transaction_lines')
        .select(`
          *,
          transactions:transaction_id (transaction_date, organization_id)
        `)
        .eq('account_id', account.account_id)
        .eq('transactions.organization_id', organizationId)
        .gte('transactions.transaction_date', previousStartDate)
        .lte('transactions.transaction_date', previousEndDate);

      if (previousLinesError) {
        throw new Error(`Failed to get previous period transaction lines for account ${account.code}: ${previousLinesError.message}`);
      }

      // Calculate previous period balance
      previousAmount = 0;
      for (const line of previousLines) {
        if (account.type === 'revenue') {
          previousAmount += line.credit_amount - line.debit_amount;
        } else if (account.type === 'expense') {
          previousAmount += line.debit_amount - line.credit_amount;
        }
      }

      // Skip accounts with zero balance in both periods
      if (currentAmount === 0 && previousAmount === 0) {
        continue;
      }
    }

    // Calculate percent change if previous amount exists and is not zero
    const percentChange = previousAmount && previousAmount !== 0
      ? ((currentAmount - previousAmount) / previousAmount) * 100
      : null;

    results.push({
      account_id: account.account_id,
      code: account.code,
      name: account.account_name,
      type: account.type,
      currentAmount,
      previousAmount,
      percentChange
    });
  }

  return results;
}
