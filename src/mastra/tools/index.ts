// Import all tools directly from their files
import { getAccounts, createAccount, updateAccount, getAccountByCode, searchAccountsByName } from './accounts.js';
import { getClasses, createClass, updateClass, searchClassesByName } from './classes.js';
import { getProjects, createProject, updateProject, searchProjects, getHierarchicalProjects } from './projects.js';
import { getCustomers, createCustomer, updateCustomer, searchCustomersByName } from './customers.js';
import { getProductsAndServices, createProductOrService, updateProductOrService, searchProductsAndServices, getProductServiceAnalytics, getInventoryStatus } from './products_and_services.js';
import { getTaxRates, createTaxRate, updateTaxRate, searchTaxRatesByName } from './tax_rates.js';
import { getTaxGroups, createTaxGroup, updateTaxGroup, searchTaxGroupsByName, getTaxGroupWithRates } from './tax_groups.js';
import { getTaxGroupItems, addTaxRateToGroup, removeTaxRateFromGroup, getTaxRatesNotInGroup } from './tax_group_items.js';
// recordTransaction tool updated with manual accounting lines architecture
import { recordTransaction } from './record_transactions.js';
import { getTransactions } from './get_transactions.js';
import { recordCustomerPayment } from './customer_payment.js';
import { recordVendorPayment } from './vendor_payment.js';
import { getOutstandingInvoices } from './get_outstanding_invoices.js';
import { getOutstandingBills } from './get_outstanding_bills.js';
import { getVendors, createVendor, updateVendor, searchVendorsByName } from './vendors.js';
import { getAccountBalance } from './get_account_balance.js';

import { getAccountBalanceOptimized, getMultipleAccountBalances, checkRealTimeDataStatus } from './get_account_balance_optimized.js';
import { getBalanceSheet } from './get_balance_sheet.js';
import { getProfitAndLossAccount } from './get_profit_and_loss_account.js';
import { getTrialBalance } from './get_trial_balance.js';
import { getTrialBalanceOptimized, getTrialBalanceSummary, compareTrialBalances } from './get_trial_balance_optimized.js';
import { createNewAccountWorkflowTool } from './create_new_account_workflow_tool.js';
import { salesInvoiceWorkflowTool } from './sales_invoice_workflow_tool.js';
import { createSalesInvoice } from './sales_invoice.js';
import { createSalesReceipt } from './sales_receipt.js';
import { createSalesReturn } from './sales_return.js';
import { createPurchaseReturn } from './purchase_return.js';
import { recordCustomerCashRefund } from './customer_cash_refund.js';
import { recordVendorCashRefund } from './vendor_cash_refund.js';
import { createBill } from './bills.js';
import { createExpense } from './expense.js';
import { createJournalEntry } from './journal_entry.js';



// Export all tools as a single object
export const tools = {
  // All individual tools
  getAccounts,
  createAccount,
  updateAccount,
  getAccountByCode,
  searchAccountsByName,

  getClasses,
  createClass,
  updateClass,
  searchClassesByName,

  getProjects,
  createProject,
  updateProject,
  searchProjects,
  getHierarchicalProjects,

  getCustomers,
  createCustomer,
  updateCustomer,
  searchCustomersByName,

  getProductsAndServices,
  createProductOrService,
  updateProductOrService,
  searchProductsAndServices,
  getProductServiceAnalytics,
  getInventoryStatus,

  getTaxRates,
  createTaxRate,
  updateTaxRate,
  searchTaxRatesByName,

  getTaxGroups,
  createTaxGroup,
  updateTaxGroup,
  searchTaxGroupsByName,
  getTaxGroupWithRates,

  getTaxGroupItems,
  addTaxRateToGroup,
  removeTaxRateFromGroup,
  getTaxRatesNotInGroup,

  recordTransaction, // Re-registered with manual accounting lines architecture
  getTransactions,
  recordCustomerPayment,
  recordVendorPayment,
  getOutstandingInvoices,
  getOutstandingBills,

  getVendors,
  createVendor,
  updateVendor,
  searchVendorsByName,

  getAccountBalance,
  getAccountBalanceOptimized,
  getMultipleAccountBalances,
  checkRealTimeDataStatus,
  getBalanceSheet,
  getProfitAndLossAccount,
  getTrialBalance,
  getTrialBalanceOptimized,
  getTrialBalanceSummary,
  compareTrialBalances,

  // Workflow tools
  createNewAccountWorkflowTool,
  salesInvoiceWorkflowTool,

  // Sales Invoice tools
  createSalesInvoice,

  // Sales Receipt tools
  createSalesReceipt,

  // Sales Return tools
  createSalesReturn,

  // Purchase Return tools
  createPurchaseReturn,

  // Customer Cash Refund tools
  recordCustomerCashRefund,

  // Vendor Cash Refund tools
  recordVendorCashRefund,

  // Bill tools
  createBill,

  // Expense tools
  createExpense,

  // Journal Entry tools
  createJournalEntry,
};
