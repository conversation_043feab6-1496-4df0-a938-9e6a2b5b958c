import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { supabase } from '../../utils/supabase.js';

// Define outstanding invoice schema
const outstandingInvoiceSchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.string(),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  description: z.string().nullable(),
  total_amount: z.number(),
  due_date: z.string().nullable(),
  total_paid: z.number(),
  outstanding_amount: z.number(),
  is_overdue: z.boolean(),
});

// Tool to get outstanding invoices for a customer
export const getOutstandingInvoices = createTool({
  id: 'Get Outstanding Invoices',
  description: 'Get list of unpaid or partially paid invoices for a customer in the current organization. Use this when you need to manage accounts receivable, check what invoices need to be collected, prioritize customer follow-ups by due dates, or when the user asks about outstanding customer invoices. Essential for cash flow management, customer payment tracking, and maintaining healthy receivables. This tool helps identify which invoices are overdue and require collection efforts.',
  inputSchema: z.object({
    organizationId: z.string().uuid().describe('Organization ID to filter invoices'),
    customerId: z.string().uuid().describe('Customer ID to get outstanding invoices for'),
  }),
  outputSchema: z.object({
    success: z.boolean().describe('Whether the operation was successful'),
    outstandingInvoices: z.array(outstandingInvoiceSchema),
    totalOutstanding: z.number().describe('Total amount of all outstanding invoices'),
    overdueCount: z.number().describe('Number of overdue invoices'),
    totalOverdue: z.number().describe('Total amount of overdue invoices'),
    message: z.string().describe('Summary message about the outstanding invoices and collection status'),
  }),
  execute: async ({ context }) => {
    try {
      // Get all invoices for the customer
      const { data: invoices, error: invoicesError } = await supabase
        .from('transactions')
        .select(`
          transaction_id,
          transaction_type,
          transaction_date,
          reference_number,
          description,
          total_amount,
          due_date
        `)
        .eq('customer_id', context.customerId)
        .eq('organization_id', context.organizationId)
        .in('transaction_type', ['sales_invoice', 'sales_receipt'])
        .order('transaction_date', { ascending: false });

      if (invoicesError) {
        throw new Error(`Failed to get invoices: ${invoicesError.message}`);
      }

      if (!invoices || invoices.length === 0) {
        return {
          success: true,
          outstandingInvoices: [],
          totalOutstanding: 0,
          overdueCount: 0,
          totalOverdue: 0,
          message: 'No invoices found for this customer'
        };
      }

      // Get all payments for these invoices
      const invoiceIds = invoices.map(inv => inv.transaction_id);
      const { data: payments, error: paymentsError } = await supabase
        .from('invoice_payments')
        .select('transaction_id, amount')
        .in('transaction_id', invoiceIds);

      if (paymentsError) {
        throw new Error(`Failed to get payments: ${paymentsError.message}`);
      }

      // Calculate outstanding amounts
      const paymentsByInvoice = (payments || []).reduce((acc: any, payment: any) => {
        if (!acc[payment.transaction_id]) {
          acc[payment.transaction_id] = 0;
        }
        acc[payment.transaction_id] += payment.amount;
        return acc;
      }, {});

      const outstandingInvoices = invoices
        .map(invoice => {
          const totalPaid = paymentsByInvoice[invoice.transaction_id] || 0;
          const outstandingAmount = invoice.total_amount - totalPaid;

          return {
            ...invoice,
            total_paid: totalPaid,
            outstanding_amount: outstandingAmount,
            is_overdue: invoice.due_date && new Date(invoice.due_date) < new Date(),
          };
        })
        .filter(invoice => invoice.outstanding_amount > 0.001); // Only include invoices with outstanding amounts

      const totalOutstanding = outstandingInvoices.reduce((sum, inv) => sum + inv.outstanding_amount, 0);

      // Calculate overdue statistics
      const overdueInvoices = outstandingInvoices.filter(invoice => invoice.is_overdue);
      const overdueCount = overdueInvoices.length;
      const totalOverdue = overdueInvoices.reduce((sum, invoice) => sum + invoice.outstanding_amount, 0);

      // Generate comprehensive message
      let message = `Found ${outstandingInvoices.length} outstanding invoice(s) with total amount of $${totalOutstanding.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      if (overdueCount > 0) {
        message += `. ATTENTION: ${overdueCount} invoice(s) are overdue with total overdue amount of $${totalOverdue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} - these require collection follow-up`;
      } else if (outstandingInvoices.length > 0) {
        message += '. All invoices are current with no overdue amounts';
      }

      if (outstandingInvoices.length > 0) {
        const upcomingDue = outstandingInvoices.filter(invoice => invoice.due_date && new Date(invoice.due_date) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));
        if (upcomingDue.length > 0) {
          message += `. ${upcomingDue.length} invoice(s) are due within the next 7 days`;
        }
      }

      return {
        success: true,
        outstandingInvoices,
        totalOutstanding,
        overdueCount,
        totalOverdue,
        message
      };

    } catch (error: any) {
      return {
        success: false,
        outstandingInvoices: [],
        totalOutstanding: 0,
        overdueCount: 0,
        totalOverdue: 0,
        message: `Error retrieving outstanding invoices: ${error.message}`,
      };
    }
  },
});

