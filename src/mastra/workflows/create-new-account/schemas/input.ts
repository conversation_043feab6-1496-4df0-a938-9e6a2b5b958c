import { z } from 'zod';

// Account type enums
export const AccountTypeEnum = z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']);

// Account type detail enums based on account type
export const AccountTypeDetailEnum = z.enum([
  // Asset details
  'accounts_receivable', 'other_current_assets', 'bank', 'fixed_assets', 'other_assets',
  // Liability details
  'accounts_payable', 'credit_card', 'other_current_liabilities', 'long_term_liabilities',
  // Equity details
  'equity',
  // Revenue details
  'revenue', 'other_revenue',
  // Expense details
  'cost_of_goods_sold', 'expenditures', 'other_expenditure'
]);

// Main workflow input schema
export const WorkflowInputSchema = z.object({
  accountName: z.string().describe('The name of the account to search for or create'),
  accountCode: z.string().optional().describe('Optional account code (4-digit format)'),
  accountType: AccountTypeEnum.describe('The type of account'),
  accountTypeDetail: AccountTypeDetailEnum.describe('The specific classification within the account type'),
  description: z.string().optional().describe('Optional description for the account'),
  organizationId: z.string().uuid().describe('Organization ID for the account'),
  userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;
export type AccountType = z.infer<typeof AccountTypeEnum>;
export type AccountTypeDetail = z.infer<typeof AccountTypeDetailEnum>;
