import { z } from 'zod';

// Account schema for database records
export const AccountSchema = z.object({
  account_id: z.string().uuid(),
  account_name: z.string(),
  account_code: z.number(),
  account_type: z.string(),
  account_type_detail: z.string(),
  description: z.string().nullable(),
  is_active: z.boolean(),
});

// Main workflow output schema
export const WorkflowOutputSchema = z.object({
  action: z.enum(['created', 'exists', 'error']),
  message: z.string(),
  account: AccountSchema.optional(),
  existingAccounts: z.array(AccountSchema).optional(),
});

// Step 1 output schema (search existing accounts)
export const SearchExistingAccountsOutputSchema = z.object({
  existingAccounts: z.array(AccountSchema),
  accountExists: z.boolean(),
  searchSummary: z.string(),
  duplicateType: z.enum(['exact_name', 'exact_code', 'similar_name', 'none']).describe('Type of duplicate found'),
});

// Step 2 input schema (create or inform)
export const CreateOrInformInputSchema = z.object({
  accountExists: z.boolean(),
  existingAccounts: z.array(AccountSchema),
  searchSummary: z.string(),
  duplicateType: z.enum(['exact_name', 'exact_code', 'similar_name', 'none']),
  accountName: z.string(),
  accountCode: z.string().optional(),
  accountType: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense']),
  accountTypeDetail: z.enum([
    'accounts_receivable', 'other_current_assets', 'bank', 'fixed_assets', 'other_assets',
    'accounts_payable', 'credit_card', 'other_current_liabilities', 'long_term_liabilities',
    'equity',
    'revenue', 'other_revenue',
    'cost_of_goods_sold', 'expenditures', 'other_expenditure'
  ]),
  description: z.string().optional(),
  organizationId: z.string().uuid(),
  userId: z.string().uuid(),
});

export type Account = z.infer<typeof AccountSchema>;
export type WorkflowOutput = z.infer<typeof WorkflowOutputSchema>;
export type SearchExistingAccountsOutput = z.infer<typeof SearchExistingAccountsOutputSchema>;
export type CreateOrInformInput = z.infer<typeof CreateOrInformInputSchema>;
