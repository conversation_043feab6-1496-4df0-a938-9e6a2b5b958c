import { supabase } from '../../../../utils/supabase.js';
import type { AccountType } from '../schemas/index.js';

/**
 * Helper function for intelligent account code generation
 * Generates a 4-digit account code based on account type and existing codes
 */
export async function generateAccountCode(
  accountType: AccountType,
  organizationId: string
): Promise<number> {
  const typeCodeMap = {
    asset: '1',
    liability: '2',
    equity: '3',
    revenue: '4',
    expense: '5',
  };

  const baseCode = parseInt(typeCodeMap[accountType] + '000');

  try {
    // Find the next available code in this range with better pattern matching
    const { data: existingCodes, error: codeError } = await supabase
      .from('accounts')
      .select('account_code')
      .eq('organization_id', organizationId)
      .gte('account_code', baseCode)
      .lt('account_code', (parseInt(typeCodeMap[accountType]) + 1) * 1000)
      .order('account_code', { ascending: false })
      .limit(1);

    if (codeError) {
      throw new Error(`Failed to generate account code: ${codeError.message}`);
    }

    if (existingCodes && existingCodes.length > 0) {
      const lastCode = existingCodes[0].account_code;
      if (typeof lastCode === 'number') {
        return lastCode + 1;
      }
    }

    return baseCode;
  } catch (error) {
    // Fallback to base code if generation fails
    console.warn(`Account code generation failed, using base code: ${error}`);
    return baseCode;
  }
}
