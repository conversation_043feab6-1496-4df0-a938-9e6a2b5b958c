import { createWorkflow } from '@mastra/core/workflows';
import { WorkflowInputSchema, WorkflowOutputSchema } from './schemas/index.js';
import { searchExistingAccountsStep, createOrInformStep } from './steps/index.js';

/**
 * Create New Account Workflow
 * 
 * This workflow provides intelligent account creation with comprehensive duplicate detection:
 * 1. Searches for existing accounts by name and code to prevent duplicates
 * 2. Provides detailed duplicate analysis (exact name, exact code, similar name matches)
 * 3. Generates smart account codes based on account type if not provided
 * 4. Creates new accounts only when no conflicts exist
 * 5. Provides clear feedback about existing accounts when duplicates are found
 */
const workflow = createWorkflow({
  id: 'createNewAccountWorkflow',
  description: 'Intelligent account creation workflow with duplicate detection and smart code generation',
  inputSchema: WorkflowInputSchema,
  outputSchema: WorkflowOutputSchema,
  steps: [searchExistingAccountsStep, createOrInformStep],
});

// Configure workflow step connections and data mapping
workflow
  .then(searchExistingAccountsStep)
  .map({
    accountExists: {
      step: searchExistingAccountsStep,
      path: 'accountExists',
    },
    existingAccounts: {
      step: searchExistingAccountsStep,
      path: 'existingAccounts',
    },
    searchSummary: {
      step: searchExistingAccountsStep,
      path: 'searchSummary',
    },
    duplicateType: {
      step: searchExistingAccountsStep,
      path: 'duplicateType',
    },
    accountName: {
      initData: workflow,
      path: 'accountName',
    },
    accountCode: {
      initData: workflow,
      path: 'accountCode',
    },
    accountType: {
      initData: workflow,
      path: 'accountType',
    },
    accountTypeDetail: {
      initData: workflow,
      path: 'accountTypeDetail',
    },
    description: {
      initData: workflow,
      path: 'description',
    },
    organizationId: {
      initData: workflow,
      path: 'organizationId',
    },
    userId: {
      initData: workflow,
      path: 'userId',
    },
  })
  .then(createOrInformStep)
  .commit();

export const createNewAccountWorkflow = workflow;

// Export types and schemas for external use
export type { WorkflowInput, WorkflowOutput } from './schemas/index.js';
