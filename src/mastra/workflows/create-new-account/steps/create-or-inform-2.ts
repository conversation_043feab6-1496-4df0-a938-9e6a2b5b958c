import { createStep } from '@mastra/core/workflows';
import { supabase } from '../../../../utils/supabase.js';
import {
  CreateOrInformInputSchema,
  WorkflowOutputSchema
} from '../schemas/index.js';
import { generateAccountCode } from '../utils/index.js';

/**
 * Step 2: Create new account or inform about existing account with enhanced logic
 * This step either creates a new account if no duplicates exist, or informs about existing accounts
 */
export const createOrInformStep = createStep({
  id: 'createOrInform',
  description: 'Create a new account if no duplicates exist, or inform about existing accounts',
  inputSchema: CreateOrInformInputSchema,
  outputSchema: WorkflowOutputSchema,
  execute: async ({ inputData }) => {
    const {
      accountExists,
      existingAccounts,
      searchSummary,
      accountName,
      accountCode,
      accountType,
      accountTypeDetail,
      description,
      organizationId,
      userId
    } = inputData;

    // If account exists, inform user about existing accounts
    if (accountExists) {
      let message = `${searchSummary}\n\nExisting accounts found:\n`;

      existingAccounts.forEach((account, index) => {
        message += `${index + 1}. ${account.account_name} (Code: ${account.account_code}, Type: ${account.account_type})`;
        if (account.description) {
          message += ` - ${account.description}`;
        }
        message += '\n';
      });

      message += '\nPlease review these existing accounts before creating a new one. You may want to use one of these existing accounts instead.';

      return {
        action: 'exists' as const,
        message,
        existingAccounts,
      };
    }

    // If no existing account, create new one with enhanced code generation
    try {
      // Generate account code if not provided using improved logic
      let finalAccountCode: number;
      if (accountCode) {
        // Convert string account code to number
        finalAccountCode = parseInt(accountCode);
        if (isNaN(finalAccountCode)) {
          throw new Error(`Invalid account code: ${accountCode}. Account code must be a valid number.`);
        }
      } else {
        finalAccountCode = await generateAccountCode(accountType, organizationId);
      }

      // Create the account data manually since we don't have runtimeContext in workflows
      const createData = {
        account_name: accountName,
        account_code: finalAccountCode,
        account_type: accountType,
        account_type_detail: accountTypeDetail,
        description: description || null,
        is_active: true,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      };

      const { data: newAccount, error: createError } = await supabase
        .from('accounts')
        .insert(createData)
        .select()
        .single();

      if (createError) {
        throw new Error(`Failed to create account: ${createError.message}`);
      }

      const message = `Successfully created new account "${accountName}" with code "${finalAccountCode}" (Type: ${accountType}).`;

      return {
        action: 'created' as const,
        message,
        account: newAccount,
      };
    } catch (error) {
      return {
        action: 'error' as const,
        message: `Failed to create account: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  },
});
