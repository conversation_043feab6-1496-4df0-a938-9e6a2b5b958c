import { createStep } from '@mastra/core/workflows';
import { getOrganizationQuery } from '../../../../utils/supabase.js';
import {
  WorkflowInputSchema,
  SearchExistingAccountsOutputSchema
} from '../schemas/index.js';

/**
 * Step 1: Search for existing accounts with enhanced duplicate detection
 * This step searches for existing accounts by name and code to prevent duplicates
 */
export const searchExistingAccountsStep = createStep({
  id: 'searchExistingAccounts',
  description: 'Search for existing accounts by name and code to prevent duplicates',
  inputSchema: WorkflowInputSchema,
  outputSchema: SearchExistingAccountsOutputSchema,
  execute: async ({ inputData }) => {
    const { accountName, accountCode, organizationId } = inputData;

    try {
      // Search by name (case-insensitive)
      let nameQuery = getOrganizationQuery('accounts', organizationId)
        .ilike('account_name', `%${accountName}%`)
        .eq('is_active', true);

      const { data: nameMatches, error: nameError } = await nameQuery;

      if (nameError) {
        throw new Error(`Failed to search accounts by name: ${nameError.message}`);
      }

      let codeMatches: any[] = [];

      // If account code is provided, also search by code
      if (accountCode) {
        const numericAccountCode = parseInt(accountCode);
        if (!isNaN(numericAccountCode)) {
          let codeQuery = getOrganizationQuery('accounts', organizationId)
            .eq('account_code', numericAccountCode)
            .eq('is_active', true);

          const { data: codeData, error: codeError } = await codeQuery;

          if (codeError) {
            throw new Error(`Failed to search accounts by code: ${codeError.message}`);
          }

          codeMatches = codeData || [];
        }
      }

      // Combine results and remove duplicates
      const allMatches = [...(nameMatches || []), ...codeMatches];
      const uniqueMatches = allMatches.filter((account, index, self) =>
        index === self.findIndex(a => a.account_id === account.account_id)
      );

      const accountExists = uniqueMatches.length > 0;

      // Enhanced duplicate detection logic
      let searchSummary = '';
      let duplicateType: 'exact_name' | 'exact_code' | 'similar_name' | 'none' = 'none';

      if (accountExists) {
        const exactNameMatch = uniqueMatches.find(acc =>
          acc.account_name.toLowerCase() === accountName.toLowerCase()
        );
        const exactCodeMatch = accountCode ? uniqueMatches.find(acc => acc.account_code === parseInt(accountCode)) : null;

        if (exactNameMatch && exactCodeMatch && exactNameMatch.account_id === exactCodeMatch.account_id) {
          duplicateType = 'exact_name';
          searchSummary = `Found exact match for both name "${accountName}" and code "${accountCode}".`;
        } else if (exactNameMatch) {
          duplicateType = 'exact_name';
          searchSummary = `Found exact match for account name "${accountName}".`;
        } else if (exactCodeMatch) {
          duplicateType = 'exact_code';
          searchSummary = `Found exact match for account code "${accountCode}".`;
        } else {
          duplicateType = 'similar_name';
          searchSummary = `Found ${uniqueMatches.length} similar account(s) matching "${accountName}".`;
        }
      } else {
        searchSummary = `No existing accounts found matching "${accountName}"${accountCode ? ` or code "${accountCode}"` : ''}.`;
      }

      return {
        existingAccounts: uniqueMatches,
        accountExists,
        searchSummary,
        duplicateType,
      };
    } catch (error) {
      throw new Error(`Account search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});
