import { createWorkflow } from '@mastra/core/workflows';
import { WorkflowInputSchema, WorkflowOutputSchema } from './schemas/index.js';
import { 
  fetchMasterDataStep, 
  validateDataStep, 
  handleMissingDataStep, 
  recordInvoiceStep 
} from './steps/index.js';

/**
 * Sales Invoice Workflow
 * 
 * This workflow provides intelligent sales invoice creation with comprehensive data validation:
 * 1. Fetches all required master data (customer, accounts, products, tax rates) in parallel
 * 2. Validates data completeness and resolves names to IDs
 * 3. Handles missing data by creating items or suspending for user input
 * 4. Records the sales invoice transaction with all accounting lines
 * 
 * Benefits:
 * - Reduces token costs by batching data fetching
 * - Improves data integrity through validation
 * - <PERSON>les missing master data gracefully
 * - Supports suspend/resume for user interaction
 */
const workflow = createWorkflow({
  id: 'salesInvoiceWorkflow',
  description: 'Intelligent sales invoice creation workflow with master data validation and handling',
  inputSchema: WorkflowInputSchema,
  outputSchema: WorkflowOutputSchema,
  steps: [fetchMasterDataStep, validateDataStep, handleMissingDataStep, recordInvoiceStep],
});

// Configure workflow step connections and data mapping
workflow
  // Step 1: Fetch all master data in parallel
  .then(fetchMasterDataStep)

  // Step 2: Validate data completeness and resolve IDs
  .map({
    workflowInput: {
      initData: workflow,
      path: '.',
    },
    masterData: {
      step: fetchMasterDataStep,
      path: '.',
    },
  })
  .then(validateDataStep)

  // Step 3: Handle missing data (conditional execution)
  .map({
    workflowInput: {
      initData: workflow,
      path: '.',
    },
    validation: {
      step: validateDataStep,
      path: '.',
    },
  })
  .then(handleMissingDataStep)

  // Step 4: Record the invoice
  .map({
    workflowInput: {
      initData: workflow,
      path: '.',
    },
    masterData: {
      step: fetchMasterDataStep,
      path: '.',
    },
    validation: {
      step: validateDataStep,
      path: '.',
    },
    missingDataHandling: {
      step: handleMissingDataStep,
      path: '.',
    },
  })
  .then(recordInvoiceStep)
  .commit();

export const salesInvoiceWorkflow = workflow;

// Export types and schemas for external use
export type { WorkflowInput, WorkflowOutput } from './schemas/index.js';
