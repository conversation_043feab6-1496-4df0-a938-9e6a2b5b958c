import { z } from 'zod';

// Define transaction status enum for validation
const invoiceStatusEnum = z.enum([
  'for_review',
  'approved'
]).optional();

// Define payment terms enum for validation
const paymentTermsEnum = z.enum([
  'due_on_receipt',
  'net_15',
  'net_30',
  'net_45',
  'net_60',
  'custom'
]).optional();

// Custom Zod transform for currency amounts - rounds to 2 decimal places
const currencyAmount = z.number()
  .transform(val => Math.round(val * 100) / 100)
  .refine(val => val >= 0, { message: 'Amount must be non-negative' });

// Helper function to validate date format (YYYY-MM-DD)
const isValidDateFormat = (dateString: string): boolean => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;

  const date = new Date(dateString);
  const timestamp = date.getTime();

  if (isNaN(timestamp)) return false;

  return date.toISOString().slice(0, 10) === dateString;
};

// Enhanced accounting line schema for workflow input
const workflowAccountingLineSchema = z.object({
  // Required fields
  accountName: z.string().describe('Account name to search for (e.g., "Sales Revenue", "Accounts Receivable")'),
  description: z.string().describe('Line item description'),
  debitAmount: currencyAmount.default(0).describe('Debit amount (automatically rounded to 2 decimal places)'),
  creditAmount: currencyAmount.default(0).describe('Credit amount (automatically rounded to 2 decimal places)'),
  
  // Optional classification
  className: z.string().optional().describe('Class name for tracking business segments'),
  projectName: z.string().optional().describe('Project name for tracking project-based work'),

  // Optional product/service fields for revenue/expense lines
  productServiceName: z.string().optional().describe('Product/Service name (for revenue/expense lines)'),
  quantity: z.number().positive().optional().describe('Quantity (if product/service line)'),
  unitPrice: currencyAmount.optional().describe('Unit price (automatically rounded to 2 decimal places)'),
  discountPercentage: z.number().min(0).max(100).default(0).describe('Discount percentage'),
  discountAmount: currencyAmount.default(0).describe('Discount amount (automatically rounded to 2 decimal places)'),
  taxRateName: z.string().optional().describe('Tax rate name'),
  taxRate: z.number().min(0).max(100).default(0).describe('Tax rate percentage (0-100)'),
});

// Main workflow input schema
export const WorkflowInputSchema = z.object({
  // Organization and user context
  organizationId: z.string().uuid().describe('Organization ID for the invoice'),
  userId: z.string().uuid().describe('User ID for created_by and updated_by fields'),
  
  // Invoice details
  invoiceDate: z.string()
    .refine(val => isValidDateFormat(val), {
      message: 'Invoice date must be in YYYY-MM-DD format',
    })
    .describe('Date of the invoice (YYYY-MM-DD)'),
  referenceNumber: z.string().optional().describe('Reference or invoice number'),
  description: z.string()
    .min(3, { message: 'Description must be at least 3 characters' })
    .describe('Description of the invoice'),
  
  // Customer information
  customerName: z.string().describe('Customer name to search for'),
  
  // Invoice terms
  dueDate: z.string().optional()
    .refine(val => val === undefined || isValidDateFormat(val), {
      message: 'Due date must be in YYYY-MM-DD format',
    })
    .describe('Due date for the invoice (YYYY-MM-DD). If not provided, defaults to 30 days from invoice date.'),
  status: invoiceStatusEnum.default('approved').describe('Invoice status (for_review or approved)'),
  paymentTerms: paymentTermsEnum.describe('Payment terms for the invoice'),
  
  // Financial totals
  subtotalAmount: currencyAmount.optional()
    .describe('Subtotal amount before tax (automatically rounded to 2 decimal places)'),
  taxAmount: currencyAmount.optional()
    .describe('Tax amount (automatically rounded to 2 decimal places)'),
  totalAmount: currencyAmount
    .describe('Total invoice amount (automatically rounded to 2 decimal places)'),
  
  // Tax account information
  taxAccountName: z.string().optional()
    .describe('Tax account name (required if there are taxes on the invoice)'),

  // Accounting lines with names instead of IDs
  lines: z.array(workflowAccountingLineSchema)
    .min(2, { message: 'At least 2 line items are required for double-entry accounting' })
    .describe('Accounting line items with names that will be resolved to IDs'),
    
  // Workflow options
  skipDuplicateCheck: z.boolean().optional().default(false).describe('Skip checking for duplicate invoices'),
  createMissingData: z.boolean().optional().default(false).describe('Automatically create missing master data'),
  maxRetries: z.number().optional().default(1).describe('Maximum number of retries for database operations'),
});

// Export types
export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;
export type WorkflowAccountingLine = z.infer<typeof workflowAccountingLineSchema>;
