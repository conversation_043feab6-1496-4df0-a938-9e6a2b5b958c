import { z } from 'zod';

// Customer schema
export const CustomerSchema = z.object({
  customer_id: z.string().uuid(),
  customer_name: z.string(),
  email: z.string().nullable(),
  phone: z.string().nullable(),
  is_active: z.boolean(),
});

// Account schema
export const AccountSchema = z.object({
  account_id: z.string().uuid(),
  account_name: z.string(),
  account_code: z.number(),
  account_type: z.string(),
  account_type_detail: z.string(),
  description: z.string().nullable(),
  is_active: z.boolean(),
});

// Product/Service schema
export const ProductServiceSchema = z.object({
  product_service_id: z.string().uuid(),
  product_service_name: z.string(),
  product_service_type: z.enum(['product', 'service']),
  sale_price: z.number().nullable(),
  purchase_price: z.number().nullable(),
  is_active: z.boolean(),
});

// Tax Rate schema
export const TaxRateSchema = z.object({
  tax_rate_id: z.string().uuid(),
  tax_rate_name: z.string(),
  tax_rate_percentage: z.number(),
  tax_rate_type: z.enum(['sales', 'purchase', 'both']),
  is_active: z.boolean(),
});

// Class schema
export const ClassSchema = z.object({
  class_id: z.string().uuid(),
  class_name: z.string(),
  description: z.string().nullable(),
  is_active: z.boolean(),
});

// Project schema
export const ProjectSchema = z.object({
  project_id: z.string().uuid(),
  project_name: z.string(),
  description: z.string().nullable(),
  is_active: z.boolean(),
});

// Missing data item schema
export const MissingDataItemSchema = z.object({
  type: z.enum(['customer', 'account', 'product_service', 'tax_rate', 'class', 'project']),
  name: z.string(),
  context: z.string().describe('Additional context about where this item is needed'),
  suggestions: z.array(z.string()).describe('Suggested actions or similar existing items'),
});

// Step 1: Fetch Master Data Output Schema
export const FetchMasterDataOutputSchema = z.object({
  customer: CustomerSchema.nullable(),
  accounts: z.array(AccountSchema),
  productsServices: z.array(ProductServiceSchema),
  taxRates: z.array(TaxRateSchema),
  classes: z.array(ClassSchema),
  projects: z.array(ProjectSchema),
  searchSummary: z.string().describe('Summary of what was found and what was searched for'),
});

// Step 2: Validate Data Output Schema
export const ValidateDataOutputSchema = z.object({
  isValid: z.boolean().describe('Whether all required data was found'),
  missingData: z.array(MissingDataItemSchema).describe('List of missing master data items'),
  resolvedLines: z.array(z.object({
    originalLine: z.any(),
    accountId: z.string().uuid().nullable(),
    productServiceId: z.string().uuid().nullable(),
    taxRateId: z.string().uuid().nullable(),
    classId: z.string().uuid().nullable(),
    projectId: z.string().uuid().nullable(),
    issues: z.array(z.string()).describe('Issues found with this line'),
  })).describe('Lines with resolved IDs and any issues'),
  validationSummary: z.string().describe('Summary of validation results'),
});

// Step 3: Handle Missing Data Output Schema
export const HandleMissingDataOutputSchema = z.object({
  action: z.enum(['proceed', 'suspend', 'create_and_proceed']),
  message: z.string().describe('Message about the action taken'),
  createdItems: z.array(z.object({
    type: z.string(),
    name: z.string(),
    id: z.string().uuid(),
  })).optional().describe('Items that were automatically created'),
  suspendReason: z.string().optional().describe('Reason for suspension if action is suspend'),
});

// Sales invoice schema for final output
const SalesInvoiceSchema = z.object({
  transaction_id: z.string().uuid(),
  transaction_type: z.string(),
  transaction_date: z.string(),
  reference_number: z.string().nullable(),
  document_number: z.string().nullable(),
  description: z.string(),
  customer_id: z.string().uuid(),
  due_date: z.string().nullable(),
  status: z.string(),
  payment_terms: z.string().nullable(),
  subtotal_amount: z.number().nullable(),
  tax_amount: z.number().nullable(),
  total_amount: z.number(),
  organization_id: z.string().uuid(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Step 4: Record Invoice Output Schema
export const RecordInvoiceOutputSchema = z.object({
  invoice: SalesInvoiceSchema,
  invoiceLines: z.array(z.object({
    transaction_line_id: z.string().uuid(),
    account_id: z.string().uuid(),
    description: z.string(),
    debit_amount: z.number(),
    credit_amount: z.number(),
    class_id: z.string().uuid().nullable(),
    project_id: z.string().uuid().nullable(),
  })),
  message: z.string().describe('Success message with invoice details'),
  processingTimeMs: z.number().describe('Time taken to process the invoice'),
});

// Main workflow output schema
export const WorkflowOutputSchema = z.object({
  success: z.boolean().describe('Whether the workflow completed successfully'),
  action: z.enum(['completed', 'suspended', 'failed']),
  message: z.string().describe('Summary message about the workflow result'),
  
  // Data from each step (optional based on how far the workflow progressed)
  masterData: FetchMasterDataOutputSchema.optional(),
  validation: ValidateDataOutputSchema.optional(),
  missingDataHandling: HandleMissingDataOutputSchema.optional(),
  invoice: RecordInvoiceOutputSchema.optional(),
  
  // Workflow metadata
  workflowDetails: z.object({
    runId: z.string().describe('Unique workflow run ID'),
    status: z.string().describe('Final workflow status'),
    stepsExecuted: z.array(z.string()).describe('List of steps that were executed'),
    totalProcessingTimeMs: z.number().describe('Total time taken for the workflow'),
  }),
});

// Export types
export type WorkflowOutput = z.infer<typeof WorkflowOutputSchema>;
export type FetchMasterDataOutput = z.infer<typeof FetchMasterDataOutputSchema>;
export type ValidateDataOutput = z.infer<typeof ValidateDataOutputSchema>;
export type HandleMissingDataOutput = z.infer<typeof HandleMissingDataOutputSchema>;
export type RecordInvoiceOutput = z.infer<typeof RecordInvoiceOutputSchema>;
export type MissingDataItem = z.infer<typeof MissingDataItemSchema>;
