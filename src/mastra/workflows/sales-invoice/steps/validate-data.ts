import { createStep } from '@mastra/core/workflows';
import {
  FetchMasterDataOutputSchema,
  ValidateDataOutputSchema,
  type FetchMasterDataOutput,
  type WorkflowInput,
  type MissingDataItem,
} from '../schemas/index.js';
import { z } from 'zod';

// Input schema for validation step (combines workflow input + master data)
const ValidateDataInputSchema = z.object({
  // Original workflow input
  workflowInput: z.any(),
  // Master data from previous step
  masterData: FetchMasterDataOutputSchema,
});

/**
 * Step 2: Validate Data Completeness
 * This step validates that all required master data was found and identifies
 * any missing items that need to be created or resolved before proceeding.
 */
export const validateDataStep = createStep({
  id: 'validateData',
  description: 'Validate that all required master data exists and resolve names to IDs',
  inputSchema: ValidateDataInputSchema,
  outputSchema: ValidateDataOutputSchema,
  execute: async ({ inputData }) => {
    const { workflowInput, masterData } = inputData;
    const { lines, customerName, taxAccountName } = workflowInput as WorkflowInput;

    try {
      const missingData: MissingDataItem[] = [];
      const resolvedLines = [];

      // 1. Validate customer
      if (!masterData.customer) {
        missingData.push({
          type: 'customer',
          name: customerName,
          context: 'Required for sales invoice',
          suggestions: [
            `Create customer "${customerName}"`,
            'Check if customer name is spelled correctly',
            'Search for similar customer names',
          ],
        });
      }

      // 2. Validate tax account if specified
      let taxAccountId: string | null = null;
      if (taxAccountName) {
        const taxAccount = masterData.accounts.find(acc => 
          acc.account_name.toLowerCase().includes(taxAccountName.toLowerCase())
        );
        if (!taxAccount) {
          missingData.push({
            type: 'account',
            name: taxAccountName,
            context: 'Required for tax calculations',
            suggestions: [
              `Create tax account "${taxAccountName}"`,
              'Check if account name is spelled correctly',
              'Use existing tax liability account',
            ],
          });
        } else {
          taxAccountId = taxAccount.account_id;
        }
      }

      // 3. Validate each line
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const lineIssues: string[] = [];
        
        // Find account
        const account = masterData.accounts.find(acc => 
          acc.account_name.toLowerCase().includes(line.accountName.toLowerCase())
        );
        
        if (!account) {
          missingData.push({
            type: 'account',
            name: line.accountName,
            context: `Required for line ${i + 1}: ${line.description}`,
            suggestions: [
              `Create account "${line.accountName}"`,
              'Check if account name is spelled correctly',
              'Use similar existing account',
            ],
          });
          lineIssues.push(`Account "${line.accountName}" not found`);
        }

        // Find product/service if specified
        let productService = null;
        if (line.productServiceName) {
          productService = masterData.productsServices.find(ps => 
            ps.product_service_name.toLowerCase().includes(line.productServiceName!.toLowerCase())
          );
          
          if (!productService) {
            missingData.push({
              type: 'product_service',
              name: line.productServiceName,
              context: `Required for line ${i + 1}: ${line.description}`,
              suggestions: [
                `Create product/service "${line.productServiceName}"`,
                'Check if name is spelled correctly',
                'Use similar existing product/service',
              ],
            });
            lineIssues.push(`Product/Service "${line.productServiceName}" not found`);
          }
        }

        // Find tax rate if specified
        let taxRate = null;
        if (line.taxRateName) {
          taxRate = masterData.taxRates.find(tr => 
            tr.tax_rate_name.toLowerCase().includes(line.taxRateName!.toLowerCase())
          );
          
          if (!taxRate) {
            missingData.push({
              type: 'tax_rate',
              name: line.taxRateName,
              context: `Required for line ${i + 1}: ${line.description}`,
              suggestions: [
                `Create tax rate "${line.taxRateName}"`,
                'Check if name is spelled correctly',
                'Use similar existing tax rate',
              ],
            });
            lineIssues.push(`Tax rate "${line.taxRateName}" not found`);
          }
        }

        // Find class if specified
        let classItem = null;
        if (line.className) {
          classItem = masterData.classes.find(cls => 
            cls.class_name.toLowerCase().includes(line.className!.toLowerCase())
          );
          
          if (!classItem) {
            missingData.push({
              type: 'class',
              name: line.className,
              context: `Required for line ${i + 1}: ${line.description}`,
              suggestions: [
                `Create class "${line.className}"`,
                'Check if name is spelled correctly',
                'Use similar existing class',
              ],
            });
            lineIssues.push(`Class "${line.className}" not found`);
          }
        }

        // Find project if specified
        let project = null;
        if (line.projectName) {
          project = masterData.projects.find(proj => 
            proj.project_name.toLowerCase().includes(line.projectName!.toLowerCase())
          );
          
          if (!project) {
            missingData.push({
              type: 'project',
              name: line.projectName,
              context: `Required for line ${i + 1}: ${line.description}`,
              suggestions: [
                `Create project "${line.projectName}"`,
                'Check if name is spelled correctly',
                'Use similar existing project',
              ],
            });
            lineIssues.push(`Project "${line.projectName}" not found`);
          }
        }

        // Validate line amounts
        if (line.debitAmount > 0 && line.creditAmount > 0) {
          lineIssues.push('Line cannot have both debit and credit amounts');
        }
        if (line.debitAmount === 0 && line.creditAmount === 0) {
          lineIssues.push('Line must have either debit or credit amount');
        }

        resolvedLines.push({
          originalLine: line,
          accountId: account?.account_id || null,
          productServiceId: productService?.product_service_id || null,
          taxRateId: taxRate?.tax_rate_id || null,
          classId: classItem?.class_id || null,
          projectId: project?.project_id || null,
          issues: lineIssues,
        });
      }

      // 4. Validate double-entry balance
      const totalDebits = lines.reduce((sum, line) => sum + line.debitAmount, 0);
      const totalCredits = lines.reduce((sum, line) => sum + line.creditAmount, 0);
      
      if (Math.abs(totalDebits - totalCredits) > 0.01) {
        missingData.push({
          type: 'account',
          name: 'Balancing Entry',
          context: `Debits (${totalDebits}) do not equal credits (${totalCredits})`,
          suggestions: [
            'Adjust line amounts to balance',
            'Add balancing entry',
            'Check calculation errors',
          ],
        });
      }

      const isValid = missingData.length === 0;
      
      const validationSummary = isValid 
        ? `Validation successful: All required data found for ${lines.length} lines`
        : `Validation failed: ${missingData.length} missing items found`;

      return {
        isValid,
        missingData,
        resolvedLines,
        validationSummary,
      };

    } catch (error) {
      throw new Error(`Data validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});
