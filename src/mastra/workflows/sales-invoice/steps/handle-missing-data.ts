import { createStep } from '@mastra/core/workflows';
import { supabase } from '../../../../utils/supabase.js';
import {
  ValidateDataOutputSchema,
  HandleMissingDataOutputSchema,
  type ValidateDataOutput,
  type WorkflowInput,
} from '../schemas/index.js';
import { z } from 'zod';

// Input schema for missing data handling step
const HandleMissingDataInputSchema = z.object({
  workflowInput: z.any(),
  validation: ValidateDataOutputSchema,
});

/**
 * Step 3: Handle Missing Data
 * This step handles missing master data by either:
 * 1. Suspending the workflow to request user input
 * 2. Automatically creating missing data (if enabled)
 * 3. Proceeding if no data is missing
 */
export const handleMissingDataStep = createStep({
  id: 'handleMissingData',
  description: 'Handle missing master data by creating items or suspending for user input',
  inputSchema: HandleMissingDataInputSchema,
  outputSchema: HandleMissingDataOutputSchema,
  execute: async ({ inputData, suspend }) => {
    const { workflowInput, validation } = inputData;
    const { createMissingData, organizationId, userId } = workflowInput as WorkflowInput;

    try {
      // If validation passed, proceed
      if (validation.isValid) {
        return {
          action: 'proceed' as const,
          message: 'All required data found, proceeding with invoice creation',
        };
      }

      // If auto-creation is disabled, suspend for user input
      if (!createMissingData) {
        const missingItems = validation.missingData.map(item =>
          `${item.type}: "${item.name}" (${item.context})`
        ).join('\n');

        await suspend({
          reason: 'missing_master_data',
          missingData: validation.missingData,
          message: `Missing master data items:\n${missingItems}\n\nPlease create these items or enable auto-creation.`,
        });

        return {
          action: 'suspend' as const,
          message: `Workflow suspended due to ${validation.missingData.length} missing master data items`,
          suspendReason: 'Missing master data requires user attention',
        };
      }

      // Auto-create missing data
      const createdItems = [];
      
      for (const missingItem of validation.missingData) {
        try {
          let createdId: string | null = null;

          switch (missingItem.type) {
            case 'customer':
              const customerResult = await supabase
                .from('customers')
                .insert({
                  customer_name: missingItem.name,
                  organization_id: organizationId,
                  is_active: true,
                  created_by: userId,
                  updated_by: userId,
                })
                .select('customer_id')
                .single();

              if (!customerResult.error) {
                createdId = customerResult.data.customer_id;
              }
              break;

            case 'account':
              // For accounts, we need to determine the type based on context
              let accountType = 'asset';
              let accountTypeDetail = 'other_current_assets';
              
              if (missingItem.name.toLowerCase().includes('revenue') || 
                  missingItem.name.toLowerCase().includes('sales') ||
                  missingItem.name.toLowerCase().includes('income')) {
                accountType = 'revenue';
                accountTypeDetail = 'revenue';
              } else if (missingItem.name.toLowerCase().includes('tax') ||
                        missingItem.name.toLowerCase().includes('payable')) {
                accountType = 'liability';
                accountTypeDetail = 'other_current_liabilities';
              } else if (missingItem.name.toLowerCase().includes('receivable')) {
                accountType = 'asset';
                accountTypeDetail = 'accounts_receivable';
              }

              // Generate account code
              const { data: codeResult } = await supabase
                .rpc('generate_account_code', {
                  p_account_type: accountType,
                  p_organization_id: organizationId
                });

              const accountCode = codeResult || 1000;

              const accountResult = await supabase
                .from('accounts')
                .insert({
                  account_name: missingItem.name,
                  account_code: accountCode,
                  account_type: accountType,
                  account_type_detail: accountTypeDetail,
                  organization_id: organizationId,
                  is_active: true,
                  created_by: userId,
                  updated_by: userId,
                })
                .select('account_id')
                .single();

              if (!accountResult.error) {
                createdId = accountResult.data.account_id;
              }
              break;

            case 'product_service':
              const productServiceResult = await supabase
                .from('products_and_services')
                .insert({
                  product_service_name: missingItem.name,
                  product_service_type: 'service', // Default to service
                  organization_id: organizationId,
                  is_active: true,
                  created_by: userId,
                  updated_by: userId,
                })
                .select('product_service_id')
                .single();

              if (!productServiceResult.error) {
                createdId = productServiceResult.data.product_service_id;
              }
              break;

            case 'tax_rate':
              const taxRateResult = await supabase
                .from('tax_rates')
                .insert({
                  tax_rate_name: missingItem.name,
                  tax_rate_percentage: 0, // Default to 0%, user can update later
                  tax_rate_type: 'sales',
                  organization_id: organizationId,
                  is_active: true,
                  created_by: userId,
                  updated_by: userId,
                })
                .select('tax_rate_id')
                .single();

              if (!taxRateResult.error) {
                createdId = taxRateResult.data.tax_rate_id;
              }
              break;

            case 'class':
              const classResult = await supabase
                .from('classes')
                .insert({
                  class_name: missingItem.name,
                  organization_id: organizationId,
                  is_active: true,
                  created_by: userId,
                  updated_by: userId,
                })
                .select('class_id')
                .single();

              if (!classResult.error) {
                createdId = classResult.data.class_id;
              }
              break;

            case 'project':
              const projectResult = await supabase
                .from('projects')
                .insert({
                  project_name: missingItem.name,
                  organization_id: organizationId,
                  is_active: true,
                  created_by: userId,
                  updated_by: userId,
                })
                .select('project_id')
                .single();

              if (!projectResult.error) {
                createdId = projectResult.data.project_id;
              }
              break;
          }

          if (createdId) {
            createdItems.push({
              type: missingItem.type,
              name: missingItem.name,
              id: createdId,
            });
          }
        } catch (error) {
          console.warn(`Failed to create ${missingItem.type} "${missingItem.name}":`, error);
        }
      }

      return {
        action: 'create_and_proceed' as const,
        message: `Created ${createdItems.length} missing items and proceeding with invoice creation`,
        createdItems,
      };

    } catch (error) {
      throw new Error(`Missing data handling failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});
