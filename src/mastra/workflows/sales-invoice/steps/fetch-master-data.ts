import { createStep } from '@mastra/core/workflows';
import { supabase } from '../../../../utils/supabase.js';
import {
  WorkflowInputSchema,
  FetchMasterDataOutputSchema,
  type WorkflowInput,
} from '../schemas/index.js';

/**
 * Step 1: Fetch Master Data in Parallel
 * This step fetches all required master data (customer, accounts, products, tax rates, etc.)
 * in parallel to minimize processing time and gather all necessary IDs for the invoice.
 */
export const fetchMasterDataStep = createStep({
  id: 'fetchMasterData',
  description: 'Fetch all required master data for the sales invoice in parallel',
  inputSchema: WorkflowInputSchema,
  outputSchema: FetchMasterDataOutputSchema,
  execute: async ({ inputData }) => {
    const {
      organizationId,
      customerName,
      taxAccountName,
      lines,
    } = inputData;

    try {
      // Extract unique names from lines for parallel fetching
      const accountNames = new Set<string>();
      const productServiceNames = new Set<string>();
      const taxRateNames = new Set<string>();
      const classNames = new Set<string>();
      const projectNames = new Set<string>();

      // Add tax account name if provided
      if (taxAccountName) {
        accountNames.add(taxAccountName);
      }

      // Extract names from lines
      lines.forEach(line => {
        accountNames.add(line.accountName);
        if (line.productServiceName) {
          productServiceNames.add(line.productServiceName);
        }
        if (line.taxRateName) {
          taxRateNames.add(line.taxRateName);
        }
        if (line.className) {
          classNames.add(line.className);
        }
        if (line.projectName) {
          projectNames.add(line.projectName);
        }
      });

      // Create parallel fetch promises
      const fetchPromises = [];

      // 1. Fetch customer
      const customerPromise = supabase
        .from('customers')
        .select('*')
        .ilike('customer_name', `%${customerName}%`)
        .eq('organization_id', organizationId)
        .eq('is_active', true)
        .order('customer_name')
        .limit(5);

      fetchPromises.push(customerPromise);

      // 2. Fetch accounts
      const accountsPromise = accountNames.size > 0 
        ? supabase
            .from('accounts')
            .select('*')
            .or(Array.from(accountNames).map(name => `account_name.ilike.%${name}%`).join(','))
            .eq('organization_id', organizationId)
            .eq('is_active', true)
            .order('account_name')
        : Promise.resolve({ data: [], error: null });

      fetchPromises.push(accountsPromise);

      // 3. Fetch products/services
      const productsServicesPromise = productServiceNames.size > 0
        ? supabase
            .from('products_and_services')
            .select('*')
            .or(Array.from(productServiceNames).map(name => `product_service_name.ilike.%${name}%`).join(','))
            .eq('organization_id', organizationId)
            .eq('is_active', true)
            .order('product_service_name')
        : Promise.resolve({ data: [], error: null });

      fetchPromises.push(productsServicesPromise);

      // 4. Fetch tax rates
      const taxRatesPromise = taxRateNames.size > 0
        ? supabase
            .from('tax_rates')
            .select('*')
            .or(Array.from(taxRateNames).map(name => `tax_rate_name.ilike.%${name}%`).join(','))
            .eq('organization_id', organizationId)
            .eq('is_active', true)
            .order('tax_rate_name')
        : Promise.resolve({ data: [], error: null });

      fetchPromises.push(taxRatesPromise);

      // 5. Fetch classes
      const classesPromise = classNames.size > 0
        ? supabase
            .from('classes')
            .select('*')
            .or(Array.from(classNames).map(name => `class_name.ilike.%${name}%`).join(','))
            .eq('organization_id', organizationId)
            .eq('is_active', true)
            .order('class_name')
        : Promise.resolve({ data: [], error: null });

      fetchPromises.push(classesPromise);

      // 6. Fetch projects
      const projectsPromise = projectNames.size > 0
        ? supabase
            .from('projects')
            .select('*')
            .or(Array.from(projectNames).map(name => `project_name.ilike.%${name}%`).join(','))
            .eq('organization_id', organizationId)
            .eq('is_active', true)
            .order('project_name')
        : Promise.resolve({ data: [], error: null });

      fetchPromises.push(projectsPromise);

      // Execute all queries in parallel
      const [
        customerResult,
        accountsResult,
        productsServicesResult,
        taxRatesResult,
        classesResult,
        projectsResult,
      ] = await Promise.all(fetchPromises);

      // Check for errors
      const errors = [];
      if (customerResult.error) errors.push(`Customer fetch error: ${customerResult.error.message}`);
      if (accountsResult.error) errors.push(`Accounts fetch error: ${accountsResult.error.message}`);
      if (productsServicesResult.error) errors.push(`Products/Services fetch error: ${productsServicesResult.error.message}`);
      if (taxRatesResult.error) errors.push(`Tax rates fetch error: ${taxRatesResult.error.message}`);
      if (classesResult.error) errors.push(`Classes fetch error: ${classesResult.error.message}`);
      if (projectsResult.error) errors.push(`Projects fetch error: ${projectsResult.error.message}`);

      if (errors.length > 0) {
        throw new Error(`Master data fetch failed: ${errors.join('; ')}`);
      }

      // Find the best customer match (exact match preferred)
      const customers = customerResult.data || [];
      const exactCustomerMatch = customers.find(c => 
        c.customer_name.toLowerCase() === customerName.toLowerCase()
      );
      const customer = exactCustomerMatch || customers[0] || null;

      // Prepare search summary
      const searchSummary = [
        `Customer search for "${customerName}": ${customer ? `Found "${customer.customer_name}"` : 'Not found'}`,
        `Accounts search: Found ${accountsResult.data?.length || 0} accounts`,
        `Products/Services search: Found ${productsServicesResult.data?.length || 0} items`,
        `Tax rates search: Found ${taxRatesResult.data?.length || 0} rates`,
        `Classes search: Found ${classesResult.data?.length || 0} classes`,
        `Projects search: Found ${projectsResult.data?.length || 0} projects`,
      ].join('\n');

      return {
        customer,
        accounts: accountsResult.data || [],
        productsServices: productsServicesResult.data || [],
        taxRates: taxRatesResult.data || [],
        classes: classesResult.data || [],
        projects: projectsResult.data || [],
        searchSummary,
      };

    } catch (error) {
      throw new Error(`Master data fetch failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});
