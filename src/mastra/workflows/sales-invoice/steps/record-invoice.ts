import { createStep } from '@mastra/core/workflows';
import { supabase } from '../../../../utils/supabase.js';
import {
  ValidateDataOutputSchema,
  HandleMissingDataOutputSchema,
  FetchMasterDataOutputSchema,
  RecordInvoiceOutputSchema,
  type WorkflowInput,
} from '../schemas/index.js';
import { z } from 'zod';

// Input schema for record invoice step
const RecordInvoiceInputSchema = z.object({
  workflowInput: z.any(),
  masterData: FetchMasterDataOutputSchema,
  validation: ValidateDataOutputSchema,
  missingDataHandling: HandleMissingDataOutputSchema,
});

// Helper function to calculate due date
const calculateDueDate = (invoiceDate: string, daysToAdd: number): string => {
  const date = new Date(invoiceDate);
  date.setDate(date.getDate() + daysToAdd);
  return date.toISOString().slice(0, 10);
};

/**
 * Step 4: Record Sales Invoice
 * This step creates the actual sales invoice transaction and lines
 * using the validated and resolved master data.
 */
export const recordInvoiceStep = createStep({
  id: 'recordInvoice',
  description: 'Create the sales invoice transaction with resolved master data',
  inputSchema: RecordInvoiceInputSchema,
  outputSchema: RecordInvoiceOutputSchema,
  execute: async ({ inputData }) => {
    const { workflowInput, masterData, validation, missingDataHandling } = inputData;
    const {
      organizationId,
      userId,
      invoiceDate,
      referenceNumber,
      description,
      dueDate,
      status,
      paymentTerms,
      subtotalAmount,
      taxAmount,
      totalAmount,
      maxRetries,
    } = workflowInput as WorkflowInput;

    const startTime = Date.now();

    try {
      // Get customer ID (should exist after missing data handling)
      let customerId = masterData.customer?.customer_id;
      
      // If customer was created in missing data handling, find it
      if (!customerId && missingDataHandling.createdItems) {
        const createdCustomer = missingDataHandling.createdItems.find(item => item.type === 'customer');
        if (createdCustomer) {
          customerId = createdCustomer.id;
        }
      }

      if (!customerId) {
        throw new Error('Customer ID not found after data resolution');
      }

      // Auto-calculate due date if not provided (default to 30 days)
      const finalDueDate = dueDate || calculateDueDate(invoiceDate, 30);

      // Generate document number
      const { data: documentNumberResult, error: documentNumberError } = await supabase
        .rpc('generate_document_number', {
          p_transaction_type: 'sales_invoice',
          p_organization_id: organizationId
        });

      if (documentNumberError) {
        throw new Error(`Failed to generate invoice number: ${documentNumberError.message}`);
      }

      const documentNumber = documentNumberResult;

      // Prepare transaction data
      const invoiceData = {
        transaction_type: 'sales_invoice',
        transaction_date: invoiceDate,
        reference_number: referenceNumber,
        document_number: documentNumber,
        description: description,
        customer_id: customerId,
        due_date: finalDueDate,
        status: status || 'approved',
        payment_terms: paymentTerms,
        subtotal_amount: subtotalAmount || totalAmount,
        tax_amount: taxAmount || 0,
        total_amount: totalAmount,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      };

      // Create invoice with retry logic
      let invoice: any;
      let invoiceError;
      let retryCount = 0;
      const maxRetryAttempts = maxRetries || 1;

      while (retryCount < maxRetryAttempts) {
        const result = await supabase
          .from('transactions')
          .insert(invoiceData)
          .select()
          .single();

        invoice = result.data;
        invoiceError = result.error;

        if (!invoiceError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        if (retryCount < maxRetryAttempts) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (invoiceError) {
        throw new Error(`Failed to create sales invoice after ${retryCount} attempts: ${invoiceError.message}`);
      }

      // Prepare transaction lines using resolved IDs
      const transactionLines = validation.resolvedLines.map((resolvedLine, index) => {
        const line = resolvedLine.originalLine;
        
        // Get IDs from resolved data or created items
        let accountId = resolvedLine.accountId;
        let productServiceId = resolvedLine.productServiceId;
        let taxRateId = resolvedLine.taxRateId;
        let classId = resolvedLine.classId;
        let projectId = resolvedLine.projectId;

        // Check if any IDs were created in missing data handling
        if (missingDataHandling.createdItems) {
          if (!accountId && line.accountName) {
            const createdAccount = missingDataHandling.createdItems.find(
              item => item.type === 'account' && item.name === line.accountName
            );
            if (createdAccount) accountId = createdAccount.id;
          }

          if (!productServiceId && line.productServiceName) {
            const createdProduct = missingDataHandling.createdItems.find(
              item => item.type === 'product_service' && item.name === line.productServiceName
            );
            if (createdProduct) productServiceId = createdProduct.id;
          }

          if (!taxRateId && line.taxRateName) {
            const createdTaxRate = missingDataHandling.createdItems.find(
              item => item.type === 'tax_rate' && item.name === line.taxRateName
            );
            if (createdTaxRate) taxRateId = createdTaxRate.id;
          }

          if (!classId && line.className) {
            const createdClass = missingDataHandling.createdItems.find(
              item => item.type === 'class' && item.name === line.className
            );
            if (createdClass) classId = createdClass.id;
          }

          if (!projectId && line.projectName) {
            const createdProject = missingDataHandling.createdItems.find(
              item => item.type === 'project' && item.name === line.projectName
            );
            if (createdProject) projectId = createdProject.id;
          }
        }

        if (!accountId) {
          throw new Error(`Account ID not resolved for line ${index + 1}: ${line.accountName}`);
        }

        const lineData: any = {
          transaction_id: invoice.transaction_id,
          account_id: accountId,
          description: line.description,
          debit_amount: line.debitAmount,
          credit_amount: line.creditAmount,
          class_id: classId || null,
          project_id: projectId || null,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        };

        // Add product/service fields if provided
        if (productServiceId) {
          lineData.product_service_id = productServiceId;
          lineData.quantity = line.quantity || null;
          lineData.unit_price = line.unitPrice || null;
          lineData.discount_percentage = line.discountPercentage || 0;
          lineData.discount_amount = line.discountAmount || 0;
          lineData.tax_rate_id = taxRateId || null;
          lineData.tax_rate = line.taxRate || 0;
        }

        return lineData;
      });

      // Insert transaction lines with retry logic
      retryCount = 0;
      let linesError;

      while (retryCount < maxRetryAttempts) {
        const result = await supabase
          .from('transaction_lines')
          .insert(transactionLines);

        linesError = result.error;

        if (!linesError) {
          break; // Success, exit the retry loop
        }

        retryCount++;
        if (retryCount < maxRetryAttempts) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
        }
      }

      if (linesError) {
        throw new Error(`Failed to create invoice lines after ${retryCount} attempts: ${linesError.message}`);
      }

      const customerName = masterData.customer?.customer_name || 'Unknown Customer';
      const linesWithProducts = validation.resolvedLines.filter(line => line.productServiceId);

      return {
        invoice,
        invoiceLines: validation.resolvedLines.map((resolvedLine, index) => ({
          transaction_line_id: `line_${index + 1}`, // This would be the actual ID from the database
          account_id: resolvedLine.accountId!,
          description: resolvedLine.originalLine.description,
          debit_amount: resolvedLine.originalLine.debitAmount,
          credit_amount: resolvedLine.originalLine.creditAmount,
          class_id: resolvedLine.classId || null,
          project_id: resolvedLine.projectId || null,
        })),
        message: `Successfully created sales invoice ${documentNumber} for ${customerName} with ${validation.resolvedLines.length} line items${linesWithProducts.length > 0 ? ` (${linesWithProducts.length} with product/service details)` : ''}.`,
        processingTimeMs: Date.now() - startTime,
      };

    } catch (error) {
      throw new Error(`Invoice recording failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});
