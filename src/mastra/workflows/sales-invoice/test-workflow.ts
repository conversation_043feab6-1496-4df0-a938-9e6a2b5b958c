/**
 * Test script for Sales Invoice Workflow
 * 
 * This script demonstrates how to use the sales invoice workflow
 * and can be used for testing during development.
 */

import { salesInvoiceWorkflow } from './index.js';

// Example test data
const testData = {
  organizationId: '123e4567-e89b-12d3-a456-************',
  userId: '123e4567-e89b-12d3-a456-************',
  invoiceDate: '2024-01-15',
  referenceNumber: 'INV-2024-001',
  description: 'Consulting services for January 2024',
  customerName: 'ABC Corporation',
  dueDate: '2024-02-14',
  status: 'approved' as const,
  paymentTerms: 'net_30' as const,
  subtotalAmount: 1000.00,
  taxAmount: 100.00,
  totalAmount: 1100.00,
  taxAccountName: 'Sales Tax Payable',
  lines: [
    {
      accountName: 'Sales Revenue',
      description: 'Consulting Revenue',
      debitAmount: 0,
      creditAmount: 1000.00,
      productServiceName: 'Consulting Services',
      quantity: 10,
      unitPrice: 100.00,
      taxRateName: 'Standard Sales Tax',
      taxRate: 10,
    },
    {
      accountName: 'Sales Tax Payable',
      description: 'Sales Tax',
      debitAmount: 0,
      creditAmount: 100.00,
    },
    {
      accountName: 'Accounts Receivable',
      description: 'Accounts Receivable - ABC Corp',
      debitAmount: 1100.00,
      creditAmount: 0,
    }
  ],
  skipDuplicateCheck: true,
  createMissingData: true,
  maxRetries: 1,
};

/**
 * Test function to run the workflow
 */
export async function testSalesInvoiceWorkflow() {
  try {
    console.log('🚀 Starting Sales Invoice Workflow Test...');
    console.log('📋 Test Data:', JSON.stringify(testData, null, 2));

    // Create a workflow run
    const run = salesInvoiceWorkflow.createRun();
    
    // Start the workflow
    const result = await run.start({
      inputData: testData,
    });

    console.log('✅ Workflow Result:', JSON.stringify(result, null, 2));

    // Check the result status
    if (result.status === 'completed') {
      console.log('🎉 Workflow completed successfully!');
      
      // Extract results from each step
      const fetchResult = result.steps?.fetchMasterData;
      const validateResult = result.steps?.validateData;
      const missingDataResult = result.steps?.handleMissingData;
      const invoiceResult = result.steps?.recordInvoice;

      console.log('📊 Step Results:');
      console.log('  - Fetch Master Data:', fetchResult?.status);
      console.log('  - Validate Data:', validateResult?.status);
      console.log('  - Handle Missing Data:', missingDataResult?.status);
      console.log('  - Record Invoice:', invoiceResult?.status);

      if (invoiceResult?.status === 'success') {
        console.log('💰 Invoice Created:', invoiceResult.output?.invoice?.document_number);
      }
    } else if (result.status === 'suspended') {
      console.log('⏸️ Workflow suspended - missing data needs attention');
      console.log('📝 Missing Data:', result.steps?.handleMissingData?.output?.missingData);
    } else {
      console.log('❌ Workflow failed:', result.error?.message);
    }

    return result;

  } catch (error) {
    console.error('💥 Test failed:', error);
    throw error;
  }
}

/**
 * Test function for workflow tool
 */
export async function testSalesInvoiceWorkflowTool(mastra: any, runtimeContext: any) {
  try {
    console.log('🔧 Testing Sales Invoice Workflow Tool...');

    const { salesInvoiceWorkflowTool } = await import('../../tools/sales_invoice_workflow_tool.js');

    const result = await salesInvoiceWorkflowTool.execute({
      context: {
        invoiceDate: testData.invoiceDate,
        referenceNumber: testData.referenceNumber,
        description: testData.description,
        customerName: testData.customerName,
        dueDate: testData.dueDate,
        status: testData.status,
        paymentTerms: testData.paymentTerms,
        subtotalAmount: testData.subtotalAmount,
        taxAmount: testData.taxAmount,
        totalAmount: testData.totalAmount,
        taxAccountName: testData.taxAccountName,
        lines: testData.lines,
        skipDuplicateCheck: testData.skipDuplicateCheck,
        createMissingData: testData.createMissingData,
        maxRetries: testData.maxRetries,
      },
      mastra,
      runtimeContext,
    });

    console.log('🔧 Tool Result:', JSON.stringify(result, null, 2));
    return result;

  } catch (error) {
    console.error('💥 Tool test failed:', error);
    throw error;
  }
}

// Export test data for external use
export { testData };

// If running directly, execute the test
if (import.meta.url === `file://${process.argv[1]}`) {
  testSalesInvoiceWorkflow()
    .then(() => console.log('✅ Test completed'))
    .catch(() => console.log('❌ Test failed'));
}
