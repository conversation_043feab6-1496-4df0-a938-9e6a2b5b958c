# Sales Invoice Workflow

This workflow provides intelligent sales invoice creation with comprehensive master data validation and handling.

## Overview

The Sales Invoice Workflow addresses the current problem of multiple sequential tool calls by batching all data operations into a single workflow execution. This results in:

- **Reduced token costs** by eliminating multiple LLM calls
- **Better error handling** through comprehensive validation
- **Improved user experience** with parallel data fetching
- **Data integrity** through master data validation
- **Graceful handling** of missing data

## Workflow Steps

### 1. Fetch Master Data (Parallel)
- Fetches customer data by name
- Fetches all required accounts by name
- Fetches products/services by name (if applicable)
- Fetches tax rates by name (if applicable)
- Fetches classes and projects by name (if applicable)

### 2. Validate Data Completeness
- Validates that all required master data was found
- Resolves names to IDs for all entities
- Identifies missing master data items
- Validates double-entry accounting balance

### 3. Handle Missing Data
- **If `createMissingData` is false**: Suspends workflow for user input
- **If `createMissingData` is true**: Automatically creates missing items
- **If no data missing**: Proceeds to invoice creation

### 4. Record Sales Invoice
- Creates the sales invoice transaction
- Creates all accounting lines with resolved IDs
- Handles product/service details and tax calculations
- Returns complete invoice data

## Usage

### Using the Workflow Tool

```typescript
import { salesInvoiceWorkflowTool } from './tools/sales_invoice_workflow_tool.js';

const result = await salesInvoiceWorkflowTool.execute({
  context: {
    invoiceDate: '2024-01-15',
    customerName: 'ABC Corporation',
    description: 'Consulting services for January',
    totalAmount: 1000.00,
    lines: [
      {
        accountName: 'Sales Revenue',
        description: 'Consulting Revenue',
        creditAmount: 1000.00,
        productServiceName: 'Consulting Services',
        quantity: 10,
        unitPrice: 100.00
      },
      {
        accountName: 'Accounts Receivable',
        description: 'Accounts Receivable - ABC Corp',
        debitAmount: 1000.00
      }
    ],
    createMissingData: true, // Auto-create missing master data
  },
  mastra,
  runtimeContext
});
```

### Direct Workflow Usage

```typescript
import { salesInvoiceWorkflow } from './workflows/sales-invoice/index.js';

const run = salesInvoiceWorkflow.createRun();
const result = await run.start({
  inputData: {
    organizationId: 'org-uuid',
    userId: 'user-uuid',
    invoiceDate: '2024-01-15',
    customerName: 'ABC Corporation',
    // ... other fields
  }
});
```

## Input Schema

The workflow accepts the following input:

- **organizationId**: Organization UUID
- **userId**: User UUID for audit trail
- **invoiceDate**: Invoice date (YYYY-MM-DD)
- **customerName**: Customer name to search for
- **description**: Invoice description
- **totalAmount**: Total invoice amount
- **lines**: Array of accounting lines with account names
- **createMissingData**: Whether to auto-create missing data (default: false)

## Output Schema

The workflow returns:

- **success**: Whether workflow completed successfully
- **action**: 'completed', 'suspended', or 'failed'
- **message**: Summary message
- **invoice**: Created invoice data (if completed)
- **invoiceLines**: Created invoice lines (if completed)
- **missingData**: Missing data items (if suspended)
- **workflowDetails**: Execution metadata

## Error Handling

The workflow handles errors gracefully:

1. **Missing Master Data**: Suspends workflow or auto-creates items
2. **Validation Errors**: Clear error messages with suggestions
3. **Database Errors**: Retry logic with exponential backoff
4. **Balance Errors**: Validates double-entry accounting rules

## Benefits Over Individual Tools

| Aspect | Individual Tools | Workflow |
|--------|------------------|----------|
| Token Usage | High (multiple LLM calls) | Low (single execution) |
| Data Validation | Manual per tool | Comprehensive upfront |
| Error Handling | Per-tool basis | Centralized and graceful |
| Missing Data | Manual creation | Auto-creation or suspension |
| Performance | Sequential execution | Parallel data fetching |
| User Experience | Multiple interactions | Single interaction |

## Future Enhancements

- Add conditional execution for suspended workflows
- Implement workflow resume functionality
- Add support for complex tax calculations
- Extend to other transaction types (receipts, bills, etc.)
- Add workflow-level caching for repeated data
