import { PostgresStore, PgVector } from '@mastra/pg';
import { config } from 'dotenv';

// Load .env file with override option to override system environment variables
config({ override: true });

// Get Supabase PostgreSQL connection string from environment variables
// Use the pooler connection string which works with IPv4
const postgresConnectionString = process.env.SUPABASE_POOLER_URL || '';

if (!postgresConnectionString) {
  throw new Error('Missing SUPABASE_POOLER_URL. Please add it to your .env file.');
}

// Create a single PostgresStore instance to be used across the application
export const mastraStorage = new PostgresStore({
  connectionString: postgresConnectionString,
  schemaName: 'mastra', // Isolate Mastra tables in their own schema
});

// Vector storage for semantic search with OpenAI embeddings
export const vectorStorage = new PgVector({
  connectionString: postgresConnectionString,
  schemaName: 'mastra', // Keep vectors in the same schema for consistency
});
