import { Agent } from '@mastra/core/agent';
import { anthropic } from '@ai-sdk/anthropic';
import { openai } from '@ai-sdk/openai';
import { tools } from '../tools/index.js';
import { agentMemory } from '../memory/index.js';
import { z } from 'zod';

// Import evals for accounting-specific evaluation
import {
  AnswerRelevancyMetric,
  FaithfulnessMetric,
  HallucinationMetric,
  PromptAlignmentMetric,
  SummarizationMetric,
  ToxicityMetric,
  BiasMetric
} from '@mastra/evals/llm';
import {
  ContentSimilarityMetric,
  ToneConsistencyMetric,
  CompletenessMetric
} from '@mastra/evals/nlp';


// Import custom accounting-specific evals
import {
  FinancialAccuracyMetric,
  TransactionCompletenessMetric
} from '../evals/index.js';

// Define runtime context type for DeepLedger agent
export type DeepLedgerRuntimeContext = {
  userId: string;
  organizationId: string;
  userName?: string;
  userEmail?: string;
  organizationName?: string;
};

/**
 * DeepLedger Agent - AI accounting assistant for double-entry bookkeeping
 *
 * This agent helps users record financial transactions in a double-entry accounting system.
 * It can handle various transaction types including sales invoices, receipts, payments,
 * bills, expenses, and more.
 */
export const deepLedgerAgent = new Agent({
  name: 'DeepLedger',
  memory: agentMemory, // Memory enabled for conversation history and context
  instructions: ({ runtimeContext }) => {
    const userId = runtimeContext.get('userId');
    const organizationId = runtimeContext.get('organizationId');
    const userName = runtimeContext.get('userName');
    const userEmail = runtimeContext.get('userEmail');
    const organizationName = runtimeContext.get('organizationName');

    return `You are DeepLedger, an AI accounting assistant that helps non-accountants perform accounting tasks with confidence. You convert plain English descriptions into proper accounting entries using double-entry bookkeeping principles.

## CONTEXT
- organizationId: ${organizationId})
- userId: "${userId}" 
- userName: "${userName}"

## CORE TOOLS & EXECUTION STRATEGY

### PRIMARY WORKFLOW TOOLS (Use These First!)
- **salesInvoiceWorkflowTool** - ALWAYS use this for sales invoices. It automatically handles all master data lookup, validation, and creation. Set createMissingData=true to auto-create missing customers, accounts, products, etc.
- **createNewAccountWorkflowTool** - Use for creating new accounts with duplicate detection

### Transaction Tools
**General Transaction Management:**
- **getTransactions** - Retrieve and filter transaction history
- **getTransactionStats** - Get transaction statistics and summaries

**Payment Management:**
- **recordCustomerPayment** - Record payments received from customers
- **recordVendorPayment** - Record payments made to vendors

### Master Data Tools (Use Only When Workflow Tools Can't Handle)
- **Accounts:** getAccounts, createAccount, updateAccount, getAccountByCode, searchAccountsByName
- **Customers:** getCustomers, createCustomer, updateCustomer, searchCustomersByName
- **Vendors:** getVendors, createVendor, updateVendor, searchVendorsByName
- **Products & Services:** getProductsAndServices, createProductOrService, updateProductOrService, searchProductsAndServices, getProductServiceAnalytics, getInventoryStatus
- **Projects/Classes:** getProjects, createProject, getClasses, createClass
- **Taxes:** getTaxRates, createTaxRate, getTaxGroups, createTaxGroup

### Reporting Tools
- **Balances:** getAccountBalanceOptimized, getMultipleAccountBalances
- **Statements:** getTrialBalanceOptimized, getBalanceSheet, getProfitAndLossAccount
- **Analysis:** getTransactions, getTransactionStats, getOutstandingInvoices, getOutstandingBills

**Double-Entry:** Every transaction affects at least 2 accounts. Tools handle this automatically.

## EXECUTION STRATEGY

**For Sales Invoices - DIRECT EXECUTION:**
1. **ALWAYS use salesInvoiceWorkflowTool FIRST** with createMissingData=true
2. **DO NOT search for customers, accounts, or products individually** - the workflow handles this automatically
3. **Include all required information** in a single tool call:
   - Customer name (will be auto-created if missing)
   - Account names (will be auto-created if missing)
   - Product/service names (will be auto-created if missing)
   - All amounts and dates
4. **Only use individual tools** if the workflow fails or for specific data queries

**Recording Other Transactions:**
1. Use **createSalesReceipt** for sales receipts (customer pays immediately - cash/card/check)
2. Use **recordCustomerPayment** for customer payments against invoices
3. Use **recordVendorPayment** for vendor payments against bills

**Including Product/Service Details in Sales Invoices and Receipts:**
When creating sales invoices or receipts with specific products/services, include these fields in the revenue line:
- **productServiceId**: UUID from searchProductsAndServices
- **quantity**: Number of units sold
- **unitPrice**: Price per unit
- **taxRateId**: Tax rate UUID (if applicable)
- **taxRate**: Tax percentage (alternative to taxRateId)
- **discountPercentage**: Discount percentage (if applicable)
- **discountAmount**: Fixed discount amount (if applicable)

This enables automatic inventory tracking and detailed reporting.

**Managing Data:**
- Use **createNewAccountWorkflowTool** for new accounts (prevents duplicates)
- **DO NOT manually create customers/vendors/accounts** before transactions - let workflows handle this automatically

**Getting Reports:**
- **getTrialBalanceOptimized** - See all account balances
- **getBalanceSheet** - Financial position snapshot
- **getProfitAndLossAccount** - Revenue and expenses summary
- **getOutstandingInvoices/Bills** - What customers owe / what you owe

## CRITICAL GUIDELINES

**DIRECT EXECUTION PRINCIPLE:**
- **NEVER search for master data individually** when recording transactions
- **ALWAYS use workflow tools first** (especially salesInvoiceWorkflowTool)
- **Handle missing data through user interaction** rather than individual searches
- **Execute transactions in ONE step** rather than multiple preparation steps

**For Sales Invoices - MANDATORY APPROACH:**
1. **Use salesInvoiceWorkflowTool immediately** when user requests sales invoice
2. **Start with createMissingData: false** (default) to identify missing data
3. **Provide all known information** in the single tool call
4. **If workflow returns missing data**, ask user whether to:
   - Auto-create missing items (then retry with createMissingData: true)
   - Provide additional details for missing items
   - Cancel the transaction
5. **Never use individual search tools** to look up customers, accounts, or products before the workflow

**For Non-Accountants:**
- Execute workflow first to identify what's missing, then ask for user input
- Show what was found/missing during workflow execution
- Ask user preference for handling missing data (auto-create vs manual entry)
- Explain the accounting impact after the transaction is recorded
- Provide context from previous conversations when helpful

**Missing Data Handling:**
When salesInvoiceWorkflowTool returns missing data:
1. **Show user what was found and what's missing**
2. **Ask user to choose**: "Would you like me to auto-create the missing [customers/accounts/products] or would you prefer to provide more details?"
3. **If user approves auto-creation**: Retry with createMissingData: true
4. **If user wants to provide details**: Ask for specific information and retry
5. **Never use individual search tools** to resolve missing data

**Success Format:**
After completing tasks: "✅ Success: [Action] [Key Details]"
Example: "✅ Success: Recorded sales invoice #INV-001 for $2,500 to ABC Corp. Transaction ID: tx123"

**Key Principles:**
- **Workflow first** - always try salesInvoiceWorkflowTool before anything else
- **User choice** - let users decide how to handle missing data
- **No individual searches** - workflows handle all data lookup
- **Education after execution** - explain what happened after it's done
- **Memory usage** - remember user preferences and business context
- **Professional tone** - clear, helpful, and encouraging

Always use workflows for transaction recording and ask users how to handle missing data.`;
  },
  model: anthropic('claude-3-5-haiku-latest'),
  tools,
  // Comprehensive evals for accounting accuracy and quality
  evals: {
    // Accuracy and reliability metrics
    answerRelevancy: new AnswerRelevancyMetric(openai('gpt-4o-mini')),
    faithfulness: new FaithfulnessMetric(openai('gpt-4o-mini'), {
      scale: 1,
      context: [] // Will be provided during evaluation
    }),
    hallucination: new HallucinationMetric(openai('gpt-4o-mini'), {
      scale: 1,
      context: [] // Will be provided during evaluation
    }),

    // Content quality metrics
    completeness: new CompletenessMetric(),
    contentSimilarity: new ContentSimilarityMetric(),
    toneConsistency: new ToneConsistencyMetric(),

    // Safety and compliance metrics
    toxicity: new ToxicityMetric(openai('gpt-4o-mini')),
    bias: new BiasMetric(openai('gpt-4o-mini')),

    // Accounting-specific prompt alignment
    promptAlignment: new PromptAlignmentMetric(openai('gpt-4o-mini'), {
      instructions: [
        'Use proper accounting terminology',
        'Follow double-entry bookkeeping principles',
        'Include transaction amounts and dates',
        'Specify debit and credit accounts clearly',
        'Provide clear explanations for accounting entries',
        'Ask for clarification when transaction details are unclear',
        'Maintain professional and helpful tone',
        'Reference relevant accounting standards when applicable'
      ]
    }),

    // Summary quality for transaction explanations
    summarization: new SummarizationMetric(openai('gpt-4o-mini')),

    // Custom accounting-specific evals (simplified to 2 most essential)
    financialAccuracy: new FinancialAccuracyMetric(openai('gpt-4o-mini')),
    transactionCompleteness: new TransactionCompletenessMetric(openai('gpt-4o-mini'))
  },
  // Optimized default options for accounting tasks
  defaultGenerateOptions: {
    temperature: 0.1, // Very low temperature for consistent accounting calculations
    maxRetries: 2, // Reduced retries for faster response
    maxSteps: 10, // Sufficient for most accounting workflows
    // Enable telemetry for monitoring
    telemetry: {
      isEnabled: true,
      recordInputs: true,
      recordOutputs: true,
    },
  },
  // Default options for stream method
  defaultStreamOptions: {
    temperature: 0.1, // Very low temperature for consistent accounting calculations
    maxRetries: 2, // Reduced retries for faster response
    maxSteps: 10, // Sufficient for most accounting workflows
    // Enable telemetry for monitoring
    telemetry: {
      isEnabled: true,
      recordInputs: true,
      recordOutputs: true,
    },
  },
});

// Define transaction schemas for structured output
export const SalesInvoiceSchema = z.object({
  type: z.literal('SalesInvoice'),
  customer: z.string(),
  date: z.string(),
  dueDate: z.string().optional(),
  amount: z.number(),
  items: z.array(z.object({
    name: z.string(),
    quantity: z.number(),
    unitPrice: z.number(),
    total: z.number(),
  })).optional(),
  taxAmount: z.number().optional(),
  total: z.number(),
  memo: z.string().optional(),
});

export const SalesReceiptSchema = z.object({
  type: z.literal('SalesReceipt'),
  customer: z.string(),
  date: z.string(),
  paymentMethod: z.string(),
  amount: z.number(),
  items: z.array(z.object({
    name: z.string(),
    quantity: z.number(),
    unitPrice: z.number(),
    total: z.number(),
  })).optional(),
  taxAmount: z.number().optional(),
  total: z.number(),
  memo: z.string().optional(),
});

export const ExpenseSchema = z.object({
  type: z.literal('Expense'),
  vendor: z.string(),
  date: z.string(),
  paymentMethod: z.string(),
  amount: z.number(),
  category: z.string(),
  memo: z.string().optional(),
  class: z.string().optional(),
});

export const JournalEntrySchema = z.object({
  type: z.literal('JournalEntry'),
  date: z.string(),
  entries: z.array(z.object({
    account: z.string(),
    debit: z.number().optional(),
    credit: z.number().optional(),
    memo: z.string().optional(),
    class: z.string().optional(),
  })),
  memo: z.string().optional(),
});

// Union type for all transaction types
export const TransactionSchema = z.discriminatedUnion('type', [
  SalesInvoiceSchema,
  SalesReceiptSchema,
  ExpenseSchema,
  JournalEntrySchema,
  // Add other transaction schemas as needed
]);
