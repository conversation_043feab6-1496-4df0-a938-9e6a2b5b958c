
import { <PERSON><PERSON> } from '@mastra/core';
import { ConsoleLogger } from '@mastra/core/logger';
import { agents } from './agents/index.js';
import { workflows } from './workflows/index.js';
import { mastraStorage } from './storage/index.js';
import { config } from 'dotenv';

// Load environment variables
config({ override: true });

export const mastra = new Mastra({
  agents,
  workflows: workflows,
  storage: mastraStorage,
  logger: new ConsoleLogger({
    name: 'DeepLedger',
    level: (process.env.MASTRA_LOG_LEVEL as any) || 'info',
  }),
  // Enhanced telemetry configuration for comprehensive tracing
  telemetry: {
    serviceName: 'deepledger-ai',
    enabled: process.env.MASTRA_TELEMETRY_ENABLED !== 'false',
    sampling: {
      type: 'ratio',
      probability: 0.1, // Sample 10% of traces in production
    },
    export: {
      type: 'console', // Start with console, can be changed to OTLP later
    },
  },
  // Runtime context will be provided by the <PERSON><PERSON> playground or client requests
  server: {
    port: parseInt(process.env.PORT || '4111'),
    timeout: 60000, // Increased timeout for complex accounting operations
    cors: {
      origin: '*',
      allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowHeaders: ['Content-Type', 'Authorization', 'x-mastra-client-type'],
      exposeHeaders: ['Content-Length', 'X-Requested-With'],
      credentials: false,
    },
  },
});