// Base metric class for custom evals
abstract class Metric {
  abstract measure(input: string, output: string): Promise<{ score: number; info: any }>;
}
import { openai } from '@ai-sdk/openai';
import { generateText } from 'ai';

/**
 * Utility function to safely parse JSON from LLM responses
 * Handles cases where the response might contain extra text or formatting
 */
function safeJsonParse(text: string): any {
  try {
    // First try direct parsing
    return JSON.parse(text);
  } catch (error) {
    try {
      // Try to extract <PERSON><PERSON><PERSON> from text that might have extra content
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // Try to find JSO<PERSON> between code blocks
      const codeBlockMatch = text.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (codeBlockMatch) {
        return JSON.parse(codeBlockMatch[1]);
      }

      throw new Error('No valid JSON found in response');
    } catch (innerError) {
      console.error('Failed to parse <PERSON>SO<PERSON> from LLM response:', text);
      throw new Error(`JSON parsing failed: ${innerError instanceof Error ? innerError.message : 'Unknown error'}`);
    }
  }
}

/**
 * Validates that the parsed evaluation has the expected structure
 */
function validateEvaluation(evaluation: any): void {
  if (!evaluation || typeof evaluation !== 'object') {
    throw new Error('Evaluation must be an object');
  }

  if (typeof evaluation.score !== 'number' || evaluation.score < 0 || evaluation.score > 1) {
    throw new Error('Evaluation must have a score between 0 and 1');
  }

  if (!evaluation.reason || typeof evaluation.reason !== 'string') {
    throw new Error('Evaluation must have a reason field with string value');
  }
}

/**
 * Custom eval for financial accuracy in accounting transactions
 * This metric evaluates whether the agent correctly applies double-entry bookkeeping principles
 */
export class FinancialAccuracyMetric extends Metric {
  private model: any;

  constructor(model = openai('gpt-4o-mini')) {
    super();
    this.model = model;
  }

  async measure(input: string, output: string): Promise<{ score: number; info: any }> {
    try {
      const prompt = `You are an expert accounting auditor. Evaluate the following accounting response for financial accuracy and adherence to double-entry bookkeeping principles.

Input Query: ${input}

Agent Response: ${output}

Evaluate the response based on these criteria:
1. Double-entry principle: Are debits and credits balanced?
2. Account classification: Are accounts properly categorized (assets, liabilities, equity, revenue, expenses)?
3. Transaction logic: Does the transaction make accounting sense?
4. Completeness: Are all necessary details included (amounts, dates, accounts)?
5. Accuracy: Are calculations correct?

Provide a score from 0 to 1 where:
- 1.0 = Perfect financial accuracy, follows all accounting principles
- 0.8-0.9 = Good accuracy with minor issues
- 0.6-0.7 = Acceptable but with some accounting errors
- 0.4-0.5 = Poor accuracy with significant errors
- 0.0-0.3 = Incorrect or dangerous financial advice

IMPORTANT: Respond with ONLY a valid JSON object, no additional text or explanation. The JSON must be properly formatted and parseable.

{
  "score": <number between 0 and 1>,
  "reason": "<detailed explanation of the evaluation including any issues found and strengths identified>"
}`;

      const result = await generateText({
        model: this.model,
        prompt,
        temperature: 0.1,
      });

      const evaluation = safeJsonParse(result.text);
      validateEvaluation(evaluation);

      return {
        score: evaluation.score,
        info: {
          reason: evaluation.reason
        }
      };
    } catch (error) {
      console.error('Error in FinancialAccuracyMetric:', error);
      return {
        score: 0,
        info: {
          reason: 'Failed to evaluate financial accuracy due to an error'
        }
      };
    }
  }
}

/**
 * Custom eval for transaction completeness
 * Evaluates whether all required information for a financial transaction is present
 */
export class TransactionCompletenessMetric extends Metric {
  private model: any;

  constructor(model = openai('gpt-4o-mini')) {
    super();
    this.model = model;
  }

  async measure(input: string, output: string): Promise<{ score: number; info: any }> {
    try {
      const prompt = `Evaluate the completeness of this accounting transaction response.

Input Query: ${input}

Agent Response: ${output}

Check for the presence of these essential transaction elements:
1. Transaction date
2. Transaction amount(s)
3. Account names or types involved
4. Transaction description/memo
5. Debit and credit entries (if applicable)
6. Reference numbers (if applicable)
7. Party involved (customer, vendor, etc.)

Score from 0 to 1 based on completeness:
- 1.0 = All essential elements present and clearly stated
- 0.8-0.9 = Most elements present, minor omissions
- 0.6-0.7 = Some key elements present but missing important details
- 0.4-0.5 = Few elements present, significant gaps
- 0.0-0.3 = Most essential elements missing

IMPORTANT: Respond with ONLY a valid JSON object, no additional text or explanation. The JSON must be properly formatted and parseable.

{
  "score": <number between 0 and 1>,
  "reason": "<detailed explanation of the score including which elements are present and which are missing>"
}`;

      const result = await generateText({
        model: this.model,
        prompt,
        temperature: 0.1,
      });

      const evaluation = safeJsonParse(result.text);
      validateEvaluation(evaluation);

      return {
        score: evaluation.score,
        info: {
          reason: evaluation.reason
        }
      };
    } catch (error) {
      console.error('Error in TransactionCompletenessMetric:', error);
      return {
        score: 0,
        info: {
          reason: 'Failed to evaluate transaction completeness due to an error'
        }
      };
    }
  }
}