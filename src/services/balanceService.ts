import { supabase } from '../utils/supabase.js';

export interface AccountBalance {
  account_id: string;
  code: number;
  name: string;
  account_type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  account_type_detail: string;
  balance: number;
  total_debits: number;
  total_credits: number;
  transaction_count: number;
  last_transaction_date: string | null;
  as_of_date: string;
}

export interface TrialBalanceEntry {
  account_id: string;
  code: number;
  name: string;
  type: string;
  debit_balance: number;
  credit_balance: number;
  net_balance: number;
}

export interface BalanceSheetData {
  assets: AccountBalance[];
  liabilities: AccountBalance[];
  equity: AccountBalance[];
  total_assets: number;
  total_liabilities: number;
  total_equity: number;
  as_of_date: string;
}

/**
 * Real-Time Balance Service
 * Provides high-performance balance calculations using real-time views for always-current data.
 * Perfect for AI agents that need immediate, accurate financial information.
 */
export class BalanceService {

  /**
   * Get account balance using optimized database function
   */
  static async getAccountBalance(
    accountId: string,
    organizationId: string,
    asOfDate?: string
  ): Promise<AccountBalance> {
    const dateParam = asOfDate || new Date().toISOString().split('T')[0];

    try {
      // Use the optimized database function
      const { data, error } = await supabase.rpc('get_account_balance', {
        p_account_id: accountId,
        p_organization_id: organizationId,
        p_as_of_date: dateParam
      });

      if (error) {
        throw new Error(`Failed to get account balance: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error(`Account with ID ${accountId} not found`);
      }

      const result = data[0];

      // Get account details
      const { data: account, error: accountError } = await supabase
        .from('accounts')
        .select('account_code, account_name, account_type, account_type_detail')
        .eq('account_id', accountId)
        .eq('organization_id', organizationId)
        .single();

      if (accountError) {
        throw new Error(`Failed to get account details: ${accountError.message}`);
      }

      return {
        account_id: accountId,
        code: account.account_code,
        name: account.account_name,
        account_type: account.account_type,
        account_type_detail: account.account_type_detail,
        balance: parseFloat(result.balance || '0'),
        total_debits: parseFloat(result.total_debits || '0'),
        total_credits: parseFloat(result.total_credits || '0'),
        transaction_count: parseInt(result.transaction_count || '0'),
        last_transaction_date: result.last_transaction_date,
        as_of_date: dateParam
      };
    } catch (error) {
      console.error('Error in getAccountBalance:', error);
      throw error;
    }
  }

  /**
   * Get trial balance using optimized database function
   */
  static async getTrialBalance(
    organizationId: string,
    asOfDate?: string,
    includeZeroBalances: boolean = false
  ): Promise<{
    entries: TrialBalanceEntry[];
    total_debits: number;
    total_credits: number;
    is_balanced: boolean;
  }> {
    const dateParam = asOfDate || new Date().toISOString().split('T')[0];

    try {
      const { data, error } = await supabase.rpc('get_trial_balance', {
        p_organization_id: organizationId,
        p_as_of_date: dateParam,
        p_include_zero_balances: includeZeroBalances
      });

      if (error) {
        throw new Error(`Failed to get trial balance: ${error.message}`);
      }

      const entries: TrialBalanceEntry[] = data.map((row: any) => ({
        account_id: row.account_id,
        code: row.code,
        name: row.account_name,
        type: row.account_type,
        debit_balance: parseFloat(row.debit_balance || '0'),
        credit_balance: parseFloat(row.credit_balance || '0'),
        net_balance: parseFloat(row.net_balance || '0')
      }));

      const total_debits = entries.reduce((sum, entry) => sum + entry.debit_balance, 0);
      const total_credits = entries.reduce((sum, entry) => sum + entry.credit_balance, 0);
      const is_balanced = Math.abs(total_debits - total_credits) < 0.01;

      return {
        entries,
        total_debits,
        total_credits,
        is_balanced
      };
    } catch (error) {
      console.error('Error in getTrialBalance:', error);
      throw error;
    }
  }

  /**
   * Get balance sheet data using real-time views for always-current data
   */
  static async getBalanceSheet(
    organizationId: string,
    asOfDate?: string
  ): Promise<BalanceSheetData> {
    const dateParam = asOfDate || new Date().toISOString().split('T')[0];

    try {
      // Use real-time view for current balances (always up-to-date)
      const useCurrentView = !asOfDate || asOfDate === new Date().toISOString().split('T')[0];

      let query;
      if (useCurrentView) {
        query = supabase
          .from('account_balances')
          .select('*')
          .eq('organization_id', organizationId)
          .in('account_type', ['asset', 'liability', 'equity']);
      } else {
        // For historical dates, use the function
        const { data, error } = await supabase.rpc('get_trial_balance', {
          p_organization_id: organizationId,
          p_as_of_date: dateParam,
          p_include_zero_balances: false
        });

        if (error) {
          throw new Error(`Failed to get balance sheet data: ${error.message}`);
        }

        const balanceSheetAccounts = data.filter((row: any) =>
          ['asset', 'liability', 'equity'].includes(row.account_type)
        );

        return this.formatBalanceSheetData(balanceSheetAccounts, dateParam);
      }

      const { data: accounts, error } = await query;

      if (error) {
        throw new Error(`Failed to get balance sheet data: ${error.message}`);
      }

      return this.formatBalanceSheetData(accounts, dateParam);
    } catch (error) {
      console.error('Error in getBalanceSheet:', error);
      throw error;
    }
  }

  /**
   * Get multiple account balances efficiently
   */
  static async getMultipleAccountBalances(
    accountIds: string[],
    organizationId: string,
    asOfDate?: string
  ): Promise<AccountBalance[]> {
    const dateParam = asOfDate || new Date().toISOString().split('T')[0];

    try {
      const balances = await Promise.all(
        accountIds.map(accountId =>
          this.getAccountBalance(accountId, organizationId, dateParam)
        )
      );

      return balances;
    } catch (error) {
      console.error('Error in getMultipleAccountBalances:', error);
      throw error;
    }
  }

  /**
   * No refresh needed - real-time views are always current!
   * This method is kept for backward compatibility but does nothing.
   */
  static async refreshBalanceViews(_organizationId?: string): Promise<void> {
    // Real-time views don't need refreshing - they're always current!
    console.log('Real-time views are always current - no refresh needed');
    return Promise.resolve();
  }

  /**
   * Helper method to format balance sheet data
   */
  private static formatBalanceSheetData(accounts: any[], asOfDate: string): BalanceSheetData {
    const assets = accounts.filter(acc => acc.account_type === 'asset');
    const liabilities = accounts.filter(acc => acc.account_type === 'liability');
    const equity = accounts.filter(acc => acc.account_type === 'equity');

    const total_assets = assets.reduce((sum, acc) => sum + parseFloat(acc.balance || '0'), 0);
    const total_liabilities = liabilities.reduce((sum, acc) => sum + parseFloat(acc.balance || '0'), 0);
    const total_equity = equity.reduce((sum, acc) => sum + parseFloat(acc.balance || '0'), 0);

    return {
      assets: assets.map(acc => ({
        account_id: acc.account_id,
        code: acc.account_code,
        name: acc.account_name,
        account_type: acc.account_type,
        account_type_detail: acc.account_type_detail || 'other_assets',
        balance: parseFloat(acc.balance || '0'),
        total_debits: parseFloat(acc.total_debits || '0'),
        total_credits: parseFloat(acc.total_credits || '0'),
        transaction_count: parseInt(acc.transaction_count || '0'),
        last_transaction_date: acc.last_transaction_date,
        as_of_date: asOfDate
      })),
      liabilities: liabilities.map(acc => ({
        account_id: acc.account_id,
        code: acc.account_code,
        name: acc.account_name,
        account_type: acc.account_type,
        account_type_detail: acc.account_type_detail || 'other_current_liabilities',
        balance: parseFloat(acc.balance || '0'),
        total_debits: parseFloat(acc.total_debits || '0'),
        total_credits: parseFloat(acc.total_credits || '0'),
        transaction_count: parseInt(acc.transaction_count || '0'),
        last_transaction_date: acc.last_transaction_date,
        as_of_date: asOfDate
      })),
      equity: equity.map(acc => ({
        account_id: acc.account_id,
        code: acc.account_code,
        name: acc.account_name,
        account_type: acc.account_type,
        account_type_detail: acc.account_type_detail || 'equity',
        balance: parseFloat(acc.balance || '0'),
        total_debits: parseFloat(acc.total_debits || '0'),
        total_credits: parseFloat(acc.total_credits || '0'),
        transaction_count: parseInt(acc.transaction_count || '0'),
        last_transaction_date: acc.last_transaction_date,
        as_of_date: asOfDate
      })),
      total_assets,
      total_liabilities,
      total_equity,
      as_of_date: asOfDate
    };
  }
}
