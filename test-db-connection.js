import { createClient } from '@supabase/supabase-js';
import 'dotenv/config';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    console.log('🔍 Checking database tables...');

    // Query organizations
    const { data: organizations, error: orgError } = await supabase
      .from('organizations')
      .select('*')
      .limit(5);

    if (orgError) {
      console.error('❌ Error querying organizations:', orgError);
      return;
    }

    console.log('📊 Organizations found:', organizations?.length || 0);

    // Query users
    const { data: users, error: userError } = await supabase
      .from('users')
      .select('*')
      .limit(5);

    if (userError) {
      console.error('❌ Error querying users:', userError);
      return;
    }

    console.log('👥 Users found:', users?.length || 0);

    if (users && users.length > 0) {
      console.log('👤 Available users:');
      users.forEach((user, index) => {
        console.log(`  ${index + 1}. ID: ${user.user_id}, Email: ${user.email || 'N/A'}, Name: ${user.full_name || 'N/A'}`);
      });
    }

    if (organizations && organizations.length > 0) {
      console.log('🏢 Available organizations:');
      organizations.forEach((org, index) => {
        console.log(`  ${index + 1}. ID: ${org.organization_id}, Name: ${org.name}, Slug: ${org.slug}`);
      });
    }

    // Test with real IDs if both exist
    if (organizations && organizations.length > 0 && users && users.length > 0) {
      // Use the organization ID from the user's request
      const userOrgId = '8f175142-e4dc-460d-b451-545d5dc36323';
      const testOrgId = organizations.find(org => org.organization_id === userOrgId)?.organization_id || organizations[0].organization_id;
      const testUserId = users[0].user_id;

      // Check which users are associated with this organization
      console.log('\n🔍 Checking users for organization:', testOrgId);
      const { data: orgUsers, error: orgUsersError } = await supabase
        .from('organization_users')
        .select('user_id, role')
        .eq('organization_id', testOrgId);

      if (orgUsersError) {
        console.error('❌ Error querying organization users:', orgUsersError);
      } else {
        console.log('👥 Users in this organization:', orgUsers?.length || 0);
        if (orgUsers && orgUsers.length > 0) {
          orgUsers.forEach((orgUser, index) => {
            console.log(`  ${index + 1}. User ID: ${orgUser.user_id}, Role: ${orgUser.role}`);
          });
          // Use the first user from this organization
          const orgUserId = orgUsers[0].user_id;
          console.log('\n🧪 Testing customer creation...');
          console.log('🏢 Using organization ID:', testOrgId);
          console.log('👤 Using user ID from organization:', orgUserId);

          const testCustomer = {
            customer_name: 'Test Customer API Real IDs',
            email: '<EMAIL>',
            phone: '+1234567890',
            address: '123 Test Street'
          };

          const response = await fetch('http://localhost:4112/api/tools/createCustomer', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              args: testCustomer,
              organizationId: testOrgId,
              userId: orgUserId
            }),
          });

          const result = await response.json();
          console.log('📝 API Response:', result);

          if (result.success) {
            console.log('✅ Customer created successfully!');
          } else {
            console.log('❌ Customer creation failed:', result.error);
          }
        } else {
          console.log('⚠️ No users found in this organization.');
        }
      }
    } else {
      console.log('⚠️ Missing organizations or users. Cannot test API.');
    }

  } catch (error) {
    console.error('❌ Connection test failed:', error);
  }
}

testConnection();
