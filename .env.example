# DeepLedger Mastra Backend Environment Variables

# AI Model API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Supabase Configuration (for authentication and database)
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_POOLER_URL=your_supabase_pooler_connection_string

# Server Configuration
PORT=4111
NODE_ENV=development

# Production Configuration
# Update these for production deployment
# CORS_ORIGINS=https://your-frontend-domain.com,https://www.your-frontend-domain.com
# ALLOWED_ORIGINS=https://your-frontend-domain.com

# Telemetry and Observability Configuration
MASTRA_TELEMETRY_ENABLED=true
MASTRA_LOG_LEVEL=info

# OpenTelemetry Configuration (Optional)
# OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
# OTEL_EXPORTER_OTLP_HEADERS=x-api-key=your-api-key
# OTEL_SERVICE_NAME=deepledger-ai
# OTEL_RESOURCE_ATTRIBUTES=service.version=1.0.0

# Eval Configuration
# Set to false to disable evals in production
MASTRA_EVALS_ENABLED=true

# Runtime Context Configuration
# Runtime context is now provided dynamically through the Mastra playground
# or via API requests. No hardcoded defaults are needed.

# Optional: Additional AI Providers (if needed)
# GOOGLE_API_KEY=your_google_api_key
# GROQ_API_KEY=your_groq_api_key
